#!/usr/bin/env python
# coding: utf-8

# In[ ]:


import sys
import argparse

# Add the scripts directory to the sys.path
sys.path.append("../")

from datetime import datetime, timedelta
import os
from odps_client import logging, get_odps_sql_result_as_df, write_pandas_df_into_odps
from openai_client import call_azure_openai
import pandas as pd
from feishu_client import send_markdown_to_feishu

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

DATA_PATH = f"./qiyu_audio_data"

# 设置命令行参数
parser = argparse.ArgumentParser(description="指定运行日期", allow_abbrev=False)
parser.add_argument("--wav_file", type=str, help="本地文件地址", required=True)
parser.add_argument(
    "--use_q4_model",
    choices=["true", "false"],
    default="true",
    help="是否使用Q4模型（会快一些）",
    required=False,
)

args, unknown = parser.parse_known_args()
use_q4_model = "true" == args.use_q4_model
wav_file = f"{DATA_PATH}/{args.wav_file}"

started_at = datetime.now()


# In[ ]:


import mlx_whisper
import numpy as np


def convert_np_floats(data):
    if isinstance(data, dict):
        # Recursively process dictionaries
        return {key: convert_np_floats(value) for key, value in data.items()}
    elif isinstance(data, list):
        # Recursively process lists
        return [convert_np_floats(item) for item in data]
    elif isinstance(data, np.float64):
        # Convert np.float64 to Python float
        return float(data)
    else:
        # Return the data as is for non-dict and non-float64 values
        return data


LOCAL_MLX_MODEL_PATH = os.getenv(
    "LOCAL_MLX_MODEL_PATH", "/Users/<USER>/Documents/mlx-whisper"
)

LOCAL_MLX_MODEL_Q4_PATH = os.getenv(
    "LOCAL_MLX_MODEL_Q4_PATH", "/Users/<USER>/Documents/mlx-whisper-v3-turbo-q4"
)


def transcribe_with_local_whisper_model(
    audio_file: str = None,
) -> dict:
    logging.info(f"使用本地模型进行transcribe, audio_file:{audio_file}")
    result = mlx_whisper.transcribe(
        audio_file,
        path_or_hf_repo=(
            LOCAL_MLX_MODEL_Q4_PATH if use_q4_model else LOCAL_MLX_MODEL_PATH
        ),
        initial_prompt="鲜沐农场的销售员和客户的电话录音：",
    )
    converted_data = convert_np_floats(result)
    return converted_data


# ## 切分wav文件为左右两个声道

# In[7]:


import os, subprocess


def split_wav_into_left_and_right_channels(file_path: str) -> list:
    try:
        file_base_name = os.path.basename(file_path)
        directory = os.path.dirname(file_path)
        left_path = f"{directory}/left_{file_base_name}"
        right_path = f"{directory}/right_{file_base_name}"

        cmd = f'ffmpeg -loglevel error -y -i "{file_path}" -filter_complex "[0:a]channelsplit=channel_layout=stereo[left][right]" -map "[left]" "{left_path}" -map "[right]" "{right_path}"'

        # Use subprocess.Popen instead of run to better handle output
        process = subprocess.Popen(
            cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
        )

        stdout, stderr = process.communicate()

        if process.returncode != 0:
            logging.error(f"FFmpeg error: {stderr}")
            return None

        logging.info(f"Successfully split {file_path} into left and right channels")
        return [left_path, right_path]

    except Exception as e:
        logging.error(f"Error during audio splitting: {str(e)}")
        return None


USE_LOCAL_WHISPER = os.getenv("USE_LOCAL_WHISPER", "true")
USE_LOCAL_WHISPER = "true" == USE_LOCAL_WHISPER


def call_whisper_with_local_file(
    filename="/Users/<USER>/Downloads/白津源_3d341fed6a4637ad614830dc9d1a6b97.wav",
    is_retrying=False,
):
    if USE_LOCAL_WHISPER:
        # 使用本地模型进行转录
        return transcribe_with_local_whisper_model(filename)


# In[9]:


def merge_left_and_right_channel_segments(left_segments=[], right_segments=[]):
    merge = [None] * (
        len(left_segments) + len(right_segments)
    )  # Initialize the merge list

    l, r = 0, 0
    merge_idx = 0
    last_r_text, last_l_text = "", ""

    for i in range(len(left_segments) + len(right_segments)):
        seg_l = left_segments[l] if l < len(left_segments) else None
        seg_r = right_segments[r] if r < len(right_segments) else None

        if seg_r:
            seg_r["speaker"] = "right"
        if seg_l:
            seg_l["speaker"] = "left"

        if seg_l is None:
            if seg_r["text"] != last_r_text:
                logging.info(f"seg_r: {seg_r['text']}")
                merge[merge_idx] = seg_r
                last_r_text = seg_r["text"]
                merge_idx += 1
            r += 1
            continue

        if seg_r is None:
            if seg_l["text"] != last_l_text:
                logging.info(f"seg_l: {seg_l['text']}")
                merge[merge_idx] = seg_l
                last_l_text = seg_l["text"]
                merge_idx += 1
            l += 1
            continue

        if seg_l["end"] < seg_r["end"]:
            if seg_l["text"] != last_l_text:
                merge[merge_idx] = seg_l
                last_l_text = seg_l["text"]
                merge_idx += 1
            l += 1
        else:
            if seg_r["text"] != last_r_text:
                merge[merge_idx] = seg_r
                last_r_text = seg_r["text"]
                merge_idx += 1
            r += 1

    # Remove None values from the merge list
    return [x for x in merge[:merge_idx] if x is not None]


# In[ ]:


def strip_repeated_text(text: str) -> str:
    import re

    # 使用正则表达式去除重复3次以上的词语
    return re.sub(r"\b(\w+)( \1\b){2,}", r"\1", text)


def split_wav_and_call_whisper(local_wav_file: str) -> (str, dict):
    channels = split_wav_into_left_and_right_channels(local_wav_file)
    segment_result = []
    for file in channels:
        whisper_result = call_whisper_with_local_file(filename=file)
        if whisper_result is not None:
            segments = whisper_result.get("segments", [])
            # Filter out segments with empty text
            segments = [s for s in segments if s.get("text", "").strip()]
            # 去除重复文本
            for segment in segments:
                segment["text"] = strip_repeated_text(segment["text"])
            segment_result.append(segments)
        else:
            segment_result.append([])

    if len(segment_result) != 2 or not all(segment_result):
        return (f"未能解析所有声道的数据: {local_wav_file}", {})

    merge_result = merge_left_and_right_channel_segments(
        segment_result[0], segment_result[1]
    )
    merged_text = "\n".join(
        [
            f"[{round(segment['start'],1)}~{round(segment['end'],1)}] {segment['speaker']}: {segment['text']}"
            for segment in merge_result
            if segment.get("text", "").strip()
        ]
    )
    logging.info(f"local_wav_file: {local_wav_file}, \nmerged_text{merged_text}")
    return (merged_text, merge_result)


logging.info(f"transcribing file:{wav_file} with whisper...")
merged_text, merge_result = split_wav_and_call_whisper(wav_file)

print(merged_text, merge_result)
