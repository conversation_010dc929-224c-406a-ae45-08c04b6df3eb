{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import argparse\n", "\n", "# Add the scripts directory to the sys.path\n", "sys.path.append(\"../\")\n", "\n", "from datetime import datetime, timedelta\n", "import os\n", "from odps_client import logging, get_odps_sql_result_as_df, write_pandas_df_into_odps\n", "from openai_client import call_azure_openai\n", "import pandas as pd\n", "from feishu_client import send_markdown_to_feishu\n", "\n", "logging.basicConfig(\n", "    level=logging.INFO,\n", "    format=\"%(asctime)s - %(levelname)s - %(message)s\",\n", "    datefmt=\"%Y-%m-%d %H:%M:%S\",\n", ")\n", "\n", "# 设置命令行参数\n", "parser = argparse.ArgumentParser(description=\"指定运行日期\", allow_abbrev=False)\n", "parser.add_argument(\n", "    \"--ds_to_run\", type=str, default=\"\", help=\"运行日期，格式为YYYYMMDD\", required=False\n", ")\n", "parser.add_argument(\n", "    \"--reuse_downloaded_files\",\n", "    type=str,\n", "    default=\"false\",\n", "    help=\"是否重复利用已经下载的wav文件（提速），默认false\",\n", "    required=False,\n", ")\n", "parser.add_argument(\n", "    \"--communication_time_lower_limit\",\n", "    type=int,\n", "    default=20,\n", "    help=\"最低沟通时长，低于这个阈值则不需要分析，默认20s\",\n", "    required=False,\n", ")\n", "parser.add_argument(\n", "    \"--use_q4_model\",\n", "    choices=[\"true\", \"false\"],\n", "    default=\"false\",\n", "    help=\"是否使用Q4模型（会快一些）\",\n", "    required=False,\n", ")\n", "parser.add_argument(\n", "    \"--feishu_token\",\n", "    type=str,\n", "    default=\"-\",\n", "    help=\"飞书通知用的token,默认不发送通知\",\n", "    required=False,\n", ")\n", "parser.add_argument(\n", "    \"--skip_whisper\",\n", "    choices=[\"true\", \"false\"],\n", "    default=\"false\",\n", "    help=\"是否跳过whisper解析的部分，用于直接跑AI分析\",\n", "    required=False,\n", ")\n", "args, unknown = parser.parse_known_args()\n", "communication_time_lower_limit = args.communication_time_lower_limit\n", "reuse_downloaded_files = \"true\" == args.reuse_downloaded_files\n", "use_q4_model = \"true\" == args.use_q4_model\n", "feishu_token = args.feishu_token\n", "skip_whisper = \"true\" == args.skip_whisper\n", "\n", "started_at = datetime.now()\n", "# 如果没有指定日期，则使用昨天的日期\n", "if args.ds_to_run:\n", "    ds_to_run = args.ds_to_run\n", "else:\n", "    ds_to_run = (started_at - timedelta(days=1)).strftime(\"%Y%m%d\")\n", "\n", "logging.info(f\"ds_to_run:{ds_to_run}\")\n", "\n", "import shutil\n", "\n", "DATA_PATH = f\"./qiyu_audio_data\"\n", "\n", "\n", "def create_dir_if_not_exist(path):\n", "    # Remove the directory if it exists\n", "    if os.path.exists(path) and not reuse_downloaded_files:\n", "        logging.warning(f\"即将删除以下文件夹的内容:{path}\")\n", "        shutil.rmtree(path)\n", "    # Create the directory\n", "    os.makedirs(path, exist_ok=reuse_downloaded_files)\n", "    return path\n", "\n", "\n", "create_dir_if_not_exist(DATA_PATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import mlx_whisper\n", "import numpy as np\n", "\n", "\n", "def convert_np_floats(data):\n", "    if isinstance(data, dict):\n", "        # Recursively process dictionaries\n", "        return {key: convert_np_floats(value) for key, value in data.items()}\n", "    elif isinstance(data, list):\n", "        # Recursively process lists\n", "        return [convert_np_floats(item) for item in data]\n", "    elif isinstance(data, np.float64):\n", "        # Convert np.float64 to Python float\n", "        return float(data)\n", "    else:\n", "        # Return the data as is for non-dict and non-float64 values\n", "        return data\n", "\n", "\n", "LOCAL_MLX_MODEL_PATH = os.getenv(\n", "    \"LOCAL_MLX_MODEL_PATH\", \"/Users/<USER>/Documents/mlx-whisper\"\n", ")\n", "\n", "LOCAL_MLX_MODEL_Q4_PATH = os.getenv(\n", "    \"LOCAL_MLX_MODEL_Q4_PATH\", \"/Users/<USER>/Documents/mlx-whisper-v3-turbo-q4\"\n", ")\n", "\n", "\n", "def transcribe_with_local_whisper_model(\n", "    audio_file: str = None,\n", ") -> dict:\n", "    logging.info(f\"使用本地模型进行transcribe, audio_file:{audio_file}\")\n", "    result = mlx_whisper.transcribe(\n", "        audio_file,\n", "        path_or_hf_repo=LOCAL_MLX_MODEL_Q4_PATH if use_q4_model else LOCAL_MLX_MODEL_PATH,\n", "        initial_prompt=\"鲜沐农场的销售员和客户的电话录音：\",\n", "    )\n", "    converted_data = convert_np_floats(result)\n", "    return converted_data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 切分wav文件为左右两个声道"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["import os, subprocess\n", "\n", "\n", "def split_wav_into_left_and_right_channels(file_path: str) -> list:\n", "    try:\n", "        file_base_name = os.path.basename(file_path)\n", "        directory = os.path.dirname(file_path)\n", "        left_path = f\"{directory}/left_{file_base_name}\"\n", "        right_path = f\"{directory}/right_{file_base_name}\"\n", "\n", "        cmd = f'ffmpeg -loglevel error -y -i \"{file_path}\" -filter_complex \"[0:a]channelsplit=channel_layout=stereo[left][right]\" -map \"[left]\" \"{left_path}\" -map \"[right]\" \"{right_path}\"'\n", "\n", "        # Use subprocess.Popen instead of run to better handle output\n", "        process = subprocess.Popen(\n", "            cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True\n", "        )\n", "\n", "        stdout, stderr = process.communicate()\n", "\n", "        if process.returncode != 0:\n", "            logging.error(f\"FFmpeg error: {stderr}\")\n", "            return None\n", "\n", "        logging.info(f\"Successfully split {file_path} into left and right channels\")\n", "        return [left_path, right_path]\n", "\n", "    except Exception as e:\n", "        logging.error(f\"Error during audio splitting: {str(e)}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 这是Groq模型"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "USE_LOCAL_WHISPER = os.getenv(\"USE_LOCAL_WHISPER\", \"true\")\n", "USE_LOCAL_WHISPER = \"true\" == USE_LOCAL_WHISPER\n", "\n", "\n", "def call_whisper_with_local_file(\n", "    filename=\"/Users/<USER>/Downloads/白津源_3d341fed6a4637ad614830dc9d1a6b97.wav\",\n", "    is_retrying=False,\n", "):\n", "    if USE_LOCAL_WHISPER:\n", "        # 使用本地模型进行转录\n", "        return transcribe_with_local_whisper_model(filename)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def merge_left_and_right_channel_segments(left_segments=[], right_segments=[]):\n", "    merge = [None] * (\n", "        len(left_segments) + len(right_segments)\n", "    )  # Initialize the merge list\n", "\n", "    l, r = 0, 0\n", "    merge_idx = 0\n", "    last_r_text, last_l_text = \"\", \"\"\n", "\n", "    for i in range(len(left_segments) + len(right_segments)):\n", "        seg_l = left_segments[l] if l < len(left_segments) else None\n", "        seg_r = right_segments[r] if r < len(right_segments) else None\n", "\n", "        if seg_r:\n", "            seg_r[\"speaker\"] = \"right\"\n", "        if seg_l:\n", "            seg_l[\"speaker\"] = \"left\"\n", "\n", "        if seg_l is None:\n", "            if seg_r[\"text\"] != last_r_text:\n", "                logging.info(f\"seg_r: {seg_r['text']}\")\n", "                merge[merge_idx] = seg_r\n", "                last_r_text = seg_r[\"text\"]\n", "                merge_idx += 1\n", "            r += 1\n", "            continue\n", "\n", "        if seg_r is None:\n", "            if seg_l[\"text\"] != last_l_text:\n", "                logging.info(f\"seg_l: {seg_l['text']}\")\n", "                merge[merge_idx] = seg_l\n", "                last_l_text = seg_l[\"text\"]\n", "                merge_idx += 1\n", "            l += 1\n", "            continue\n", "\n", "        if seg_l[\"end\"] < seg_r[\"end\"]:\n", "            if seg_l[\"text\"] != last_l_text:\n", "                merge[merge_idx] = seg_l\n", "                last_l_text = seg_l[\"text\"]\n", "                merge_idx += 1\n", "            l += 1\n", "        else:\n", "            if seg_r[\"text\"] != last_r_text:\n", "                merge[merge_idx] = seg_r\n", "                last_r_text = seg_r[\"text\"]\n", "                merge_idx += 1\n", "            r += 1\n", "\n", "    # Remove None values from the merge list\n", "    return [x for x in merge[:merge_idx] if x is not None]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from feishu_client import send_markdown_to_feishu\n", "\n", "\n", "staffname_list_to_analytics = os.getenv(\n", "    \"STAFF_NAME_LIST\", \"白津源,陈汉文,李梦婷,宋懿航\"\n", ")\n", "staffname_list = \"','\".join(staffname_list_to_analytics.split(\",\"))\n", "rows_count_to_analytics = os.getenv(\"ROWS_TO_ANALYTICS\", \"20000\")\n", "logging.info(\n", "    f\"staffname_list:{staffname_list}, rows_count_to_analytics:{rows_count_to_analytics}\"\n", ")\n", "\n", "recordurl_df = get_odps_sql_result_as_df(\n", "    f\"\"\"\n", "SELECT  *,DATEDIFF(CAST(endtime AS TIMESTAMP),CAST(createtime AS TIMESTAMP),'ss') communication_time_in_seconds,'{ds_to_run}' as ds\n", "FROM    (\n", "            SELECT  JSON_TUPLE(body,\"eventtype\",\"sessionid\",\"direction\",\"createtime\",\"endtime\",\"connectionbeginetime\",\"connectionendtime\",\"from\",\"to\",\"user\",\"category\",\"staffid\",\"staffname\",\"status\",\"visittimes\",\"duration\",\"evaluation\",\"recordurl\",\"overflowFrom\",\"shuntGroupName\",\"ivrPath\",\"mobileArea\",\"waitDuration\",\"ringDuration\",\"sessionIdFrom\",\"firstEndDirection\") AS (\"eventtype\",\"sessionid\",\"direction\",\"createtime\",\"endtime\",\"connectionbeginetime\",\"connectionendtime\",\"from\",\"to\",\"user\",\"category\",\"staffid\",\"staffname\",\"status\",\"visittimes\",\"duration\",\"evaluation\",\"recordurl\",\"overflowFrom\",\"shuntGroupName\",\"ivrPath\",\"mobileArea\",\"waitDuration\",\"ringDuration\",\"sessionIdFrom\",\"firstEndDirection\")\n", "            FROM    summerfarm_tech.ods_qiyu_call_log_di\n", "            WHERE   ds = '{ds_to_run}'\n", "            AND     GET_JSON_OBJECT(body,'$.eventtype') = '5'\n", "        ) \n", "WHERE   recordurl LIKE 'https://hzxmkjyxgs7.%'\n", "and staffname in('{staffname_list}')\n", "limit {rows_count_to_analytics};\"\"\"\n", ")\n", "\n", "length_of_source = len(recordurl_df)\n", "logging.info(f\"数据量大小:{length_of_source}\")\n", "if length_of_source <= 0:\n", "    send_markdown_to_feishu(\n", "        title=f\"暂无电销录音数据,ds={ds_to_run}\",\n", "        markdown_content=\"如题\",\n", "        feishu_token=feishu_token,\n", "    )\n", "    raise Exception(\"summerfarm_tech.ods_qiyu_call_log_di 的数据为空\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 下载wav文件到本地，用于whisper模型识别"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 下载wav文件到本地，用于whisper模型识别\n", "\n", "from concurrent.futures import ThreadPoolExecutor\n", "import concurrent\n", "import requests\n", "import os\n", "from multiprocessing import cpu_count\n", "\n", "\n", "def download_wav_file(url, sessionid, communication_time_in_seconds=0):\n", "    logging.info(\n", "        f\"下载语音文件:{url}, session:{sessionid}, 通话时长:{communication_time_in_seconds}s\"\n", "    )\n", "    file_name = os.path.basename(url)\n", "    response = requests.get(url)\n", "    local_file = f\"{DATA_PATH}/{sessionid}_{file_name}\"\n", "    if os.path.exists(local_file):\n", "        logging.info(f\"The file {local_file} already exists.\")\n", "        return\n", "\n", "    # Check if the request was successful\n", "    if response.status_code == 200:\n", "        # Open a file in binary mode to write the content\n", "        with open(local_file, \"wb\") as f:\n", "            f.write(response.content)\n", "        logging.info(f\"File {file_name} downloaded successfully.\")\n", "    else:\n", "        logging.info(\"Failed to download the file.\")\n", "\n", "\n", "if not skip_whisper:\n", "    with ThreadPoolExecutor(max_workers=cpu_count() * 2) as executor:\n", "        futures = [\n", "            executor.submit(\n", "                download_wav_file,\n", "                row[\"recordurl\"],\n", "                row[\"sessionid\"],\n", "                row[\"communication_time_in_seconds\"],\n", "            )\n", "            for index, row in recordurl_df.iterrows()\n", "        ]\n", "        concurrent.futures.wait(futures)\n", "    recordurl_df[\"local_wav_file\"] = recordurl_df.apply(\n", "        lambda row: f\"{DATA_PATH}/{row['sessionid']}_{os.path.basename(row['recordurl'])}\",\n", "        axis=1,\n", "    )\n", "\n", "    print(\n", "        recordurl_df[[\"local_wav_file\", \"sessionid\", \"recordurl\", \"staffname\"]].head(5)\n", "    )\n", "else:\n", "    logging.warning(\"跳过whisper解析部分，直接到AI分析\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def strip_repeated_text(text: str) -> str:\n", "    import re\n", "\n", "    # 使用正则表达式去除重复3次以上的词语\n", "    return re.sub(r\"\\b(\\w+)( \\1\\b){2,}\", r\"\\1\", text)\n", "\n", "\n", "def split_wav_and_call_whisper(local_wav_file: str) -> (str, dict):\n", "    channels = split_wav_into_left_and_right_channels(local_wav_file)\n", "    segment_result = []\n", "    for file in channels:\n", "        whisper_result = call_whisper_with_local_file(filename=file)\n", "        if whisper_result is not None:\n", "            segments = whisper_result.get(\"segments\", [])\n", "            # Filter out segments with empty text\n", "            segments = [s for s in segments if s.get(\"text\", \"\").strip()]\n", "            # 去除重复文本\n", "            for segment in segments:\n", "                segment[\"text\"] = strip_repeated_text(segment[\"text\"])\n", "            segment_result.append(segments)\n", "        else:\n", "            segment_result.append([])\n", "\n", "    if len(segment_result) != 2 or not all(segment_result):\n", "        return (f\"未能解析所有声道的数据: {local_wav_file}\", {})\n", "\n", "    merge_result = merge_left_and_right_channel_segments(\n", "        segment_result[0], segment_result[1]\n", "    )\n", "    merged_text = \"\\n\".join(\n", "        [\n", "            f\"[{round(segment['start'], 1)}~{round(segment['end'], 1)}] {segment['speaker']}: {segment['text']}\"\n", "            for segment in merge_result\n", "            if segment.get(\"text\", \"\").strip()\n", "        ]\n", "    )\n", "    logging.info(f\"local_wav_file: {local_wav_file}, \\nmerged_text{merged_text}\")\n", "    return (merged_text, merge_result)\n", "\n", "\n", "def transcribe_row(row: pd.Series):\n", "    try:\n", "        if row[\"communication_time_in_seconds\"] <= communication_time_lower_limit:\n", "            logging.warning(\n", "                f\"沟通时长太短了:{row['communication_time_in_seconds']}s, 无需分析\"\n", "            )\n", "            return {\n", "                \"merged_text\": f\"沟通时长太短了:{row['communication_time_in_seconds']}s, 无需分析\",\n", "                \"merge_result\": None,\n", "            }\n", "        logging.info(f\"transcribing file:{row['local_wav_file']} with whisper...\")\n", "        merged_text, merge_result = split_wav_and_call_whisper(row[\"local_wav_file\"])\n", "        return pd.Series({\"merged_text\": merged_text, \"merge_result\": merge_result})\n", "    except Exception as e:\n", "        return pd.Series({\"merged_text\": f\"ERROR:{e}\", \"merge_result\": None})\n", "\n", "\n", "from openai_client import call_azure_openai, parse_json_string\n", "\n", "\n", "def which_is_salesman(merged_text):\n", "    prompt = f\"\"\"以下是销售员和客户之间的对话，请你根据对话的内容，判断left和right分别是销售员还是客户。请以json格式输出，比如{{\"left\": \"[销售员或客户]\", \"right\": \"[销售员或客户]\"}}\n", "    \n", "{merged_text}\n", "    \"\"\"\n", "    response, success = call_azure_openai(text_input=prompt)\n", "    if not success:\n", "        return merged_text\n", "\n", "    salesman_json = parse_json_string(response)\n", "    for key, value in salesman_json.items():\n", "        merged_text = merged_text.replace(key, value)\n", "\n", "    return merged_text\n", "\n", "\n", "if not skip_whisper:\n", "    recordurl_df.sort_values(by=\"communication_time_in_seconds\", ascending=False)\n", "    # 先使用whisper做语音到文本的解析:\n", "    recordurl_df[[\"merged_text\", \"merge_result\"]] = recordurl_df.apply(\n", "        transcribe_row, axis=1\n", "    )\n", "    # 再调用AI区分出left和right哪一个是销售员:\n", "    recordurl_df[\"对话还原\"] = recordurl_df[\"merged_text\"].apply(which_is_salesman)\n", "    recordurl_df[\n", "        [\n", "            \"sessionid\",\n", "            \"recordurl\",\n", "            \"对话还原\",\n", "            \"staffname\",\n", "            \"connectionbeginetime\",\n", "            \"to\",\n", "        ]\n", "    ].to_csv(f\"./{DATA_PATH}/七鱼对话还原{ds_to_run}.csv\", index=False)\n", "    partition_spec = f\"ds={ds_to_run}\"\n", "    table_name = f\"summerfarm_ds.crm_qiyu_call_analytics_whisper_di\"\n", "\n", "    result = write_pandas_df_into_odps(\n", "        recordurl_df.astype(str), table_name, partition_spec\n", "    )\n", "\n", "    time_cost = datetime.now() - started_at\n", "    minutes, seconds = divmod(time_cost.total_seconds(), 60)\n", "    logging.info(\n", "        f\"开始时间:{started_at}, 结束时间:{datetime.now()}, 耗时:{int(minutes)}分{int(seconds)}秒\"\n", "    )\n", "else:\n", "    logging.error(\"无须whisper解析...\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["whisper_di_df = get_odps_sql_result_as_df(\n", "    f\"\"\"\n", "select * from summerfarm_ds.crm_qiyu_call_analytics_whisper_di \n", "where ds='{ds_to_run}'\n", "\"\"\"\n", ")\n", "\n", "logging.info(\n", "    f\"first line:{whisper_di_df.head(1).to_json(orient='records', lines=False)}\"\n", ")\n", "\n", "whisper_di_df[\"communication_time_in_seconds\"] = whisper_di_df[\n", "    \"communication_time_in_seconds\"\n", "].astype(int)\n", "\n", "print(\n", "    whisper_di_df[\"communication_time_in_seconds\"].quantile([0.2, 0.5, 0.75, 0.9, 0.95])\n", ")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["whisper_di_clean_df = whisper_di_df[\n", "    [\n", "        \"communication_time_in_seconds\",\n", "        \"staffname\",\n", "        \"to\",\n", "        \"对话还原\",\n", "        \"direction\",\n", "        \"user\",\n", "        \"recordurl\",\n", "        \"sessionid\",\n", "        \"createtime\",\n", "    ]\n", "]\n", "\n", "whisper_di_clean_df.rename(columns={\"createtime\": \"通话开始时间\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["import json\n", "from openai_client import ModelType, call_azure_openai\n", "\n", "system_prompt = \"\"\"\n", "您是一位专业的销售通话评估专家。您的任务是分析和评估销售通话记录，并提供标准化的评估结果。请严格按照以下框架进行分析，并以JSON格式输出结果。\n", "\n", "## 评分标准说明\n", "\n", "每个维度的评分范围为1-10分，解释如下：\n", "- 1-3分：表现差\n", "- 4-6分：表现一般\n", "- 7-8分：表现良好\n", "- 9-10分：表现优秀\n", "\n", "总分为所有维度得分的总和。\n", "\n", "## 评估维度\n", "\n", "1. 明确目标\n", "- 是否明确本次通话具体目标(首次拜访/催单/扩品/月活/拉新)\n", "- 是否确定主要卖点(优惠券/特价/配送服务/鲜果降价/大促)\n", "- 是否针对性准备解决方案\n", "\n", "2. 开场白\n", "- 销售员是否根据不同目标使用了相应的开场白?\n", "- 催单:是否提到了活动、库存有限等信息来制造紧迫感?\n", "- 扩品:是否提到了客户正在使用的产品,并自然引入新产品?  \n", "- 拉新/召回:是否询问了客户长期未使用的原因?\n", "\n", "3. 答疑解惑\n", "- 销售员是否针对客户的各种疑问和反馈给出了恰当的回应?\n", "- 对于价格质疑,是否尝试了以下方法:\n", "  a) 强调批发价和低利润\n", "  b) 与竞争对手比较\n", "  c) 申请特殊优惠\n", "  d) 提供组合优惠\n", "- 面对客户敷衍时,是否抓住机会表达了所有可能引起兴趣的卖点?\n", "- 对于不耐烦的客户,是否简洁地表达了最大的优惠筹码?\n", "- 在客户表示已从其他供应商购买时,是否尝试:\n", "  a) 对比价格\n", "  b) 推荐省心送服务\n", "  c) 强调一站式采购的便利性\n", "- 介绍新品时,是否充分阐述了产品优点并举例说明?\n", "- 面对拒绝,是否尝试提供样品或进行对比说服?\n", "\n", "4. 了解情况\n", "- 销售员是否主动询问了以下信息:\n", "  a) 客户目前的供应商情况\n", "  b) 竞争对手的配送频率和服务\n", "  c) 客户对现有产品质量的评价\n", "- 是否针对客户反馈的问题(如生意差、忘记平台、价格高、品质差、售后问题等)给出了恰当的回应和解决方案?\n", "\n", "5. 应对特殊情况\n", "- 对于表示店铺关闭的客户,是否了解了具体情况并留下未来合作的可能性?\n", "- 对于更换地址的客户,是否确认了新地址是否在配送范围内,并指导其更新信息?\n", "\n", "6. 结束和后续跟进\n", "- 销售员是否在通话结束时设法获取了客户的微信联系方式?\n", "- 是否承诺发送产品信息或优惠详情到客户微信?\n", "- 对于没有微信的客户,是否表示将添加其手机号为微信好友?\n", "\n", "7. 整体沟通技巧\n", "- 销售员是否使用了适当的称呼(哥/姐/X总)?\n", "- 是否在整个对话过程中保持了专业和诚恳的态度?\n", "- 是否能够灵活运用话术,根据客户的不同反应进行调整?\n", "- 在遇到客户抱怨或负面反馈时,是否能够耐心倾听并给出合适的解决方案?\n", "- 销售员是否始终围绕本次通话的主要目标展开对话,不偏离主题?\n", "\n", "8. 产品知识\n", "- 销售员是否展现出对不同类型店铺(如烘焙店、茶饮店)所需产品的全面了解?\n", "- 在介绍产品时,是否能够准确说出品牌名称和产品特点?\n", "\n", "9. 说服技巧\n", "- 销售员是否善用对比、案例分享等方式增强说服力?\n", "- 是否能够快速识别客户的需求和痛点,并针对性地提供解决方案?\n", "\n", "10. 关系维护\n", "- 销售员是否表现出对客户业务的关心,如询问近期生意状况?\n", "- 是否主动提出可以协助解决问题或提供持续支持?\n", "\n", "## 输出格式要求\n", "\n", "请严格按照以下JSON格式输出评估结果：\n", "\n", "```json\n", "{\"通话评估\":{\"明确目标\":{\"得分\":0,\"分析\":{\"优点\":\"\",\"不足\":\"\"}},\"开场白\":{\"得分\":0,\"分析\":{\"优点\":\"\",\"不足\":\"\"}},\"答疑解惑\":{\"得分\":0,\"分析\":{\"优点\":\"\",\"不足\":\"\"}},\"了解情况\":{\"得分\":0,\"分析\":{\"优点\":\"\",\"不足\":\"\"}},\"应对特殊情况\":{\"得分\":0,\"分析\":{\"优点\":\"\",\"不足\":\"\"}},\"结束和后续跟进\":{\"得分\":0,\"分析\":{\"优点\":\"\",\"不足\":\"\"}},\"整体沟通技巧\":{\"得分\":0,\"分析\":{\"优点\":\"\",\"不足\":\"\"}},\"产品知识\":{\"得分\":0,\"分析\":{\"优点\":\"\",\"不足\":\"\"}},\"说服技巧\":{\"得分\":0,\"分析\":{\"优点\":\"\",\"不足\":\"\"}},\"关系维护\":{\"得分\":0,\"分析\":{\"优点\":\"\",\"不足\":\"\"}}},\"整体评估\":{\"主要优点\":[],\"主要问题\":[]}}\n", "```\n", "\n", "请按照上述框架进行评估，并输出规范的JSON格式结果。\n", "\"\"\"\n", "\n", "summarize_system_prompt = \"\"\"\n", "## 总结销售员和客户的电话内容。请遵循以下原则：\n", "\n", "1. 输出字数限制：150字以内\n", "- 语言风格：客观、专业、连贯\n", "\n", "2. 内容要素(按重要性排序)：\n", "- 核心问题/诉求是什么\n", "- 问题的具体表现\n", "- 客户的关键背景信息\n", "- 当前处理进展\n", "- 对话的结果/解决方案\n", "\n", "3. 表达要求：\n", "- 用简洁的句子串联各要素\n", "- 使用分号或逗号区分不同信息点\n", "- 避免重复信息\n", "- 去除非必要的细节\n", "- 保持时态一致\n", "\n", "4. 禁止事项：\n", "- 不要添加主观评价\n", "- 不要猜测未提及的信息\n", "- 不要包含对话中的口头语或语气词\n", "\"\"\"\n", "\n", "\n", "def sum_scores(json_data):\n", "    # Extract the \"通话评估\" section from the JSON\n", "    call_assessment = json_data.get(\"通话评估\", {})\n", "\n", "    # Initialize total score to 0\n", "    total_score = 0\n", "\n", "    # Iterate through the categories in \"通话评估\" and sum the \"得分\"\n", "    for category, details in call_assessment.items():\n", "        score = details.get(\"得分\", 0)\n", "        total_score += score\n", "\n", "    return total_score\n", "\n", "\n", "def diarization_row(row: pd.Series) -> str:\n", "    text_input = row[\"对话还原\"]\n", "    if \"无需分析\" in text_input:\n", "        return text_input\n", "    result, is_ok = call_azure_openai(\n", "        text_input=text_input,\n", "        system_prompt=system_prompt,\n", "        json=True,\n", "        model_type=ModelType.OPENROUTER,\n", "    )\n", "\n", "    summarize_result, is_ok = call_azure_openai(\n", "        text_input=text_input,\n", "        system_prompt=summarize_system_prompt,\n", "        json=False,\n", "        model_type=ModelType.OPENROUTER,\n", "    )\n", "\n", "    try:\n", "        result = json.loads(result)\n", "        result[\"总分\"] = sum_scores(result)\n", "        result[\"对话总结\"] = summarize_result\n", "    except Exception as e:\n", "        logging.error(f\"解析异常：{result},{e}\")\n", "        return result\n", "\n", "    logging.info(f\"GPT is_ok:{is_ok}, result:{result}\")\n", "    return json.dumps(result, ensure_ascii=False)\n", "\n", "\n", "whisper_di_clean_df[\"AI评分\"] = whisper_di_clean_df.apply(diarization_row, axis=1)\n", "\n", "whisper_di_clean_df.to_csv(f\"{DATA_PATH}/AI评价_{ds_to_run}.csv\", index=False)\n", "\n", "write_pandas_df_into_odps(\n", "    df=whisper_di_clean_df,\n", "    table_name=\"summerfarm_ds.crm_qiyu_call_ai_summarize_di\",\n", "    partition_spec=f\"ds={ds_to_run}\",\n", "    overwrite=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["time_cost = datetime.now() - started_at\n", "hours, remainder = divmod(time_cost.total_seconds(), 3600)\n", "minutes, seconds = divmod(remainder, 60)\n", "msg = f\"\"\"- 开始时间:{started_at.strftime('%Y%m%d %H:%M:%S')}\n", "- 结束时间:{datetime.now().strftime('%Y%m%d %H:%M:%S')}\n", "- 总耗时:{int(hours)}时{int(minutes)}分{int(seconds)}秒\"\"\"\n", "\n", "send_markdown_to_feishu(\n", "    f\"电销语音解析完成:ds={ds_to_run}\",\n", "    f\"- 共解析了{len(whisper_di_clean_df)}个会话\\n{msg}\",\n", "    feishu_token=feishu_token,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 2}