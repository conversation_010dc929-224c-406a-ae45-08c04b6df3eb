#!/usr/bin/env python
# coding: utf-8


import sys
import argparse

# Add the scripts directory to the sys.path
sys.path.append("../")

from datetime import datetime
import os
from odps_client import logging, get_odps_sql_result_as_df, write_pandas_df_into_odps
from openai_client import call_azure_openai
import pandas as pd
from feishu_client import send_markdown_to_feishu

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

# 设置命令行参数
parser = argparse.ArgumentParser(description="指定运行日期", allow_abbrev=False)
parser.add_argument(
    "--ds_to_run", type=str, default="", help="运行日期，格式为YYYYMMDD", required=False
)

parser.add_argument(
    "--reuse_downloaded_files",
    type=str,
    default="false",
    help="是否重复利用已经下载的wav文件（提速），默认false",
    required=False,
)
parser.add_argument(
    "--communication_time_lower_limit",
    type=int,
    default=20,
    help="最低沟通时长，低于这个阈值则不需要分析，默认20s",
    required=False,
)
parser.add_argument(
    "--use_q4_model",
    choices=["true", "false"],
    default="false",
    help="是否使用Q4模型（会快一些）",
    required=False,
)
parser.add_argument(
    "--feishu_token",
    type=str,
    default="-",
    help="飞书通知用的token,默认不发送通知",
    required=False,
)
parser.add_argument(
    "--skip_whisper",
    choices=["true", "false"],
    default="false",
    help="是否跳过whisper解析的部分，用于直接跑AI分析",
    required=False,
)
parser.add_argument(
    "--rows_to_analytics",
    type=int,
    default=20000,
    help="用于分析的行数",
    required=False,
)

args, unknown = parser.parse_known_args()
communication_time_lower_limit = args.communication_time_lower_limit
reuse_downloaded_files = "true" == args.reuse_downloaded_files
use_q4_model = "true" == args.use_q4_model
feishu_token = args.feishu_token
skip_whisper = "true" == args.skip_whisper
rows_to_analytics = args.rows_to_analytics

started_at = datetime.now()
# 如果没有指定日期，则使用昨天的日期
if args.ds_to_run:
    ds_to_run = args.ds_to_run
else:
    # 如果没有指定日期，则使用当天的日期
    ds_to_run = started_at.strftime("%Y%m%d")

logging.info(f"ds_to_run:{ds_to_run}")

import shutil

DATA_PATH = f"./qiyu_audio_data"


def create_dir_if_not_exist(path):
    # Remove the directory if it exists
    if os.path.exists(path) and not reuse_downloaded_files:
        logging.warning(f"即将删除以下文件夹的内容:{path}")
        shutil.rmtree(path)
    # Create the directory
    os.makedirs(path, exist_ok=reuse_downloaded_files)
    return path


create_dir_if_not_exist(DATA_PATH)


import mlx_whisper
import numpy as np


def convert_np_floats(data):
    if isinstance(data, dict):
        # Recursively process dictionaries
        return {key: convert_np_floats(value) for key, value in data.items()}
    elif isinstance(data, list):
        # Recursively process lists
        return [convert_np_floats(item) for item in data]
    elif isinstance(data, np.float64):
        # Convert np.float64 to Python float
        return float(data)
    else:
        # Return the data as is for non-dict and non-float64 values
        return data


LOCAL_MLX_MODEL_PATH = os.getenv(
    "LOCAL_MLX_MODEL_PATH", "/Users/<USER>/Documents/mlx-whisper"
)

LOCAL_MLX_MODEL_Q4_PATH = os.getenv(
    "LOCAL_MLX_MODEL_Q4_PATH", "/Users/<USER>/Documents/mlx-whisper-v3-turbo-q4"
)


def transcribe_with_local_whisper_model(
    audio_file: str = None,
) -> dict:
    logging.info(f"使用本地模型进行transcribe, audio_file:{audio_file}")
    result = mlx_whisper.transcribe(
        audio_file,
        path_or_hf_repo=(
            LOCAL_MLX_MODEL_Q4_PATH if use_q4_model else LOCAL_MLX_MODEL_PATH
        ),
        initial_prompt="鲜沐农场的销售员和客户的电话录音：",
    )
    converted_data = convert_np_floats(result)
    return converted_data


# ## 切分wav文件为左右两个声道


import os, subprocess


def split_wav_into_left_and_right_channels(file_path: str) -> list:
    try:
        file_base_name = os.path.basename(file_path)
        directory = os.path.dirname(file_path)
        left_path = f"{directory}/left_{file_base_name}"
        right_path = f"{directory}/right_{file_base_name}"

        cmd = f'ffmpeg -loglevel error -y -i "{file_path}" -filter_complex "[0:a]channelsplit=channel_layout=stereo[left][right]" -map "[left]" "{left_path}" -map "[right]" "{right_path}"'

        # Use subprocess.Popen instead of run to better handle output
        process = subprocess.Popen(
            cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True
        )

        stdout, stderr = process.communicate()

        if process.returncode != 0:
            logging.error(f"FFmpeg error: {stderr}")
            return None

        logging.info(f"Successfully split {file_path} into left and right channels")
        return [left_path, right_path]

    except Exception as e:
        logging.error(f"Error during audio splitting: {str(e)}")
        return None


# ## 这是Groq模型


import pandas as pd

USE_LOCAL_WHISPER = os.getenv("USE_LOCAL_WHISPER", "true")
USE_LOCAL_WHISPER = "true" == USE_LOCAL_WHISPER


def call_whisper_with_local_file(
    filename="/Users/<USER>/Downloads/白津源_3d341fed6a4637ad614830dc9d1a6b97.wav",
    is_retrying=False,
):
    if USE_LOCAL_WHISPER:
        # 使用本地模型进行转录
        return transcribe_with_local_whisper_model(filename)


def merge_left_and_right_channel_segments(left_segments=[], right_segments=[]):
    merge = [None] * (
        len(left_segments) + len(right_segments)
    )  # Initialize the merge list

    l, r = 0, 0
    merge_idx = 0
    last_r_text, last_l_text = "", ""

    for i in range(len(left_segments) + len(right_segments)):
        seg_l = left_segments[l] if l < len(left_segments) else None
        seg_r = right_segments[r] if r < len(right_segments) else None

        if seg_r:
            seg_r["speaker"] = "right"
        if seg_l:
            seg_l["speaker"] = "left"

        if seg_l is None:
            if seg_r["text"] != last_r_text:
                logging.info(f"seg_r: {seg_r['text']}")
                merge[merge_idx] = seg_r
                last_r_text = seg_r["text"]
                merge_idx += 1
            r += 1
            continue

        if seg_r is None:
            if seg_l["text"] != last_l_text:
                logging.info(f"seg_l: {seg_l['text']}")
                merge[merge_idx] = seg_l
                last_l_text = seg_l["text"]
                merge_idx += 1
            l += 1
            continue

        if seg_l["end"] < seg_r["end"]:
            if seg_l["text"] != last_l_text:
                merge[merge_idx] = seg_l
                last_l_text = seg_l["text"]
                merge_idx += 1
            l += 1
        else:
            if seg_r["text"] != last_r_text:
                merge[merge_idx] = seg_r
                last_r_text = seg_r["text"]
                merge_idx += 1
            r += 1

    # Remove None values from the merge list
    return [x for x in merge[:merge_idx] if x is not None]


from feishu_client import send_markdown_to_feishu


staffname_list_to_analytics = os.getenv(
    "STAFF_NAME_LIST", "白津源,陈汉文,李梦婷,宋懿航"
)
staffname_list = "','".join(staffname_list_to_analytics.split(","))
logging.info(f"staffname_list:{staffname_list}, rows_to_analytics:{rows_to_analytics}")

recordurl_df = get_odps_sql_result_as_df(
    f"""
SELECT  *,DATEDIFF(CAST(endtime AS TIMESTAMP),CAST(createtime AS TIMESTAMP),'ss') communication_time_in_seconds,'{ds_to_run}' as ds
FROM    (
            SELECT  JSON_TUPLE(body,"eventtype","sessionid","direction","createtime","endtime","connectionbeginetime","connectionendtime","from","to","user","category","staffid","staffname","status","visittimes","duration","evaluation","recordurl","overflowFrom","shuntGroupName","ivrPath","mobileArea","waitDuration","ringDuration","sessionIdFrom","firstEndDirection") AS ("eventtype","sessionid","direction","createtime","endtime","connectionbeginetime","connectionendtime","from","to","user","category","staffid","staffname","status","visittimes","duration","evaluation","recordurl","overflowFrom","shuntGroupName","ivrPath","mobileArea","waitDuration","ringDuration","sessionIdFrom","firstEndDirection")
            FROM    summerfarm_tech.ods_qiyu_call_log_di
            WHERE   ds = '{ds_to_run}'
            AND     GET_JSON_OBJECT(body,'$.eventtype') = '5'
        ) 
WHERE   recordurl LIKE 'http%'
and staffname in('{staffname_list}')
limit {rows_to_analytics};"""
)

length_of_source = len(recordurl_df)
logging.info(f"数据量大小:{length_of_source}")
if length_of_source <= 0:
    send_markdown_to_feishu(
        title=f"暂无电销录音数据,ds={ds_to_run}",
        markdown_content="如题",
        feishu_token=feishu_token,
    )
    raise Exception("summerfarm_tech.ods_qiyu_call_log_di 的数据为空")
else:
    send_markdown_to_feishu(
        title=f"电销录音分析任务开始了,数据量大小:{length_of_source},ds={ds_to_run}",
        markdown_content="如题",
        feishu_token=feishu_token,
    )


# 下载wav文件到本地，用于whisper模型识别

from concurrent.futures import ThreadPoolExecutor
import concurrent
import requests
import os
from multiprocessing import cpu_count


def download_wav_file(url, sessionid, communication_time_in_seconds=0):
    logging.info(
        f"下载语音文件:{url}, session:{sessionid}, 通话时长:{communication_time_in_seconds}s"
    )
    file_name = os.path.basename(url)
    response = requests.get(url)
    local_file = f"{DATA_PATH}/{sessionid}_{file_name}"
    if os.path.exists(local_file):
        logging.info(f"The file {local_file} already exists.")
        return

    # Check if the request was successful
    if response.status_code == 200:
        # Open a file in binary mode to write the content
        with open(local_file, "wb") as f:
            f.write(response.content)
        logging.info(f"File {file_name} downloaded successfully.")
    else:
        logging.info("Failed to download the file.")


if not skip_whisper:
    with ThreadPoolExecutor(max_workers=cpu_count() * 2) as executor:
        futures = [
            executor.submit(
                download_wav_file,
                row["recordurl"],
                row["sessionid"],
                row["communication_time_in_seconds"],
            )
            for index, row in recordurl_df.iterrows()
        ]
        concurrent.futures.wait(futures)
    recordurl_df["local_wav_file"] = recordurl_df.apply(
        lambda row: f"{DATA_PATH}/{row['sessionid']}_{os.path.basename(row['recordurl'])}",
        axis=1,
    )

    print(
        recordurl_df[["local_wav_file", "sessionid", "recordurl", "staffname"]].head(5)
    )
else:
    logging.warning("跳过whisper解析部分，直接到AI分析")


def strip_repeated_text(text: str) -> str:
    import re

    # 使用正则表达式去除重复3次以上的词语
    return re.sub(r"\b(\w+)( \1\b){2,}", r"\1", text)


def split_wav_and_call_whisper(local_wav_file: str) -> (str, dict):
    channels = split_wav_into_left_and_right_channels(local_wav_file)
    segment_result = []
    for file in channels:
        whisper_result = call_whisper_with_local_file(filename=file)
        if whisper_result is not None:
            segments = whisper_result.get("segments", [])
            # Filter out segments with empty text
            segments = [s for s in segments if s.get("text", "").strip()]
            # 去除重复文本
            for segment in segments:
                segment["text"] = strip_repeated_text(segment["text"])
            segment_result.append(segments)
        else:
            segment_result.append([])

    if len(segment_result) != 2 or not all(segment_result):
        return (f"未能解析所有声道的数据: {local_wav_file}", {})

    merge_result = merge_left_and_right_channel_segments(
        segment_result[0], segment_result[1]
    )
    merged_text = "\n".join(
        [
            # 以下可以把交流的时间戳一并打印出来
            # f"[{round(segment['start'], 1)}~{round(segment['end'], 1)}] {segment['speaker']}: {segment['text']}"
            f"{segment['speaker']}: {segment['text']}"
            for segment in merge_result
            if segment.get("text", "").strip()
        ]
    )
    logging.info(f"local_wav_file: {local_wav_file}, \nmerged_text{merged_text}")
    return (merged_text, merge_result)


def transcribe_row(row: pd.Series) -> pd.Series:
    logging.info(f"transcribing session:{row['sessionid']}...")
    index_to_return = ["merged_text", "merge_result"]
    try:
        # if row["communication_time_in_seconds"] <= communication_time_lower_limit:
        #     logging.warning(
        #         f"沟通时长太短了:{row['communication_time_in_seconds']}s, 无需分析"
        #     )
        #     return pd.Series(
        #         {
        #             "merged_text": f"沟通时长太短了:{row['communication_time_in_seconds']}s, 无需分析",
        #             "merge_result": None,
        #         },
        #         index=index_to_return,
        #     )
        logging.info(
            f"transcribing file:{row['local_wav_file']} with whisper...communication_time_in_seconds:{row['communication_time_in_seconds']}"
        )
        merged_text, merge_result = split_wav_and_call_whisper(row["local_wav_file"])
        return pd.Series(
            {"merged_text": merged_text, "merge_result": merge_result},
            index=index_to_return,
        )
    except Exception as e:
        logging.error(
            f"调用whisper失败了:{e}, file:{row['local_wav_file']}", exc_info=True
        )
        return pd.Series(
            {"merged_text": f"ERROR:{e}", "merge_result": None},
            index=index_to_return,
        )


from openai_client import call_azure_openai, parse_json_string


def which_is_salesman(merged_text):
    prompt = f"""以下是销售员和客户之间的对话，请你根据对话的内容，判断left和right分别是销售员还是客户。请以json格式输出，比如{{"left": "[销售员或客户]", "right": "[销售员或客户]"}}
    
{merged_text}
    """
    response, success = call_azure_openai(text_input=prompt)
    if not success:
        return merged_text

    salesman_json = parse_json_string(response)
    logging.info(f"salesman_json:{salesman_json}, AI response:{response}")
    for key, value in salesman_json.items():
        merged_text = merged_text.replace(key, value)

    return merged_text


if not skip_whisper:
    recordurl_df.sort_values(by="communication_time_in_seconds", ascending=False)
    # 先使用whisper做语音到文本的解析:
    recordurl_df = recordurl_df.join(recordurl_df.apply(transcribe_row, axis=1))
    # 再调用AI区分出left和right哪一个是销售员:
    recordurl_df["对话还原"] = recordurl_df["merged_text"].apply(which_is_salesman)
    recordurl_df[
        [
            "sessionid",
            "recordurl",
            "对话还原",
            "staffname",
            "connectionbeginetime",
            "to",
        ]
    ].to_csv(f"./{DATA_PATH}/七鱼对话还原{ds_to_run}.csv", index=False)
    partition_spec = f"ds={ds_to_run}"
    table_name = f"summerfarm_ds.crm_qiyu_call_analytics_whisper_di"

    result = write_pandas_df_into_odps(
        recordurl_df.astype(str), table_name, partition_spec, overwrite=True
    )

    time_cost = datetime.now() - started_at
    minutes, seconds = divmod(time_cost.total_seconds(), 60)
    logging.info(
        f"开始时间:{started_at}, 结束时间:{datetime.now()}, 耗时:{int(minutes)}分{int(seconds)}秒"
    )
else:
    logging.error("无须whisper解析...")


whisper_di_df = get_odps_sql_result_as_df(
    f"""
select * from summerfarm_ds.crm_qiyu_call_analytics_whisper_di 
where ds='{ds_to_run}'
"""
)

if whisper_di_df.empty:
    logging.error(f"summerfarm_ds.crm_qiyu_call_analytics_whisper_di 没有数据！")
    exit(-1)

logging.info(f"first row:{whisper_di_df.iloc[0].to_dict()}")

whisper_di_df["communication_time_in_seconds"] = whisper_di_df[
    "communication_time_in_seconds"
].astype(int)

print(
    whisper_di_df["communication_time_in_seconds"].quantile([0.2, 0.5, 0.75, 0.9, 0.95])
)


whisper_di_clean_df = whisper_di_df[
    [
        "communication_time_in_seconds",
        "staffname",
        "to",
        "对话还原",
        "direction",
        "user",
        "recordurl",
        "sessionid",
        "createtime",
    ]
].copy()

whisper_di_clean_df.rename(columns={"createtime": "通话开始时间"}, inplace=True)


import json

system_prompt = """
您是一位专业的销售通话评估专家。您的任务是分析和评估销售通话记录，并提供标准化的评估结果。请严格按照以下框架进行分析，并以JSON格式输出结果。

## 评分标准说明

每个维度的评分范围为1-10分，解释如下：
- 1-3分：表现差
- 4-6分：表现一般
- 7-8分：表现良好
- 9-10分：表现优秀

总分为所有维度得分的总和。

## 评估维度

1. 明确目标
- 是否明确本次通话具体目标(首次拜访/催单/扩品/月活/拉新)
- 是否确定主要卖点(优惠券/特价/配送服务/鲜果降价/大促)
- 是否针对性准备解决方案

2. 开场白
- 销售员是否根据不同目标使用了相应的开场白?
- 催单:是否提到了活动、库存有限等信息来制造紧迫感?
- 扩品:是否提到了客户正在使用的产品,并自然引入新产品?  
- 拉新/召回:是否询问了客户长期未使用的原因?

3. 答疑解惑
- 销售员是否针对客户的各种疑问和反馈给出了恰当的回应?
- 对于价格质疑,是否尝试了以下方法:
  a) 强调批发价和低利润
  b) 与竞争对手比较
  c) 申请特殊优惠
  d) 提供组合优惠
- 面对客户敷衍时,是否抓住机会表达了所有可能引起兴趣的卖点?
- 对于不耐烦的客户,是否简洁地表达了最大的优惠筹码?
- 在客户表示已从其他供应商购买时,是否尝试:
  a) 对比价格
  b) 推荐省心送服务
  c) 强调一站式采购的便利性
- 介绍新品时,是否充分阐述了产品优点并举例说明?
- 面对拒绝,是否尝试提供样品或进行对比说服?

4. 了解情况
- 销售员是否主动询问了以下信息:
  a) 客户目前的供应商情况
  b) 竞争对手的配送频率和服务
  c) 客户对现有产品质量的评价
- 是否针对客户反馈的问题(如生意差、忘记平台、价格高、品质差、售后问题等)给出了恰当的回应和解决方案?

5. 应对特殊情况
- 对于表示店铺关闭的客户,是否了解了具体情况并留下未来合作的可能性?
- 对于更换地址的客户,是否确认了新地址是否在配送范围内,并指导其更新信息?

6. 结束和后续跟进
- 销售员是否在通话结束时设法获取了客户的微信联系方式?
- 是否承诺发送产品信息或优惠详情到客户微信?
- 对于没有微信的客户,是否表示将添加其手机号为微信好友?

7. 整体沟通技巧
- 销售员是否使用了适当的称呼(哥/姐/X总)?
- 是否在整个对话过程中保持了专业和诚恳的态度?
- 是否能够灵活运用话术,根据客户的不同反应进行调整?
- 在遇到客户抱怨或负面反馈时,是否能够耐心倾听并给出合适的解决方案?
- 销售员是否始终围绕本次通话的主要目标展开对话,不偏离主题?

8. 产品知识
- 销售员是否展现出对不同类型店铺(如烘焙店、茶饮店)所需产品的全面了解?
- 在介绍产品时,是否能够准确说出品牌名称和产品特点?

9. 说服技巧
- 销售员是否善用对比、案例分享等方式增强说服力?
- 是否能够快速识别客户的需求和痛点,并针对性地提供解决方案?

10. 关系维护
- 销售员是否表现出对客户业务的关心,如询问近期生意状况?
- 是否主动提出可以协助解决问题或提供持续支持?

## 输出格式要求

请严格按照以下JSON格式输出评估结果：

```json
{"通话评估":{"明确目标":{"得分":0,"分析":{"优点":"","不足":""}},"开场白":{"得分":0,"分析":{"优点":"","不足":""}},"答疑解惑":{"得分":0,"分析":{"优点":"","不足":""}},"了解情况":{"得分":0,"分析":{"优点":"","不足":""}},"应对特殊情况":{"得分":0,"分析":{"优点":"","不足":""}},"结束和后续跟进":{"得分":0,"分析":{"优点":"","不足":""}},"整体沟通技巧":{"得分":0,"分析":{"优点":"","不足":""}},"产品知识":{"得分":0,"分析":{"优点":"","不足":""}},"说服技巧":{"得分":0,"分析":{"优点":"","不足":""}},"关系维护":{"得分":0,"分析":{"优点":"","不足":""}}},"整体评估":{"主要优点":[],"主要问题":[]}}
```

请按照上述框架进行评估，并输出规范的JSON格式结果。
"""

summarize_system_prompt = """
## 总结销售员和客户的电话内容。请遵循以下原则：

1. 输出字数限制：150字以内
- 语言风格：客观、专业、连贯

2. 内容要素(按重要性排序)：
- 核心问题/诉求是什么
- 问题的具体表现
- 客户的关键背景信息
- 当前处理进展
- 对话的结果/解决方案

3. 表达要求：
- 用简洁的句子串联各要素
- 使用分号或逗号区分不同信息点
- 避免重复信息
- 去除非必要的细节
- 保持时态一致

4. 禁止事项：
- 不要添加主观评价
- 不要猜测未提及的信息
- 不要包含对话中的口头语或语气词
"""


def sum_scores(json_data):
    try:
        # 如果json_data是字符串，尝试解析为JSON
        if isinstance(json_data, str):
            json_data = json.loads(json_data)

        # Extract the "通话评估" section from the JSON
        call_assessment = json_data.get("通话评估", {})

        # Initialize total score to 0
        total_score = 0

        # Iterate through the categories in "通话评估" and sum the "得分"
        for category, details in call_assessment.items():
            score = details.get("得分", 0)
            total_score += score

        return total_score
    except Exception as e:
        logging.error(f"Error calculating sum scores: {e}, json_data:{json_data}")
        return -1


def summarize_row_with_ai(row: pd.Series) -> pd.Series:
    text_input = row["对话还原"]
    communication_time_in_seconds = row["communication_time_in_seconds"]
    index_to_return = ["SOP分析", "总分", "对话总结"]
    if (
        "无需分析" in text_input
        or communication_time_in_seconds <= communication_time_lower_limit
    ):
        return pd.Series(
            {
                "SOP分析": f"沟通时长过短:{communication_time_in_seconds}s, 文本解析:{text_input}",
                "总分": None,
                "对话总结": text_input,
            },
            index=index_to_return,
        )

    sop_result = {"SOP分析": None, "总分": None, "对话总结": None}
    try:
        result, is_ok = call_azure_openai(
            text_input=text_input,
            system_prompt=system_prompt,
            json=True,
        )

        if not is_ok:
            logging.error(f"SOP分析失败：{result}")
            return pd.Series(
                {
                    "SOP分析": None,
                    "总分": None,
                    "对话总结": None,
                },
                index=index_to_return,
            )

        summarize_result, is_ok = call_azure_openai(
            text_input=text_input,
            system_prompt=summarize_system_prompt,
            json=False,
        )

        if not is_ok:
            logging.error(f"对话总结失败：{summarize_result}")
            return pd.Series(
                {
                    "SOP分析": result,
                    "总分": None,
                    "对话总结": None,
                },
                index=index_to_return,
            )

        sop_result["SOP分析"] = result
        sop_result["总分"] = sum_scores(result)
        sop_result["对话总结"] = summarize_result
    except Exception as e:
        logging.error(f"解析异常：{e}, text_input:{text_input}")
        return pd.Series(
            {"SOP分析": None, "总分": None, "对话总结": f"{e}"}, index=index_to_return
        )

    logging.info(f"GPT分析完成, result:{result}, text_input:{text_input}")
    return pd.Series(sop_result, index=index_to_return)


whisper_di_clean_df[["SOP分析", "总分", "对话总结"]] = whisper_di_clean_df.apply(
    summarize_row_with_ai, axis=1
).applymap(str)

whisper_di_clean_df.to_csv(f"{DATA_PATH}/AI评价_{ds_to_run}.csv", index=False)

write_pandas_df_into_odps(
    df=whisper_di_clean_df,
    table_name="summerfarm_ds.crm_qiyu_call_ai_summarize_di",
    partition_spec=f"ds={ds_to_run}",
    overwrite=True,
)

staffname_list=list(whisper_di_clean_df['staffname'].unique())

time_cost = datetime.now() - started_at
hours, remainder = divmod(time_cost.total_seconds(), 3600)
minutes, seconds = divmod(remainder, 60)
msg = f"""- 开始时间:{started_at.strftime('%Y%m%d %H:%M:%S')}
- 结束时间:{datetime.now().strftime('%Y%m%d %H:%M:%S')}
- 总耗时:{int(hours)}时{int(minutes)}分{int(seconds)}秒"""

send_markdown_to_feishu(
    f"电销语音解析完成:ds={ds_to_run}",
    f"- 解析了:{','.join(staffname_list)}等{len(staffname_list)}人的共{len(whisper_di_clean_df)}个七鱼会话\n{msg}",
    feishu_token=feishu_token,
)
