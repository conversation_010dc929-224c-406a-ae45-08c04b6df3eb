#!/usr/bin/env python
# coding: utf-8


from sls_client import get_sls_data_by_query
from datetime import datetime, timedelta
from odps_client import get_odps_sql_result_as_df

# 获取当前日期和时间
now = datetime.now()
today_time = now.strftime("%Y-%m-%d 00:00:00")
today_time = datetime.strptime(today_time, "%Y-%m-%d %H:%M:%S")

query = f"""type:cl and (name:点击立即定制 or bid:"pid:落地页图片,name:2643,type:4") |
select phone,uid,sku,count(0) "点击次数",date_format(max(__time__) ,'%Y-%m-%d %H:%i:%S') "最后点击时间" 
from log group by 1,2,3"""

click_uid_df = get_sls_data_by_query(
    query=query,
    from_time=today_time,
    to_time=now,
    project="xianmu-front-end-log",
    logstore="xm-mall",
)

if len(click_uid_df)<=0:
    print("没有数据")
    exit()

click_uid_df=click_uid_df.drop(columns=["__source__", "__time__"], errors="ignore")
clicked_skus=click_uid_df["sku"].unique()

print(f"打印clicked_skus", clicked_skus)
clicked_skus_str="','".join([str(sku) for sku in clicked_skus]) # 连接成字符串


sku_sql = f"""
select sku_id,spu_name,disc as specification
from summerfarm_tech.dim_sku_df
where ds=max_pt('summerfarm_tech.dim_sku_df') and sku_id in ('{clicked_skus_str}')
"""
sku_df = get_odps_sql_result_as_df(sku_sql)

print(f"打印sku_df.head(5)")
sku_df.head(5)

# 获取token
import requests
import os

login_url = "https://admin.summerfarm.net/authentication/auth/username/login"
login_data = {
    "username": "<EMAIL>",
    "password": os.getenv("XIANMU_ADMIN_PASSWORD"),
}

token = requests.post(login_url, data=login_data).json()

token_str = token.get("data").get("token")

print(token)


headers = {
    "token": token_str,
    "xm-rqid": "create_fake_merchant_tp",
    "xm-uid": "2047",
    "Content-Type": "application/json;charset=UTF-8",
}

print(headers)


import requests


def get_merchant_detail(uid=51637):
    uid = int(uid)

    url = "https://admin.summerfarm.net/sf-mall-manage/merchant/query/page"
    _headers = {
        "content-type": "application/json;charset=UTF-8",
        "token": headers["token"],
    }
    data = {"mId": uid, "pageIndex": 1, "pageSize": 10}
    response = requests.post(url, headers=_headers, json=data)
    return response.json().get("data", {}).get("list", [])[0]


click_uid_df["merchant"] = click_uid_df["uid"].apply(get_merchant_detail)
click_uid_df.head(4)


click_uid_df = click_uid_df.sort_values(by="最后点击时间", ascending=False)

markdown_list = ["门店ID, 手机号, BD名字, 门店名称, 区域, 门店业态, 最后点击时间, 点击次数, 点击的SKU"]
for index, row in click_uid_df.iterrows():
    merchant = row["merchant"]
    phone = row["phone"]
    uid = row["uid"]
    # 由于merchant字典中的字段可能不存在，使用get方法并设置默认值为空字符串
    adminRealName = merchant.get("adminRealName", "无")
    mname = merchant.get("mname", "无")
    areaName = merchant.get("areaName", "无")
    businessType = merchant.get("businessType", "无")
    lastClickTime = row["最后点击时间"]
    clickTimes = row["点击次数"]
    sku_id = row["sku"]
    sku_name = sku_df[sku_df["sku_id"] == sku_id]["spu_name"].values[0]
    markdown_list.append(
        f"{len(markdown_list)}. {uid}, {phone}, {adminRealName}, {mname}, {areaName}, {businessType}, {lastClickTime}, {clickTimes}, {sku_id}:{sku_name}"
    )

markdown_output = "\n".join(markdown_list)
print(markdown_output)


from datetime import datetime
import requests


url = (
    "https://open.feishu.cn/open-apis/bot/v2/hook/e7802cad-765c-44f8-ac45-186b28cd4c8a"
)


def send_feishu_notice_with_title_and_content(
    markdown_str: str,
    feishu_url=url,
    title="",
    error=False,
):
    feishu_message_obj = {
        "schema": "2.0",
        "header": {
            "template": "red" if error else "blue",
            "title": {
                "content": f"**{title}**",
                "tag": "lark_md",
            },
        },
        "body": {
            "elements": [
                {
                    "tag": "markdown",
                    "content": markdown_str,
                },
                {
                    "tag": "markdown",
                    "content": f"> 数据生成于:{datetime.now().strftime('%Y-%m-%d %H:%M')}\n> ",
                },
            ]
        },
    }
    headers = {"Content-Type": "application/json"}
    data = {"msg_type": "interactive", "card": feishu_message_obj}
    feishu_result = requests.post(
        url=feishu_url, json=data, headers=headers, verify=False, proxies={}
    ).json()
    return feishu_result

date_of_today = datetime.now().strftime("%Y-%m-%d")
send_feishu_notice_with_title_and_content(markdown_str=markdown_output, title=f"点击立即定制的门店列表_{date_of_today}")



