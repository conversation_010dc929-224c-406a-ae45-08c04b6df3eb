{"cells": [{"cell_type": "markdown", "source": ["# 准备数据"], "metadata": {"collapsed": false}, "id": "839f7248d9cb919f"}, {"cell_type": "code", "execution_count": null, "id": "initial_id", "metadata": {"collapsed": true}, "outputs": [], "source": ["import numpy as np\n", "from odps import ODPS\n", "from sklearn.model_selection import train_test_split\n", "import pandas as pd\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tJjdqu75w9fAqsfHTcY'\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")\n", "\n", "pd.set_option('display.max_rows', None)  # Set to None to display all rows\n", "pd.set_option('display.max_columns', None)  # Set to None to display all columns\n", "pd.set_option('display.width', None)  # Use to set the display width for wrapping\n", "pd.set_option('display.max_colwidth', None)  # Use to set the maximum width of each column\n", "\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "outputs": [], "source": ["sku_info_df = pd.read_csv('data/sku_list.csv')"], "metadata": {"collapsed": false}, "id": "db888e22c1b902f1", "execution_count": null}, {"cell_type": "code", "outputs": [], "source": ["sql = f\"\"\"\n", "SELECT  m.sku_id as sku\n", "        ,m.city_id as area_no\n", "        ,m.order_date as ds\n", "        ,t.price as price\n", "        ,m.sku_cnt as 销量\n", "        ,m.weighted_avg_price as weighted_avg_price\n", "        ,n.login_uv as login_uv\n", "FROM    (\n", "            SELECT  d.sku_id\n", "                    ,d.city_id\n", "                    ,d.order_date\n", "                    ,SUM(d.sku_cnt) AS sku_cnt\n", "                    ,ROUND(SUM(d.real_unit_amt * d.sku_cnt) / NULLIF(SUM(d.sku_cnt), 0), 2) AS weighted_avg_price\n", "            FROM    summerfarm_tech.dwd_trd_order_df d\n", "            LEFT JOIN summerfarm_tech.dim_cust_df c\n", "            ON      d.cust_id = c.cust_id\n", "            AND     c.ds = MAX_PT(\"summerfarm_tech.dim_cust_df\")\n", "            WHERE   d.ds = MAX_PT(\"summerfarm_tech.dwd_trd_order_df\")\n", "            AND     d.sku_id IN {tuple(sku_info_df['SKUID'].astype(str))}\n", "            AND     d.order_type NOT IN (1)\n", "            AND     c.cust_group NOT IN ('大客户', '批发客户')\n", "            AND     d.order_status IN (2,3,6)\n", "            GROUP BY d.sku_id\n", "                     ,d.order_date\n", "                     ,d.city_id\n", "        ) m\n", "LEFT JOIN   (\n", "                SELECT  sku_id\n", "                        ,city_id\n", "                        ,ds AS order_date\n", "                        ,COUNT(DISTINCT cust_id) AS login_uv\n", "                FROM    summerfarm_tech.dwd_log_mall_di\n", "                WHERE   ds <= MAX_PT('summerfarm_tech.dwd_log_mall_di')\n", "                GROUP BY sku_id\n", "                         ,city_id\n", "                         ,ds\n", "            ) n\n", "ON      m.sku_id = n.sku_id\n", "AND     m.city_id = n.city_id\n", "AND     m.order_date = n.order_date\n", "LEFT JOIN   (\n", "                SELECT  *\n", "                FROM    summerfarm_tech.ods_area_sku_df\n", "                WHERE   ds <= MAX_PT('summerfarm_tech.ods_area_sku_df')\n", "            ) t\n", "ON      m.sku_id = t.sku\n", "AND     m.city_id = t.area_no\n", "AND     m.order_date = t.ds\n", "where t.sku is not null\n", "AND   m.sku_cnt > 0\n", "AND   n.login_uv > 0\n", "\"\"\"\n", "\n", "instance = odps.execute_sql(sql)\n", "instance.wait_for_success()\n", "with instance.open_reader() as reader:\n", "    price_df = reader.to_pandas()"], "metadata": {"collapsed": false}, "id": "64da51aa2d268360", "execution_count": null}, {"cell_type": "code", "outputs": [], "source": ["price_df['ds'] = pd.to_datetime(price_df['ds'], format='%Y%m%d')\n", "price_df['price'] = price_df['price'].astype(float)\n", "price_df['area_no'] = price_df['area_no'].astype(str)\n", "price_df['sku'] = price_df['sku'].astype(str)\n", "price_df['销量'] = price_df['销量'].astype(int)\n", "price_df['login_uv'] = price_df['login_uv'].astype(int)\n", "price_df['weighted_avg_price'] = price_df['weighted_avg_price'].astype(float)"], "metadata": {"collapsed": false}, "id": "3f5cce7f01a40eef", "execution_count": null}, {"cell_type": "code", "outputs": [], "source": ["sql = f\"\"\"\n", "SELECT  TO_CHAR(a.expect_time,'yyyymmdd') AS date\n", "        ,a.sku_id\n", "        ,b.area_no\n", "        ,(\n", "                    CASE   WHEN SUM(real_sku_cnt) = 0 THEN AVG(unit_cost)\n", "                            ELSE ROUND(SUM(unit_cost * real_sku_cnt) / SUM(real_sku_cnt),2)\n", "                    END\n", "        ) AS cost\n", "FROM    (\n", "            SELECT  *\n", "            FROM    summerfarm_tech.dwd_stc_trade_df\n", "            WHERE   ds = MAX_PT('summerfarm_tech.dwd_stc_trade_df')\n", "            AND     type IN (51,57,58,63)\n", "            AND     expect_time IS NOT NULL\n", "            AND     TO_CHAR(expect_time,'yyyymmdd') >= '20230101'\n", "            AND     sku_id IN {tuple(sku_info_df['SKUID'].astype(str))}\n", "        ) a\n", "JOIN    (\n", "            SELECT  *\n", "            FROM    summerfarm_tech.ods_fence_df\n", "            WHERE   ds = MAX_PT('summerfarm_tech.ods_fence_df')\n", "            AND     status = 0\n", "        ) b\n", "ON      a.store_no = b.store_no\n", "GROUP BY TO_CHAR(a.expect_time,'yyyymmdd')\n", "         ,a.sku_id\n", "         ,b.area_no\n", "\"\"\"\n", "\n", "instance = odps.execute_sql(sql)\n", "instance.wait_for_success()\n", "with instance.open_reader() as reader:\n", "    cost_df = reader.to_pandas()"], "metadata": {"collapsed": false}, "id": "136873bda7bd7f0b", "execution_count": null}, {"cell_type": "code", "outputs": [], "source": ["cost_df['date'] = pd.to_datetime(cost_df['date'], format='%Y%m%d')\n", "cost_df['cost'] = cost_df['cost'].astype(float)\n", "cost_df['sku_id'] = cost_df['sku_id'].astype(str)\n", "cost_df['area_no'] = cost_df['area_no'].astype(str)\n", "\n", "# 排除成本为0的数据\n", "cost_df = cost_df[cost_df['cost'] > 0]"], "metadata": {"collapsed": false}, "id": "ea060a1ba0b111fe", "execution_count": null}, {"cell_type": "markdown", "source": ["# 数据预处理"], "metadata": {"collapsed": false}, "id": "761182b069c6cb95"}, {"cell_type": "code", "outputs": [], "source": ["date_range = pd.date_range(start=cost_df['date'].min(), end=cost_df['date'].max())\n", "\n", "\n", "# 对每个sku_id和area_no组合，确保每天都有记录\n", "def fill_missing_dates(group):\n", "    group = group.set_index('date').reindex(date_range, method='ffill').reset_index()\n", "    group['date'] = group['index']  # 将新的索引列赋值给date\n", "    return group.drop('index', axis=1)  # 删除旧的索引列\n", "\n", "\n", "cost_df.sort_values(by=['sku_id', 'area_no', 'date'], inplace=True)\n", "filled_df = cost_df.groupby(['sku_id', 'area_no']).apply(fill_missing_dates).reset_index(drop=True)\n", "filled_df.sort_values(by=['sku_id', 'area_no', 'date'], inplace=True)\n", "cost_df = filled_df"], "metadata": {"collapsed": false}, "id": "ced42b4597dbfcc8", "execution_count": null}, {"cell_type": "code", "outputs": [], "source": ["price_cost_df = price_df.merge(cost_df, left_on=['sku', 'area_no', 'ds'],\n", "                               right_on=['sku_id', 'area_no', 'date'], how='inner')"], "metadata": {"collapsed": false}, "id": "acd17ce23b2de7ff", "execution_count": null}, {"cell_type": "code", "outputs": [], "source": ["price_cost_df['profit'] = price_cost_df['weighted_avg_price'] - price_cost_df['cost']\n", "price_cost_df['revenue'] = price_cost_df['profit'] * price_cost_df['销量']\n", "\n", "price_cost_df.sort_values(by=['sku', 'area_no', 'date'], inplace=True)\n", "\n", "\n", "def calculate_rolling_average(group, window, column, new_column_name):\n", "    group[f'{window}_day_avg_{new_column_name}'] = group.apply(\n", "        lambda row: group[\n", "            (group['date'] < row['date']) &\n", "            (group['date'] >= row['date'] - pd.Timedel<PERSON>(days=window))\n", "            ][column].mean(), axis=1)\n", "    return group\n", "\n", "\n", "# 计算前7天滑动平均销量（不包括当天）\n", "price_cost_df = price_cost_df.groupby(['sku', 'area_no']).apply(\n", "    calculate_rolling_average, window=7, column='销量', new_column_name='sales').reset_index(\n", "    drop=True)\n", "\n", "# 计算前7日平均售价\n", "price_cost_df = price_cost_df.groupby(['sku', 'area_no']).apply(\n", "    calculate_rolling_average, window=7, column='weighted_avg_price',\n", "    new_column_name='price').reset_index(drop=True)\n", "\n", "# 计算前3日平均售价\n", "# price_cost_df['3_day_avg_price'] = price_cost_df.groupby(['sku', 'area_no'])[\n", "#     'weighted_avg_price'].transform(lambda x: x.shift(1).rolling(3).mean())\n", "price_cost_df = price_cost_df.groupby(['sku', 'area_no']).apply(\n", "    calculate_rolling_average, window=3, column='weighted_avg_price',\n", "    new_column_name='price').reset_index(drop=True)\n", "\n", "# 计算与昨日的价格差\n", "price_cost_df['price_diff'] = price_cost_df.groupby(['sku', 'area_no'])['weighted_avg_price'].diff()\n", "\n", "# 昨日销量\n", "price_cost_df['yesterday_sales'] = price_cost_df.groupby(['sku', 'area_no'])['销量'].shift(1)"], "metadata": {"collapsed": false}, "id": "e7fac7981f7a4634", "execution_count": null}, {"cell_type": "code", "outputs": [], "source": ["# 去掉一些极端值\n", "lower_bound, upper_bound = 0.05, 0.95\n", "\n", "# 去掉登录uv，销量，价格，成本的极端值\n", "price_cost_df = price_cost_df[price_cost_df['login_uv'] > 50]\n", "# price_cost_df = price_cost_df.groupby(['sku', 'area_no']).apply(lambda x: x[\n", "#     (x['销量'] > x['销量'].quantile(lower_bound)) &\n", "#     (x['销量'] < x['销量'].quantile(upper_bound)) &\n", "#     (x['price'] < x['price'].quantile(upper_bound)) &\n", "#     (x['cost'] > x['cost'].quantile(lower_bound)) &\n", "#     (x['cost'] < x['cost'].quantile(upper_bound)) &\n", "#     (x['weighted_avg_price'] > x['weighted_avg_price'].quantile(lower_bound))\n", "#     ]).reset_index(drop=True)\n", "\n", "# 去掉利润的极端值\n", "price_cost_df = price_cost_df[\n", "    (price_cost_df['profit'] < price_cost_df['profit'].quantile(upper_bound))\n", "    & (price_cost_df['profit'] > price_cost_df['profit'].quantile(lower_bound))]"], "metadata": {"collapsed": false}, "id": "51a9ee472d41dcd7", "execution_count": null}, {"cell_type": "code", "outputs": [], "source": ["price_cost_df['month'] = price_cost_df['ds'].dt.month_name()\n", "price_cost_df['day_of_week'] = price_cost_df['ds'].dt.day_name()\n", "\n", "# 对month, day_of_week进行one-hot编码\n", "month_dummies = pd.get_dummies(price_cost_df['month'], prefix='month')\n", "day_of_week_dummies = pd.get_dummies(price_cost_df['day_of_week'], prefix='day_of_week')\n", "\n", "price_cost_df = pd.concat([price_cost_df, month_dummies, day_of_week_dummies], axis=1)"], "metadata": {"collapsed": false}, "id": "bf43a4875a59bf73", "execution_count": null}, {"cell_type": "markdown", "source": ["# 模型训练"], "metadata": {"collapsed": false}, "id": "3699e0ab53205f9d"}, {"cell_type": "code", "outputs": [], "source": ["features = ([\n", "                'login_uv', 'weighted_avg_price', '7_day_avg_price', '3_day_avg_price',\n", "                'price_diff', 'cost', '7_day_avg_sales', 'yesterday_sales'\n", "            ]\n", "            + [col for col in price_cost_df.columns if 'area_no_' in col]\n", "            + [col for col in price_cost_df.columns if 'month_' in col]\n", "            + [col for col in price_cost_df.columns if 'day_of_week_' in col])"], "metadata": {"collapsed": false}, "id": "c1dd5499ab176567", "execution_count": null}, {"cell_type": "code", "outputs": [], "source": ["from sklearn.ensemble import RandomForestRegressor\n", "from matplotlib import pyplot as plt\n", "import plotly.graph_objects as go\n", "\n", "def visualize_random_forest(model, X, y, X_test, y_test):\n", "    # Feature Importance Visualization\n", "    feature_importance = pd.Series(model.feature_importances_, index=X.columns)\n", "    feature_importance_sorted = feature_importance.sort_values()\n", "\n", "    fig = go.Figure(go.Bar(\n", "        y=feature_importance_sorted.values,\n", "        x=feature_importance_sorted.index,\n", "        marker_color='lightblue'\n", "    ))\n", "\n", "    fig.update_layout(title='Feature Importance',\n", "                      yaxis_title='Importance',\n", "                      xaxis_title='Features',\n", "                      xaxis={'categoryorder':'total descending'})\n", "    fig.show()\n", "\n", "    # Actual vs Predicted Visualization\n", "    y_pred = model.predict(X_test)\n", "\n", "    # Creating scatter plot for actual vs predicted values\n", "    plt.figure(figsize=(10,10))\n", "    plt.scatter(y_test, y_pred, alpha=0.3, color=\"navy\")\n", "    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], color=\"limegreen\", linestyle='--', linewidth=2)\n", "    plt.xlabel('Actual')\n", "    plt.ylabel('Predicted')\n", "    plt.title('Actual vs Predicted')\n", "    plt.show()\n", "\n", "def random_forest(dataframe):\n", "    X = dataframe[features]\n", "    y = dataframe['销量']\n", "\n", "    # drop any na values in X and y\n", "    X = X.dropna()\n", "    y = y[X.index]\n", "\n", "    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.1, random_state=42)\n", "\n", "    model = RandomForestRegressor(n_estimators=300, max_depth=10, random_state=42)\n", "    model.fit(X_train, y_train)\n", "    \n", "    visualize_random_forest(model, X_train, y_train, X_test, y_test)\n", "\n", "    return model, X_train, model.score(X_train, y_train)"], "metadata": {"collapsed": false}, "id": "a082bac197c7cfa6", "execution_count": null}, {"cell_type": "code", "outputs": [], "source": ["def predict(model, dataframe):\n", "    X = dataframe[features]\n", "\n", "    return model.predict(X), X"], "metadata": {"collapsed": false}, "id": "c7f92f40c3c12e5b", "execution_count": null}, {"cell_type": "code", "outputs": [], "source": ["results = []\n", "\n", "for sku in price_cost_df['sku'].unique():\n", "\n", "    # 为每个sku训练模型\n", "    sku_df = price_cost_df[price_cost_df['sku'] == sku]\n", "    sku_df = sku_df.dropna()\n", "    area_no_dummies = pd.get_dummies(sku_df['area_no'], prefix='area_no')\n", "    sku_df = pd.concat([sku_df, area_no_dummies], axis=1)\n", "\n", "    spu_name = sku_info_df[sku_info_df['SKUID'] == sku]['商品名称'].values[0]\n", "    print(f'商品名称: {spu_name} - {sku}')\n", "    try:\n", "        rf, sku_X_train, sku_model_score = random_forest(sku_df)\n", "        print(f'模型训练完成，模型得分: {sku_model_score}')\n", "    except Exception as e:\n", "        print('模型训练失败', e)\n", "        continue\n", "\n", "    # 对每个sku的每个area_no组合，修改价格，预测销量\n", "    for area_no in sku_df['area_no'].unique():\n", "        area_no_df = sku_df[sku_df['area_no'] == area_no]\n", "        area_no_df = area_no_df.dropna()\n", "\n", "        max_date = area_no_df['ds'].max()\n", "        area_no_df = area_no_df[area_no_df['ds'] == max_date]\n", "\n", "        y_hat, area_no_X = predict(rf, area_no_df)\n", "\n", "        # 对价格进行微调\n", "        for price_range in np.arange(-0.15, 0.15, 0.01):\n", "            modified_df = area_no_df.copy()\n", "            modified_price = area_no_df['weighted_avg_price'] * price_range\n", "            modified_df['weighted_avg_price'] += modified_price\n", "            modified_df['price_diff'] += modified_price\n", "\n", "            y_hat_modified, modified_X = predict(rf, modified_df)\n", "\n", "            results.append({\n", "                'sku': sku,\n", "                'area_no': area_no,\n", "                'weighted_avg_price': area_no_df['weighted_avg_price'].values[0],\n", "                'modified_weighted_avg_price': modified_df['weighted_avg_price'].values[0],\n", "                'modified_price_range': price_range,\n", "                'price_diff': modified_df['price_diff'].values[0],\n", "                'y_actual': area_no_df['销量'].values[0],\n", "                'y_hat': y_hat[0],\n", "                'y_hat_modified': y_hat_modified[0],\n", "                'model_score': sku_model_score,\n", "                'login_uv': area_no_df['login_uv'].values[0],\n", "                '7_day_avg_price': area_no_df['7_day_avg_price'].values[0],\n", "                '7_day_avg_sales': area_no_df['7_day_avg_sales'].values[0],\n", "                '3_day_avg_price': area_no_df['3_day_avg_price'].values[0],\n", "                'cost': area_no_df['cost'].values[0],\n", "                'date': max_date,\n", "                'day_of_week': area_no_df['day_of_week'].values[0],\n", "                'month': area_no_df['month'].values[0],\n", "                'model': 'random_forest'\n", "            })\n", "            \n", "    print('----------------------\\n\\n')\n", "            \n", "\n", "result_df = pd.DataFrame(results)"], "metadata": {"collapsed": false}, "id": "74411908adf1718d", "execution_count": null}, {"cell_type": "code", "outputs": [], "source": ["# 保存结果至odps\n", "from odps import DataFrame\n", "\n", "odps_df = DataFrame(result_df)\n", "yesterday_str = (pd.to_datetime('today') - pd.Timedelta(days=1)).strftime('%Y%m%d')\n", "partition_spec = f\"ds={yesterday_str}\"\n", "table_name = 'summerfarm_ds.app_price_optimization_result_df'\n", "\n", "# odps_df.persist(table_name, partition=partition_spec, drop_partition=False, create_partition=True, overwrite=False)\n", "\n", "# for i in range(3):\n", "#     try:\n", "#         odps_df.persist(table_name, partition=partition_spec, drop_partition=False, create_partition=True, overwrite=False)\n", "#         print(f'数据已成功写入表{table_name}，分区{partition_spec}')\n", "#         break\n", "#     except Exception as e:\n", "#         print(e)\n", "#         time.sleep(10)"], "metadata": {"collapsed": false}, "id": "8d0637440794e1d9", "execution_count": null}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}