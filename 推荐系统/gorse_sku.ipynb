{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from odps import ODPS\n", "import pandas as pd\n", "from gorse import Gorse\n", "import requests\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tJjdqu75w9fAqsfHTcY'\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 用户"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT  dcd.cust_id\n", "        ,dcd.cust_type\n", "        ,dcd.cust_name\n", "        ,dcd.cust_phone\n", "        ,dcd.city_name\n", "FROM    summerfarm_tech.dim_cust_df dcd\n", "JOIN    summerfarm_tech.ods_merchant_df omd\n", "ON      dcd.cust_id = omd.m_id\n", "AND     omd.ds = MAX_PT('summerfarm_tech.ods_merchant_df')\n", "WHERE   dcd.ds = 20231221\n", "AND     dcd.ds BETWEEN start_at AND end_at\n", "AND     omd.login_time >= CAST(DATE_ADD(GETDATE(),-180) AS DATETIME)\n", "GROUP BY dcd.cust_id\n", "         ,dcd.cust_type\n", "         ,dcd.cust_phone\n", "         ,dcd.cust_name\n", "         ,dcd.city_name\n", "\"\"\"\n", "\n", "instance = odps.execute_sql(query)\n", "instance.wait_for_success()\n", "with instance.open_reader() as reader:\n", "    user_df = reader.to_pandas()\n", "\n", "user_df['cust_id'] = user_df['cust_id'].astype(str)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT  distinct SPLIT_PART(a.phone,'_',1,1) AS phone\n", "FROM    (\n", "            SELECT  *\n", "            FROM    summerfarm_tech.ods_auth_user_base_df\n", "            WHERE   ds = MAX_PT('summerfarm_tech.ods_auth_user_base_df')\n", "            AND     username LIKE '%@summerfarm.net%'\n", "            AND     nickname NOT LIKE '%公司%'\n", "        ) a\n", "JOIN    (\n", "            SELECT  *\n", "            FROM    summerfarm_tech.ods_auth_user_df\n", "            WHERE   ds = MAX_PT('summerfarm_tech.ods_auth_user_df')\n", "            AND     system_origin = 2\n", "        ) b\n", "ON      a.id = b.user_base_id\n", "\"\"\"\n", "\n", "instance = odps.execute_sql(query)\n", "instance.wait_for_success()\n", "with instance.open_reader() as reader:\n", "    inner_user_df = reader.to_pandas()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["user_df = user_df[~user_df['cust_phone'].isin(inner_user_df['phone'])]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["user_df['labels'] = user_df.apply(lambda row: [row['cust_type']] + [row['city_name']], axis=1)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["users_list = user_df.apply(lambda row: {\"UserId\": row[\"cust_id\"], \n", "                                   \"Labels\": row['labels']}, axis=1).tolist()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["users_list_df = pd.DataFrame(users_list)\n", "users_list_df['Labels'] = users_list_df['Labels'].apply(lambda x: '|'.join(x))\n", "users_list_df.to_csv(\"users_list.csv\", index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 物品"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT  sku_id\n", "        ,other_properties\n", "        ,spu_name\n", "        ,category1\n", "        ,category4\n", "        ,create_time\n", "FROM    summerfarm_tech.dim_sku_df\n", "WHERE   ds = 20231221\n", "AND     sku_type in (0, 2)\n", "\"\"\"\n", "\n", "instance = odps.execute_sql(query)\n", "instance.wait_for_success()\n", "with instance.open_reader() as reader:\n", "    sku_properties_df = reader.to_pandas()\n", "\n", "sku_properties_df['sku_id'] = sku_properties_df['sku_id'].astype(str)\n", "sku_properties_df['create_time'] = sku_properties_df['create_time'].astype(str)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def is_json(json_string):\n", "    try:\n", "        json_object = json.loads(json_string)\n", "    except Exception as e:\n", "        return False\n", "    return True\n", "\n", "sku_properties_df['other_properties'] = sku_properties_df['other_properties'].apply(\n", "    lambda x: [f\"{k.replace('/', '-')}:{str(v).replace('/', '-')}\" for k, v in json.loads(x).items()] \n", "               if is_json(x) and isinstance(json.loads(x), dict) and json.loads(x) \n", "               else []\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# also add the category str to the other_properties list\n", "sku_properties_df['properties'] = sku_properties_df.apply(\n", "    (lambda row: row['other_properties'] + row['category4'].split(',')),\n", "    axis=1\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["item_list = sku_properties_df.apply(lambda row: {\"ItemId\": row['sku_id'],\n", "                                      \"Comment\": row['spu_name'],\n", "                                      \"IsHidden\": <PERSON><PERSON><PERSON>,\n", "                                      \"Labels\": row['properties'],\n", "                                      \"Category\": row['category1'],\n", "                                      \"Timestamp\": row['create_time']}, axis=1).tolist()\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# export item_list to csv\n", "item_list_df = pd.DataFrame(item_list)\n", "\n", "item_list_df['Labels'] = item_list_df['Labels'].apply(lambda x: '|'.join(x))\n", "\n", "# Export item_list_df to a CSV file\n", "item_list_df.to_csv('item_list.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 已读反馈"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT  cust_id\n", "        ,sku_id\n", "        ,time\n", "        ,envent_type\n", "FROM    summerfarm_tech.dwd_log_mall_di\n", "WHERE   cust_id IS NOT NULL\n", "AND     spu_id IS NOT NULL\n", "AND     envent_type IN ('pv', 'view')\n", "AND     ds >= '20231201'\n", "AND     time IS NOT NULL\n", "\"\"\"\n", "\n", "instance = odps.execute_sql(query)\n", "instance.wait_for_success()\n", "with instance.open_reader() as reader:\n", "    event_df = reader.to_pandas()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["event_df['cust_id'] = event_df['cust_id'].astype(str)\n", "event_df['sku_id'] = event_df['sku_id'].astype(str)\n", "event_df['time'] = event_df['time'].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["event_df = event_df[event_df['sku_id'].isin(sku_properties_df['sku_id'])]\n", "event_df = event_df[event_df['cust_id'].isin(user_df['cust_id'])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pv_list = event_df.apply(lambda row: {\n", "    'UserId': row['cust_id'],\n", "    'ItemId': row['sku_id'],\n", "    'Timestamp': row['time'],\n", "    'FeedbackType': 'pv'\n", "}, axis=1).tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pv_df = pd.DataFrame(pv_list)\n", "pv_df.to_csv('pv.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 购买反馈"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT  cust_id\n", "        ,sku_id\n", "        ,finish_date\n", "FROM    summerfarm_tech.dwd_dlv_delivery_cost_di\n", "WHERE   ds >= '20231201'\n", "\"\"\"\n", "\n", "instance = odps.execute_sql(query)\n", "instance.wait_for_success()\n", "with instance.open_reader() as reader:\n", "    order_df = reader.to_pandas()\n", "\n", "order_df['cust_id'] = order_df['cust_id'].astype(str)\n", "order_df['sku_id'] = order_df['sku_id'].astype(str)\n", "order_df['finish_date'] = order_df['finish_date'].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["order_df = order_df[order_df['sku_id'].isin(sku_properties_df['sku_id'])]\n", "order_df = order_df[order_df['cust_id'].isin(user_df['cust_id'])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["feedback_list = order_df.apply(lambda row: {\"UserId\": row[\"cust_id\"],\n", "                                            \"ItemId\": row[\"sku_id\"],\n", "                                            \"FeedbackType\": \"paid\",\n", "                                            \"Timestamp\": row[\"finish_date\"]}, axis=1).tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["feedback_df = pd.DataFrame(feedback_list)\n", "feedback_df = feedback_df[~feedback_df['Timestamp'].isna()]\n", "feedback_df = feedback_df[feedback_df['Timestamp'] != 'NaT']\n", "feedback_df.to_csv('feedback.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 点击反馈"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT  cust_id\n", "        ,sku_id\n", "        ,time\n", "        ,envent_type\n", "FROM    summerfarm_tech.dwd_log_mall_di\n", "WHERE   cust_id IS NOT NULL\n", "AND     spu_id IS NOT NULL\n", "AND     envent_type = 'cl'\n", "AND     ds >= '20231201'\n", "AND     time IS NOT NULL\n", "\"\"\"\n", "\n", "instance = odps.execute_sql(query)\n", "instance.wait_for_success()\n", "with instance.open_reader() as reader:\n", "    cl_df = reader.to_pandas()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cl_df['cust_id'] = cl_df['cust_id'].astype(str)\n", "cl_df['sku_id'] = cl_df['sku_id'].astype(str)\n", "cl_df['time'] = cl_df['time'].astype(str)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cl_df = cl_df[cl_df['sku_id'].isin(sku_properties_df['sku_id'])]\n", "cl_df = cl_df[cl_df['cust_id'].isin(user_df['cust_id'])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["cl_list = cl_df.apply(lambda row: {\n", "    'UserId': row['cust_id'],\n", "    'ItemId': row['sku_id'],\n", "    'Timestamp': row['time'],\n", "    'FeedbackType': 'cl'\n", "}, axis=1).tolist()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["positive_df = pd.DataFrame(cl_list)\n", "positive_df.to_csv('cl.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 筛选"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["event_df = pd.read_csv('pv.csv')\n", "event_df['UserId'] = event_df['UserId'].astype(str)\n", "event_df['ItemId'] = event_df['ItemId'].astype(str)\n", "event_df['Timestamp'] = event_df['Timestamp'].astype(str)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["event_df = event_df[event_df['ItemId'].isin(sku_properties_df['sku_id'])]\n", "event_df = event_df[event_df['UserId'].isin(user_df['cust_id'])]"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["event_df.to_csv('pv.csv', index=False)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["cl_df = pd.read_csv('cl.csv')\n", "cl_df['UserId'] = cl_df['UserId'].astype(str)\n", "cl_df['ItemId'] = cl_df['ItemId'].astype(str)\n", "cl_df['Timestamp'] = cl_df['Timestamp'].astype(str)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["cl_df = cl_df[cl_df['ItemId'].isin(sku_properties_df['sku_id'])]\n", "cl_df = cl_df[cl_df['UserId'].isin(user_df['cust_id'])]"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["cl_df.to_csv('cl.csv', index=False)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}