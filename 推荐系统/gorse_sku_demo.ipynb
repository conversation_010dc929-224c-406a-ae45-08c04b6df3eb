{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### 准备数据"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from odps import ODPS\n", "import pandas as pd\n", "from gorse import Gorse\n", "import requests\n", "import json\n", "from IPython.display import display, HTML"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tJjdqu75w9fAqsfHTcY'\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT  cust_id\n", "        ,cust_type\n", "        ,cust_name\n", "FROM    summerfarm_tech.dim_cust_df\n", "WHERE   ds = 20231221\n", "AND     ds BETWEEN start_at AND end_at\n", "\"\"\"\n", "\n", "instance = odps.execute_sql(query)\n", "instance.wait_for_success()\n", "with instance.open_reader() as reader:\n", "    cust_df = reader.to_pandas()\n", "\n", "cust_df['cust_id'] = cust_df['cust_id'].astype(str)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT  sku_id\n", "        ,avg_cost\n", "        ,img\n", "        ,sku_full_name\n", "        ,sku_full_name_with_weight\n", "        ,pd_name\n", "        ,root_category\n", "        ,parent_category\n", "        ,category\n", "FROM    temp_zhenghao_sku_detail\n", "GROUP BY sku_id\n", "         ,avg_cost\n", "         ,img\n", "         ,sku_full_name\n", "         ,sku_full_name_with_weight\n", "         ,pd_name\n", "         ,root_category\n", "         ,parent_category\n", "         ,category\n", "\"\"\"\n", "\n", "instance = odps.execute_sql(query)\n", "instance.wait_for_success()\n", "with instance.open_reader() as reader:\n", "    sku_df = reader.to_pandas()\n", "\n", "sku_df['sku_id'] = sku_df['sku_id'].astype(str)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["client = Gorse('http://127.0.0.1:8087', 'api_key')\n", "\n", "url = 'http://localhost:8088/api'\n", "headers = {\"Content-Type\": \"application/json\"}"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["def change_recommend(user_id, value, token):\n", "    u = f\"{url}/dashboard/recommend/{user_id}/{value}\"\n", "    params = {\n", "        'n': 10\n", "    }\n", "\n", "    cookies = {\n", "        'session': token\n", "    }\n", "\n", "    response = requests.get(u, params=params, cookies=cookies)\n", "\n", "    if response.ok:\n", "        items = response.json()\n", "        return items\n", "    else:\n", "        return None"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def display_row(row):\n", "    html = f\"\"\"\n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; margin:10px; display:flex;\">\n", "        <div style=\"width:150px; height:150px; margin-right:20px;\">\n", "            <img src=\"{row['img']}\" style=\"width:100%; height:100%; object-fit:contain;\">\n", "        </div>\n", "        <div>\n", "            <h4>{row['sku_full_name']}</h4>\n", "            <p><b>SKU ID:</b> {row['sku_id']}</p>\n", "            <p><b>Average Cost:</b> {row['avg_cost']}</p>\n", "            <p><b>Product Name:</b> {row['pd_name']}</p>\n", "            <p><b>Root Category:</b> {row['root_category']}</p>\n", "            <p><b>Parent Category:</b> {row['parent_category']}</p>\n", "            <p><b>Category:</b> {row['category']}</p>\n", "        </div>\n", "    </div>\n", "    \"\"\"\n", "    display(HTML(html))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def create_row_html(row):\n", "    return f\"\"\"\n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"{row['img']}\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">{row['sku_full_name']}</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> {row['sku_id']}</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> {row['avg_cost']}</p>\n", "        <p><b>Product Name:</b> {row['pd_name']}</p>\n", "        <p><b>Root Category:</b> {row['root_category']}</p>\n", "        <p><b>Parent Category:</b> {row['parent_category']}</p>\n", "        <p><b>Category:</b> {row['category']}</p>\n", "    </div>\n", "    \"\"\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 推荐"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["cust_id = '205'\n", "recommended = client.get_recommend(cust_id, n=10)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cust_id</th>\n", "      <th>cust_type</th>\n", "      <th>cust_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>699</th>\n", "      <td>205</td>\n", "      <td>面包蛋糕</td>\n", "      <td>今至烘焙</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    cust_id cust_type cust_name\n", "699     205      面包蛋糕      今至烘焙"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cust = cust_df[cust_df['cust_id'] == cust_id]\n", "display(cust)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["recommended_df = sku_df[sku_df['sku_id'].isin(recommended)]"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"data": {"text/html": ["<div style=\"display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;\">\n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/picture-path/42eqou8ra0o7pbl4i.jpg\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">青凯特芒（纸箱装）</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 533418813323</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 65.59</p>\n", "        <p><b>Product Name:</b> 青凯特芒（纸箱装）</p>\n", "        <p><b>Root Category:</b> 新鲜水果</p>\n", "        <p><b>Parent Category:</b> 核果类</p>\n", "        <p><b>Category:</b> 芒果</p>\n", "    </div>\n", "    \n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/picture-path/715fqd9it5q2g375s.jpg\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">章姬奶油草莓</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 5445065458</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 19.15</p>\n", "        <p><b>Product Name:</b> 章姬奶油草莓</p>\n", "        <p><b>Root Category:</b> 新鲜水果</p>\n", "        <p><b>Parent Category:</b> 浆果类</p>\n", "        <p><b>Category:</b> 草莓</p>\n", "    </div>\n", "    \n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/picture-path/715fqd9it5q2g375s.jpg\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">章姬奶油草莓,章姬奶油草莓</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 5445065804</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 39.69</p>\n", "        <p><b>Product Name:</b> 章姬奶油草莓</p>\n", "        <p><b>Root Category:</b> 新鲜水果</p>\n", "        <p><b>Parent Category:</b> 浆果类</p>\n", "        <p><b>Category:</b> 草莓</p>\n", "    </div>\n", "    \n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">平安果-llc,乐乐茶专用</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 577061661644</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 3.98</p>\n", "        <p><b>Product Name:</b> 平安果-llc</p>\n", "        <p><b>Root Category:</b> 新鲜水果</p>\n", "        <p><b>Parent Category:</b> 仁果类</p>\n", "        <p><b>Category:</b> 苹果</p>\n", "    </div>\n", "    \n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/picture-path/h7d3ey42fv5aqtbht.png\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">平安果</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 577318003814</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 90.0</p>\n", "        <p><b>Product Name:</b> 平安果</p>\n", "        <p><b>Root Category:</b> 新鲜水果</p>\n", "        <p><b>Parent Category:</b> 仁果类</p>\n", "        <p><b>Category:</b> 苹果</p>\n", "    </div>\n", "    \n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/picture-path/nxo0lww5my1umooe.png\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">C味冷冻芋头块,C味冷冻芋头块</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 777887102237</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 165.7</p>\n", "        <p><b>Product Name:</b> C味冷冻芋头块</p>\n", "        <p><b>Root Category:</b> 蔬菜制品</p>\n", "        <p><b>Parent Category:</b> 冷冻蔬菜</p>\n", "        <p><b>Category:</b> 冷冻生蔬菜制品</p>\n", "    </div>\n", "    \n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/sku-picture/g05xgr3tcsd76pg88.jpeg\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">舒可曼糖霜</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 796055317616</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 149.8</p>\n", "        <p><b>Product Name:</b> 舒可曼糖霜</p>\n", "        <p><b>Root Category:</b> 糖丨糖制品</p>\n", "        <p><b>Parent Category:</b> 糖粉</p>\n", "        <p><b>Category:</b> 白糖粉</p>\n", "    </div>\n", "    \n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/sku-picture/smbrnngwm3emeggx1.jpg\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">芳蜜圆水果条包,金桔柠檬</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 831772833161</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 0.0</p>\n", "        <p><b>Product Name:</b> 芳蜜圆水果条包</p>\n", "        <p><b>Root Category:</b> 饮品原料</p>\n", "        <p><b>Parent Category:</b> 果汁原料</p>\n", "        <p><b>Category:</b> 果汁浓浆</p>\n", "    </div>\n", "    </div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["html = '<div style=\"display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;\">'\n", "for index, row in recommended_df.iterrows():\n", "    html += create_row_html(row)\n", "\n", "html += '</div>'\n", "display(HTML(html))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 按算法推荐"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# 'collaborative', 'user_based', 'item_based'\n", "value = 'collaborative'\n", "token = 'MTcwNDE2MDg2MHxiNVE4VmVCNHVnY1BsbHlobE9ucjVFc3MyYzM5azZCbThvX0pWNEZZOWEyNnNBblY5cWpaNjA1T3hidDF3Y1BIdVdvV2xRa1NMSG0zLTN0NHFWMXJiaHpfQ1E9PXymuK5PRWYhdAaBqeQtmAM9vx66biKNbn37q0FmEM6h2A=='\n", "\n", "items = change_recommend(cust_id, value, token)\n", "\n", "ids = [item['ItemId'] for item in items]\n", "recommended_by_algo_df = sku_df[sku_df['sku_id'].isin(ids)]"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/html": ["<div style=\"display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;\">\n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/picture-path/0lxnzkjidu3g8qbfs.jpeg\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">嘉利宝黑巧克力57.9%</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 11721810062</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 184.55</p>\n", "        <p><b>Product Name:</b> 嘉利宝黑巧克力57.9%</p>\n", "        <p><b>Root Category:</b> 饼干丨糖果丨可可豆制品</p>\n", "        <p><b>Parent Category:</b> 可可豆制品</p>\n", "        <p><b>Category:</b> 巧克力</p>\n", "    </div>\n", "    \n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/picture-path/juqum6g4lulrlnyq.jpg\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">丹东红颜草莓</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 17120703065</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 10.13</p>\n", "        <p><b>Product Name:</b> 丹东红颜草莓</p>\n", "        <p><b>Root Category:</b> 新鲜水果</p>\n", "        <p><b>Parent Category:</b> 浆果类</p>\n", "        <p><b>Category:</b> 草莓</p>\n", "    </div>\n", "    \n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/picture-path/juqum6g4lulrlnyq.jpg\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">丹东红颜草莓</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 17120703704</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 10.38</p>\n", "        <p><b>Product Name:</b> 丹东红颜草莓</p>\n", "        <p><b>Root Category:</b> 新鲜水果</p>\n", "        <p><b>Parent Category:</b> 浆果类</p>\n", "        <p><b>Category:</b> 草莓</p>\n", "    </div>\n", "    \n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/picture-path/73ygqokfuwi38clqe.png\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">美玫低筋粉(布袋)</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 3842582316</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 151.92</p>\n", "        <p><b>Product Name:</b> 美玫低筋粉(布袋)</p>\n", "        <p><b>Root Category:</b> 谷物制品</p>\n", "        <p><b>Parent Category:</b> 面粉丨小麦粉</p>\n", "        <p><b>Category:</b> 低筋面粉</p>\n", "    </div>\n", "    \n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/picture-path/j0zocnob4clkh83d.jpg\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">金装金像高筋粉</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 3880428785</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 154.49</p>\n", "        <p><b>Product Name:</b> 金装金像高筋粉</p>\n", "        <p><b>Root Category:</b> 谷物制品</p>\n", "        <p><b>Parent Category:</b> 面粉丨小麦粉</p>\n", "        <p><b>Category:</b> 高筋面粉</p>\n", "    </div>\n", "    \n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/picture-path/5q156h20ecjdigyqw.jpg\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">青凯特芒</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 5432522553</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 44.65</p>\n", "        <p><b>Product Name:</b> 青凯特芒</p>\n", "        <p><b>Root Category:</b> 新鲜水果</p>\n", "        <p><b>Parent Category:</b> 核果类</p>\n", "        <p><b>Category:</b> 芒果</p>\n", "    </div>\n", "    \n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/1531125160222\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">蒙特瑞草莓</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 611184621</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 46.27</p>\n", "        <p><b>Product Name:</b> 蒙特瑞草莓</p>\n", "        <p><b>Root Category:</b> 新鲜水果</p>\n", "        <p><b>Parent Category:</b> 浆果类</p>\n", "        <p><b>Category:</b> 草莓</p>\n", "    </div>\n", "    \n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/picture-path/15481442931540\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">红颜草莓</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> 6416</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 10.25</p>\n", "        <p><b>Product Name:</b> 红颜草莓</p>\n", "        <p><b>Root Category:</b> 新鲜水果</p>\n", "        <p><b>Parent Category:</b> 浆果类</p>\n", "        <p><b>Category:</b> 草莓</p>\n", "    </div>\n", "    \n", "    <div style=\"border:1px solid #e1e1e1; padding:10px; display:flex; flex-direction: column; align-items: center;\">\n", "        <img src=\"https://azure.summerfarm.net/sku_list1494574983602682\" style=\"width:130px; height:130px; object-fit:contain; margin-bottom: 10px;\">\n", "        <h4 style=\"margin: 5px 0;\">安佳无盐黄油5KG</h4>\n", "        <p style=\"margin: 5px 0;\"><b>SKU ID:</b> N001H01Y005</p>\n", "        <p style=\"margin: 5px 0;\"><b>Average Cost:</b> 265.31</p>\n", "        <p><b>Product Name:</b> 安佳无盐黄油5KG</p>\n", "        <p><b>Root Category:</b> 乳制品</p>\n", "        <p><b>Parent Category:</b> 黄油</p>\n", "        <p><b>Category:</b> 无盐黄油</p>\n", "    </div>\n", "    </div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["html = '<div style=\"display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;\">'\n", "for index, row in recommended_by_algo_df.iterrows():\n", "    html += create_row_html(row)\n", "\n", "html += '</div>'\n", "display(HTML(html))"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}