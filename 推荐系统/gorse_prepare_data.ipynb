{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from odps import ODPS\n", "import pandas as pd\n", "from gorse import Gorse\n", "import requests\n", "import json\n", "from datetime import datetime,timedelta"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tJjdqu75w9fAqsfHTcY'\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_time = (datetime.now()-<PERSON><PERSON><PERSON>(90)).strftime('%Y%m%d')\n", "start_time_yyyymmdd = (datetime.now()-<PERSON><PERSON><PERSON>(90)).strftime('%Y-%m-%d')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 用户"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "SELECT  cust.cust_id\n", "        ,cust.city_name\n", "        ,cust.cust_type\n", "        ,cust.cust_nature\n", "        ,cust.brand_name\n", "        ,cust.cust_class\n", "        ,cust.cust_group\n", "        ,merchant.*\n", "        ,o.total_orders\n", "        ,o.total_price\n", "        ,o.first_order_time\n", "FROM    summerfarm_tech.dim_cust_df cust\n", "LEFT JOIN   (\n", "                SELECT  m_id\n", "                        ,COUNT(DISTINCT order_no) AS total_orders\n", "                        ,SUM(total_price) AS total_price\n", "                        ,MIN(o.order_time) AS first_order_time\n", "                        ,MAX(o.order_time) AS last_order_time\n", "                FROM    summerfarm_tech.ods_orders_df o\n", "                WHERE   o.status IN (2,3,6)\n", "                AND     o.ds = MAX_PT('summerfarm_tech.ods_orders_df')\n", "                AND     o.order_time >= '2020-01-01 00:00:00'\n", "                GROUP BY m_id\n", "            ) o\n", "ON      o.m_id = cust.cust_id\n", "INNER JOIN  (\n", "                SELECT  m_id\n", "                        ,login_time\n", "                        ,register_time\n", "                        ,update_time\n", "                        ,last_order_time\n", "                        ,DATEDIFF(CURRENT_DATE(),last_order_time) days_last_order\n", "                        ,CASE   WHEN member_integral <= 0 THEN '无'\n", "                                WHEN member_integral <= 1000.00 THEN '1K积分'\n", "                                WHEN member_integral <= 3000.00 THEN '3K积分'\n", "                                WHEN member_integral <= 5000.00 THEN '5K积分'\n", "                                WHEN member_integral <= 10000.00 THEN '10K积分'\n", "                                ELSE '大于10K积分'\n", "                        END AS `会员当月积分`\n", "                        ,`size`\n", "                        ,`type`\n", "                        ,CASE   WHEN `grade` >= 0 THEN CONCAT('会员等级:',`grade`)\n", "                                ELSE '非会员'\n", "                        END AS `会员等级`\n", "                        ,CASE   WHEN direct = 1 THEN '直营'\n", "                                WHEN direct = 2 THEN '加盟'\n", "                                ELSE `size`\n", "                        END AS is_direct\n", "                FROM    summerfarm_tech.ods_merchant_df\n", "                WHERE   ds = MAX_PT('summerfarm_tech.ods_merchant_df')\n", "                AND     login_time IS NOT NULL\n", "                AND     login_time >= '2020-01-01 00:00:00'\n", "                AND     pull_black_operator IS NULL\n", "            ) merchant\n", "ON      merchant.m_id = cust.cust_id\n", "WHERE   ds = MAX_PT('summerfarm_tech.dim_cust_df')\n", "AND     end_at = 99991231\n", "\"\"\"\n", "\n", "instance = odps.execute_sql(query)\n", "instance.wait_for_success()\n", "with instance.open_reader() as reader:\n", "    user_df = reader.to_pandas()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 已读反馈"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "select  cust_id,cust_name,city_name,sku_id,max(time) as time\n", "FROM    summerfarm_tech.dwd_log_mall_di\n", "WHERE   cust_id IS NOT NULL\n", "AND     sku_id in (\n", "                        SELECT  sku_id\n", "                        FROM    summerfarm_tech.dim_sku_df\n", "                        WHERE   ds = MAX_PT('summerfarm_tech.dim_sku_df')\n", "                        AND     category2 NOT LIKE '%测试%'\n", "                        AND     category4 NOT LIKE '%测试%'\n", "                        AND     category4 NOT LIKE '%帆台%'\n", "                        AND     category2 NOT LIKE '%帆台%'\n", "                        AND     category2 != '无'\n", "                        AND     sub_type != 4\n", "                        AND     sku_type != 1\n", "                        AND     outdated != 1)\n", "AND     spu_id IS NOT NULL\n", "AND     envent_type in ('cl','pv')\n", "AND     ds >= '{start_time}'\n", "AND     time IS NOT NULL\n", "GROUP BY cust_id,cust_name,city_name,sku_id\n", "\"\"\"\n", "\n", "instance = odps.execute_sql(query)\n", "instance.wait_for_success()\n", "with instance.open_reader() as reader:\n", "    event_df = reader.to_pandas()\n", "\n", "event_df['cust_id'] = event_df['cust_id'].astype(str)\n", "event_df['sku_id'] = event_df['sku_id'].astype(str)\n", "event_df['time'] = event_df['time'].astype(str)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["click_list = event_df.apply(lambda row: {\n", "    'UserId': row['cust_id'],\n", "    'ItemId': row['sku_id'],\n", "    'Timestamp': row['time'],\n", "    'FeedbackType': 'click'\n", "}, axis=1).tolist()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["click_df = pd.DataFrame(click_list)\n", "click_df = click_df.sort_values(by='Timestamp', ascending=True)\n", "click_df.to_csv('click.csv', index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 购买反馈"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["query = f\"\"\"\n", "SELECT  item.sku AS sku_id\n", "        ,o.m_id AS cust_id\n", "        ,MAX(o.order_time) AS order_time\n", "FROM    summerfarm_tech.ods_order_item_df item\n", "INNER JOIN summerfarm_tech.ods_orders_df o\n", "ON      o.order_no = item.order_no\n", "AND     o.ds = MAX_PT('summerfarm_tech.ods_orders_df')\n", "INNER JOIN (\n", "        SELECT  sku_id \n", "        FROM    summerfarm_tech.dim_sku_df\n", "        WHERE   ds = MAX_PT('summerfarm_tech.dim_sku_df')\n", "        AND     category2 NOT LIKE '%测试%'\n", "        AND     category4 NOT LIKE '%测试%'\n", "        AND     category4 NOT LIKE '%帆台%'\n", "        AND     category2 NOT LIKE '%帆台%'\n", "        AND     category2 != '无'\n", "        AND     sub_type != 4\n", "        AND     sku_type != 1\n", "        AND     outdated != 1\n", ")items ON items.sku_id = item.sku\n", "WHERE   item.ds = MAX_PT('summerfarm_tech.ods_order_item_df')\n", "AND     o.order_time >= '{start_time_yyyymmdd} 00:00:00'\n", "GROUP BY item.sku\n", "         ,o.m_id\n", "\"\"\"\n", "\n", "instance = odps.execute_sql(query)\n", "instance.wait_for_success()\n", "with instance.open_reader() as reader:\n", "    order_df = reader.to_pandas()\n", "\n", "order_df['cust_id'] = order_df['cust_id'].astype(str)\n", "order_df['sku_id'] = order_df['sku_id'].astype(str)\n", "order_df['order_time'] = order_df['order_time'].astype(str)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["buy_list = order_df.apply(lambda row: {\n", "    'UserId': row['cust_id'],\n", "    'ItemId': row['sku_id'],\n", "    'Timestamp': row['order_time'],\n", "    'FeedbackType': 'buy'\n", "}, axis=1).tolist()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["buy_df = pd.DataFrame(buy_list)\n", "buy_df = buy_df.sort_values(by='Timestamp', ascending=True)\n", "buy_df.to_csv('buy.csv', index=False)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}