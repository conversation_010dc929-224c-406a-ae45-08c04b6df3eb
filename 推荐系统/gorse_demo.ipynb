{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### 准备数据"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from odps import ODPS\n", "import pandas as pd\n", "from gorse import Gorse\n", "import requests\n", "import json"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tJjdqu75w9fAqsfHTcY'\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT  cust_id\n", "        ,cust_type\n", "        ,cust_name\n", "FROM    summerfarm_tech.dim_cust_df\n", "WHERE   ds = 20231221\n", "AND     ds BETWEEN start_at AND end_at\n", "\"\"\"\n", "\n", "instance = odps.execute_sql(query)\n", "instance.wait_for_success()\n", "with instance.open_reader() as reader:\n", "    cust_df = reader.to_pandas()\n", "\n", "cust_df['cust_id'] = cust_df['cust_id'].astype(str)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["query = \"\"\"\n", "SELECT  spu_id\n", "        ,spu_name\n", "        ,category4\n", "FROM    summerfarm_tech.dim_sku_df\n", "WHERE   ds = 20231221\n", "GROUP BY spu_id\n", "         ,spu_name\n", "         ,category4\n", "\"\"\"\n", "\n", "instance = odps.execute_sql(query)\n", "instance.wait_for_success()\n", "with instance.open_reader() as reader:\n", "    spu_df = reader.to_pandas()\n", "\n", "spu_df['spu_id'] = spu_df['spu_id'].astype(str)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["client = Gorse('http://127.0.0.1:8087', 'api_key')\n", "\n", "url = 'http://localhost:8088/api'\n", "headers = {\"Content-Type\": \"application/json\"}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def change_recommend(user_id, value, token):\n", "    u = f\"{url}/dashboard/recommend/{user_id}/{value}\"\n", "    params = {\n", "        'n': 100\n", "    }\n", "\n", "    cookies = {\n", "        'session': token\n", "    }\n", "\n", "    response = requests.get(u, params=params, cookies=cookies)\n", "\n", "    if response.ok:\n", "        items = response.json()\n", "        return items\n", "    else:\n", "        return None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 推荐"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [], "source": ["cust_id = '7396'\n", "recommended = client.get_recommend(cust_id, n=10)"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cust_id</th>\n", "      <th>cust_type</th>\n", "      <th>cust_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>163</th>\n", "      <td>7396</td>\n", "      <td>其他</td>\n", "      <td>上海和翼商贸有限公司</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    cust_id cust_type   cust_name\n", "163    7396        其他  上海和翼商贸有限公司"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>spu_id</th>\n", "      <th>spu_name</th>\n", "      <th>category4</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>91</td>\n", "      <td>爱氏晨曦全脂纯牛奶</td>\n", "      <td>常温牛奶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>1117</td>\n", "      <td>黄柠檬</td>\n", "      <td>柠檬</td>\n", "    </tr>\n", "    <tr>\n", "      <th>645</th>\n", "      <td>1701</td>\n", "      <td>维纯全脂牛奶(澳洲进口)</td>\n", "      <td>常温牛奶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>673</th>\n", "      <td>1734</td>\n", "      <td>阿尔乐全脂纯牛奶</td>\n", "      <td>常温牛奶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>980</th>\n", "      <td>3192</td>\n", "      <td>兰雀纯牛奶</td>\n", "      <td>常温牛奶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>428</th>\n", "      <td>3867</td>\n", "      <td>荷兰旗牌纯牛奶</td>\n", "      <td>常温牛奶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>441</th>\n", "      <td>3880</td>\n", "      <td>新希望纯牛奶200ML</td>\n", "      <td>常温牛奶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>455</th>\n", "      <td>3894</td>\n", "      <td>新希望纯牛奶(黑白包装款)</td>\n", "      <td>常温牛奶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>459</th>\n", "      <td>3898</td>\n", "      <td>蒙牛纯牛奶</td>\n", "      <td>常温牛奶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>473</th>\n", "      <td>3912</td>\n", "      <td>菲诺冷冻椰子水(泰国香水椰)</td>\n", "      <td>果汁</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    spu_id        spu_name category4\n", "84      91       爱氏晨曦全脂纯牛奶      常温牛奶\n", "66    1117             黄柠檬        柠檬\n", "645   1701    维纯全脂牛奶(澳洲进口)      常温牛奶\n", "673   1734        阿尔乐全脂纯牛奶      常温牛奶\n", "980   3192           兰雀纯牛奶      常温牛奶\n", "428   3867         荷兰旗牌纯牛奶      常温牛奶\n", "441   3880     新希望纯牛奶200ML      常温牛奶\n", "455   3894   新希望纯牛奶(黑白包装款)      常温牛奶\n", "459   3898           蒙牛纯牛奶      常温牛奶\n", "473   3912  菲诺冷冻椰子水(泰国香水椰)        果汁"]}, "metadata": {}, "output_type": "display_data"}], "source": ["cust = cust_df[cust_df['cust_id'] == cust_id]\n", "display(cust)\n", "# fetch the info for recommended in spu_df\n", "recommended_df = spu_df[spu_df['spu_id'].isin(recommended)]\n", "recommended_df\n", "display(recommended_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 按算法推荐"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>spu_id</th>\n", "      <th>spu_name</th>\n", "      <th>category4</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>3</td>\n", "      <td>泰国无核榴莲冻肉</td>\n", "      <td>冷冻果肉</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>55</td>\n", "      <td>总统淡奶油</td>\n", "      <td>搅打型稀奶油</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>71</td>\n", "      <td>蓝风车蓝米吉稀奶油</td>\n", "      <td>搅打型稀奶油</td>\n", "    </tr>\n", "    <tr>\n", "      <th>67</th>\n", "      <td>72</td>\n", "      <td>加利雪莓娘皮_9*9</td>\n", "      <td>雪媚娘皮</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>91</td>\n", "      <td>爱氏晨曦全脂纯牛奶</td>\n", "      <td>常温牛奶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>819</th>\n", "      <td>4263</td>\n", "      <td>辛尼迪马斯卡彭</td>\n", "      <td>马斯卡彭</td>\n", "    </tr>\n", "    <tr>\n", "      <th>871</th>\n", "      <td>4315</td>\n", "      <td>美煌沙拉酱(肉松小贝专用)</td>\n", "      <td>其他半固体(酱)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>909</th>\n", "      <td>4353</td>\n", "      <td>新希望纯牛奶(白盒装)</td>\n", "      <td>常温牛奶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>400</th>\n", "      <td>4869</td>\n", "      <td>Protag纯牛奶</td>\n", "      <td>常温牛奶</td>\n", "    </tr>\n", "    <tr>\n", "      <th>998</th>\n", "      <td>6870</td>\n", "      <td>防港细砂糖（精制白砂糖）</td>\n", "      <td>白砂糖</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>100 rows × 3 columns</p>\n", "</div>"], "text/plain": ["    spu_id       spu_name category4\n", "4        3       泰国无核榴莲冻肉      冷冻果肉\n", "51      55          总统淡奶油    搅打型稀奶油\n", "66      71      蓝风车蓝米吉稀奶油    搅打型稀奶油\n", "67      72     加利雪莓娘皮_9*9      雪媚娘皮\n", "84      91      爱氏晨曦全脂纯牛奶      常温牛奶\n", "..     ...            ...       ...\n", "819   4263        辛尼迪马斯卡彭      马斯卡彭\n", "871   4315  美煌沙拉酱(肉松小贝专用)  其他半固体(酱)\n", "909   4353    新希望纯牛奶(白盒装)      常温牛奶\n", "400   4869      Protag纯牛奶      常温牛奶\n", "998   6870   防港细砂糖（精制白砂糖）       白砂糖\n", "\n", "[100 rows x 3 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 'collaborative', 'user_based', 'item_based'\n", "value = 'collaborative'\n", "token = 'MTcwMzQ4OTMxOXw0QnNkTVFxQ0Vvc0VlWU5tSHNDVldtSVU4cThCUUpZcmFWYWJSS180Y3A4OUhyUlNVUDdWbVRtaGhEMTFIOEt3TkFVMWVibHo5WVhBZGtkc0NZOUhsdTk5WGc9PXxgy4ntgQUU7XEpPswJR4b1A_bory333Q58Qm4epscJqw=='\n", "\n", "items = change_recommend(cust_id, value, token)\n", "\n", "ids = [item['ItemId'] for item in items]\n", "recommended_df = spu_df[spu_df['spu_id'].isin(ids)]\n", "display(recommended_df)"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}