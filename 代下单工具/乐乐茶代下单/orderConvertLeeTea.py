from openpyxl import load_workbook
from openpyxl import Workbook

def outOrderExcel(input_file, output_file):
    try:
        # 读取 Excel 文件
        fileUrl = input_file
        workbook = load_workbook(fileUrl)
        sheet = workbook.active

        # 处理列头
        row1 = [cell.value for cell in sheet[1]]
        row2 = [cell.value for cell in sheet[2]]
        row3 = [cell.value for cell in sheet[3]]
        row4 = [cell.value for cell in sheet[4]]

        row5 = [cell.value for cell in sheet[5]]
        row6 = [cell.value for cell in sheet[6]]
        row7 = [cell.value for cell in sheet[7]]

        # 读取第一列数据
        column1 = [cell.value for cell in sheet['A']]

        # 老表头
        headers = [sheet.cell(row=4, column=i).value for i in range(1, 7)]

        # 获取 G5 到可读取列的门店名称
        store_names = [sheet.cell(row=5, column=i).value for i in range(7, sheet.max_column + 1)]

        # 获取 G6 到可读取列的客户收货信息
        customer_info = [sheet.cell(row=6, column=i).value for i in range(7, sheet.max_column + 1)]

        # 从第 7 行开始加载数据
        data = []
        for row in range(7, sheet.max_row + 1):
            row_data = [sheet.cell(row=row, column=i).value for i in range(1, 7)]
            for col, (store, info) in enumerate(zip(store_names, customer_info), start=7):
                infoSplit = info.split('，')
                quantity = sheet.cell(row=row, column=col).value
                if quantity is not None and quantity != '':
                    new_row = []
                    new_row.append(store)
                    new_row.append('')
                    new_row.append('')
                    new_row.append('')
                    new_row.append(row_data[0])
                    new_row.append(row_data[1])
                    new_row.append('')
                    new_row.append(row_data[2])
                    new_row.append(row_data[3])
                    new_row.append('')
                    new_row.append(quantity)
                    new_row.append(quantity)
                    new_row.append('')
                    new_row.append('')
                    new_row.append('')
                    new_row.append('')
                    new_row.append('')
                    new_row.append('')
                    new_row.append('')
                    new_row.append('')
                    new_row.append(row2[0])
                    new_row.append(row2[0])
                    new_row.append(row2[0])
                    new_row.append(row2[0])
                    new_row.append(infoSplit[0])
                    new_row.append(infoSplit[1])
                    new_row.append(infoSplit[2])
                    new_row.append('')
                    new_row.append('')
                    new_row.append('')
                    
                    data.append(new_row)

        # 构建新的表头
        new_headers =  ['收货机构','配送单号*','物品行号*','物品分类','物品编码*','物品名称','物品品牌','规格型号','订货单位','订货单位和基准单位换算率','应发数量','发货数量*','发货仓库*','批次号*','生产日期*','辅助单位','辅助单位换算关系','辅助单位应发数量','辅助单位发货数量*','备注','发货日期','预计发货日期','预计到货日期','期望到货日期','收货人','收货电话','收货地址','备用联系人','备用联系电话','收货机构备注']


        # 创建一个新的工作簿
        new_wb = Workbook()

        # 获取活动工作表
        new_sheet = new_wb.active

        # 写入新的表头
        new_sheet.append(new_headers)

        # 逐行写入数据
        for row in data:
            new_sheet.append(row)

        # 保存新的 Excel 文件
        new_wb.save(output_file)
    except FileNotFoundError:
        print(f"输入文件 {input_file} 未找到，请检查文件路径是否正确。")
    except Exception as e:
        print(f"出现异常：{e}")

if __name__ == "__main__":
    input_file = '/Users/<USER>/Downloads/供应商分货单(4).xlsx'
    output_file = '/Users/<USER>/Downloads/供应商分货单_处理后.xlsx'
    