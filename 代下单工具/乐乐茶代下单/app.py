from flask import Flask, Blueprint, request, render_template_string, send_file, url_for
import os
from werkzeug.utils import secure_filename
import pandas as pd
from datetime import datetime
from orderConvert import transform_data_order, transform_data_purchase, save_to_excel
from orderConvertLeeTea import outOrderExcel

port=int(os.getenv("LELECHE_HELP_ORDER_PORT", 8193))
routePrefix = os.getenv("LELECHE_HELP_ORDER_ROUTE_PREFIX", "orderConvert")
bp = Blueprint(f'{routePrefix}', __name__, url_prefix=f'/{routePrefix}')

# HTML模板
HTML = '''
<!DOCTYPE html>
<html>
<head>
    <title>Summerfarm 代下单Excel 文件转换</title>
    <style>
        body {
            font-family: 'Roboto', Arial, sans-serif;
            background-color: #f1f1f1;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        header {
            background-color: #4285f4;
            color: white;
            padding: 10px 20px;
            text-align: center;
        }
        header h1 {
            margin: 0;
        }
        .content {
            padding: 20px;
        }
        form {
            margin-top: 20px;
            text-align: center;
        }
        form input[type="file"] {
            margin-right: 10px;
        }
        form input[type="submit"] {
            background-color: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
        }
        form input[type="submit"]:hover {
            background-color: #3367d6;
        }
        .download-links {
            text-align: center;
            margin-top: 20px;
        }
        .download-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 10px;
            background-color: #f8f8f8;
            text-decoration: none;
            color: #333;
            border-radius: 4px;
        }
        .reset-button {
            background-color: #f1f1f1;
            color: #333;
            border: 1px solid #ddd;
            padding: 10px 20px;
            cursor: pointer;
            margin-left: 10px;
        }
        .reset-button:hover {
            background-color: #eaeaea;
        }
        .download-links a:hover {
            background-color: #eaeaea;
        }
        footer {
            margin-top: 30px;
            text-align: center;
            color: #888;
        }
    </style>
</head>
<body>
    <header>
        <h1>Summerfarm 代下单Excel 文件转换</h1>
    </header>
    <div class="container">
        <div class="content">
            <h2>上传 Excel 文件</h2>
            <form action="upload" method="post" enctype="multipart/form-data">
                <input type="file" name="file" required>
                <input type="submit" value="上传文件">
            </form>
            <a href="{{ url_for('.download_template') }}">下载模板Excel文件</a>
            {% if file_url %}
                <div class="download-links">
                    <a href="{{ file_url }}">下载平铺Excel文件</a>
                    <a href="{{ file_url2 }}">下载平铺Excel文件</a>
                    <button class="reset-button" onclick="location.href='{{ url_for('.index') }}'">重置</button>
                </div>
            {% endif %}
        </div>
    </div>
    <footer>
        <p>&copy; 2024 Summerfarm 代下单Excel 文件转换器</p>
    </footer>
</body>
</html>
'''

@bp.route('/download_template')
def download_template():
    # tmpFileName = '导出表格-代下单-客户订单_模版.xlsx'
    tmpFileName = '供应商分货单_模版.xlsx'
    template_path = os.path.join(app.root_path, tmpFileName)
    return send_file(template_path, as_attachment=True, download_name=tmpFileName)

@bp.route('/', methods=['GET'])
def index():
    return render_template_string(HTML, file_url='', file_url2='')


@bp.route('/upload', methods=['POST'])
def upload():
    file = request.files['file']
    if file:
        filename = file.filename
        input_path = os.path.join('/tmp', filename)
        file.save(input_path)

        try:
            # 读取数据并转换
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            # output_file_order_name = f'导入表格-代下单-鲜沐大客户订单_{timestamp}.xlsx'
            # output_file_order = os.path.join('/tmp', output_file_order_name)
            # output_file_purchase_name = f'导入表格-采购单_{timestamp}.xlsx'
            # output_file_purchase = os.path.join('/tmp', output_file_purchase_name)
            output_file_order_name_lee_tea = f'导入表格-代下单-鲜沐大客户订单_{timestamp}.xlsx'
            output_file_order_lee_tea = os.path.join('/tmp', output_file_order_name_lee_tea)


            # # 代下单数据转换
            # df = pd.read_excel(input_path)
            # transformed_df_order = transform_data_order(df)
            # save_to_excel(transformed_df_order, output_file_order)
            
            # 采购单数据转换
            # transformed_df_purchase = transform_data_purchase(df)
            # save_to_excel(transformed_df_purchase, output_file_purchase)
            
            # 乐乐茶订单数据转换
            outOrderExcel(input_path, output_file_order_lee_tea)
            
            # 返回结果页面，包含下载链接
            # return render_template_string(HTML, file_url=url_for('.download', filename=output_file_order_name), file_url2=url_for('.download', filename=output_file_purchase_name))
            return render_template_string(HTML, file_url=url_for('.download', filename=output_file_order_name_lee_tea), file_url2=url_for('.download', filename=output_file_order_name_lee_tea))
        except Exception as e:
            return render_template_string(HTML, error=str(e), file_url='', file_url2='')
        finally:
            # 清理临时文件
            if os.path.exists(input_path):
                os.remove(input_path)
    return render_template_string(HTML, file_url='', file_url2='')


@bp.route('/download/<filename>')
def download(filename):
    output_path = os.path.join('/tmp', filename)
    return send_file(output_path, as_attachment=True, download_name=filename)

 # 注册蓝图
app = Flask(__name__)
app.register_blueprint(bp)

if __name__ == '__main__':
    app.run(host="0.0.0.0",debug=False, port=port)