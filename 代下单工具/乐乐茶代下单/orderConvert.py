import pandas as pd

# 读取Excel文件
def read_excel(file_path):
    return pd.read_excel(file_path)
# 保存到新的Excel文件
def save_to_excel(df, file_path):
    df.to_excel(file_path, index=False)

# 转换订单数据格式
def transform_data_order(df):
    # 将SKU编码作为列，其他信息作为行
    pivot_df = df.pivot_table(index=['mid（客户编号）', '门店名称', '客户类型', '联系人', '联系方式', '配送地址'], 
                              columns='sku编码', values='数量', aggfunc='sum').reset_index()
    # 重命名列以匹配输出格式
    pivot_df.columns = ['mid（客户编号）', '门店名称', '客户类型', '联系人', '联系方式', '配送地址'] + pivot_df.columns[6:].tolist()
    return pivot_df

# 输出代下单表格
# 假设你的输入文件名为'input.xlsx'，输出文件名为'output.xlsx'
# input_file = './orderConvert/导出表格-代下单-客户订单.xlsx'
# output_file = './orderConvert/导入表格-代下单-鲜沐大客户订单.xlsx'
def outOrderExcel(input_file, output_file):
    
    # 读取数据
    df = read_excel(input_file)
    
    # 转换数据
    transformed_df_order = transform_data_order(df)
    
    # 保存到新的Excel文件
    save_to_excel(transformed_df_order, output_file)
    print(f"代下单数据已转换并保存到'{output_file}'")

# 转换数据格式并创建新的DataFrame
def transform_data_purchase(df):
    # 对数据进行透视，以SKU编码为列，对数量进行汇总
    pivot_df = df.pivot_table(index='sku编码', values='数量', aggfunc='sum').reset_index()
    # 重命名列以匹配输出格式
    pivot_df.columns = ['sku(必填)', '数量(必填)']
    # 添加其他固定列
    pivot_df['商品名称'] = ''
    pivot_df['总价(必填)'] = 0
    pivot_df['供应商(必填)'] = '供应商需替换'
    return pivot_df

# 输出采购单下单表格
# 假设你的输入文件名为'input.xlsx', 输出文件名为'output.xlsx'
# input_file = './orderConvert/导出表格-代下单-客户订单.xlsx'
# output_file = './orderConvert/导入表格-采购单.xlsx'
def outPurchaseExcel(input_file, output_file):
    # 读取数据
    df = read_excel(input_file)
    
    # 转换数据
    transformed_df_purchase = transform_data_purchase(df)
    
    # 保存到新的Excel文件
    save_to_excel(transformed_df_purchase, output_file)
    print(f"采购单数据已转换并保存到'{output_file}'")

if __name__ == "__main__":
    input_file = './orderConvert/导出表格-代下单-客户订单.xlsx'
    output_file_order = './orderConvert/导入表格-代下单-鲜沐大客户订单.xlsx'
    output_file_purchase = './orderConvert/导入表格-采购单.xlsx'
    outOrderExcel(input_file, output_file_order)
    outPurchaseExcel(input_file, output_file_purchase)