<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.xianmu.match_biaoguo_xianmu</string>
    <key>ProgramArguments</key>
    <array>
        <string>/bin/bash</string>
        <string>-l</string>
        <string>-c</string>
        <string>cd /Users/<USER>/Documents/xianmu-ai && source np/bin/activate && truncate -s 0 logs/match_biaoguo_xianmu.log logs/match_biaoguo_xianmu.error.log ; python -m 爬虫商品匹配.match_pop_and_competitor_each_other_xianmu --top_products_to_save 500 --xianmu_sku_cnt_to_save 5000 --feishu_token 99b20374-35b4-45bc-b25b-7c77a5d68106</string>
    </array>
    <key>StartCalendarInterval</key>
    <dict>
        <key>Hour</key>
        <integer>08</integer>
        <key>Minute</key>
        <integer>10</integer>
    </dict>
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/Users/<USER>/Documents/xianmu-ai/np/bin:/opt/homebrew/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin</string>
        <key>LANG</key>
        <string>en_US.UTF-8</string>
        <key>LC_ALL</key>
        <string>en_US.UTF-8</string>
        <key>HOME</key>
        <string>/Users/<USER>/string>
        <key>ALIBABA_CLOUD_ACCESS_KEY_ID</key>
        <string>LTAI5tQzmpz2nQEWdiqvQGsc</string>
        <key>ALIBABA_CLOUD_ACCESS_KEY_SECRET</key>
        <string>******************************</string>
        <key>AZURE_GPT4O_MINI_API_KEY</key>
        <string>4ETioIsf8XAGcYqpPkiV8y24Caj1vq4TDh3Hny88YrRA9LqoLYdZJQQJ99AKACYeBjFXJ3w3AAABACOGatvv</string>
        <key>AZURE_API_KEY_XM</key>
        <string>4ETioIsf8XAGcYqpPkiV8y24Caj1vq4TDh3Hny88YrRA9LqoLYdZJQQJ99AKACYeBjFXJ3w3AAABACOGatvv</string>
        <key>XIANMU_ADMIN_PASSWORD_POP</key>
        <string>KE*ErPmBit*7f3!Z</string>
        <key>XM_FAST_GPT_API_KEY</key>
        <string>sk-Is03LB8g58Qz41bdQKip5g</string>
    </dict>
    <key>WorkingDirectory</key>
    <string>/Users/<USER>/Documents/xianmu-ai</string>
    <key>StandardOutPath</key>
    <string>/Users/<USER>/Documents/xianmu-ai/logs/match_biaoguo_xianmu.log</string>
    <key>StandardErrorPath</key>
    <string>/Users/<USER>/Documents/xianmu-ai/logs/match_biaoguo_xianmu.error.log</string>
</dict>
</plist>