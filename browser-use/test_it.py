from langchain_openai import AzureChatOpenAI
from browser_use import Agent, BrowserConfig
from pydantic import SecretStr
import os
import asyncio

# Basic configuration
config = BrowserConfig(headless=False, disable_security=True)

# Initialize the model
llm = AzureChatOpenAI(
    model="gpt-4o",
    api_version="2024-10-21",
    azure_endpoint=os.getenv(
        "AZURE_OPENAI_ENDPOINT", "https://esat-us.openai.azure.com"
    ),
    api_key=SecretStr(os.getenv("AZURE_API_KEY_XM", "")),
)

# Define sensitive data
# The model will only see the keys (x_name, x_password) but never the actual values
sensitive_data = {
    "x_name": "<EMAIL>",
    "x_password": os.getenv("XIANMU_ADMIN_PASSWORD_qaadmin"),
}


task="""首先登陆https://qaadmin.summerfarm.net，输入用户名密码后，请立即导航到URI：/summerfarm-fe/activity/config/list
然后将活动id：9487的“活动范围”仅保留杭州大区、上海大区和江苏大区。
登陆时用户名/密码请用x_name和x_password"""

async def main():
    agent = Agent(
        task=task,
        llm=llm,
        sensitive_data=sensitive_data,
    )
    result = await agent.run()
    print(result)


asyncio.run(main())
