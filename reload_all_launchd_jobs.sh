#!/bin/bash

# Print current directory to confirm location
echo "Executing from: $(pwd)"

# Copy plist files to LaunchAgents
echo "Copying plist files to ~/Library/LaunchAgents/"
cp ./com.xianmu.predict.day-only.plist ~/Library/LaunchAgents/
cp ./com.xianmu.predict.week-only.plist ~/Library/LaunchAgents/
cp ./com.xianmu.speech-recognize.plist ~/Library/LaunchAgents/

# Unload existing tasks
echo "Unloading tasks..."
launchctl unload ~/Library/LaunchAgents/com.xianmu.predict.day-only.plist
launchctl unload ~/Library/LaunchAgents/com.xianmu.predict.week-only.plist
launchctl unload ~/Library/LaunchAgents/com.xianmu.speech-recognize.plist

# Small delay to ensure clean unload
sleep 1

# Load tasks
echo "Loading tasks..."
launchctl load ~/Library/LaunchAgents/com.xianmu.predict.day-only.plist
launchctl load ~/Library/LaunchAgents/com.xianmu.predict.week-only.plist
launchctl load ~/Library/LaunchAgents/com.xianmu.speech-recognize.plist

# Check status
echo "Checking launchd status..."
launchctl list | grep xianmu

echo "Installation and reload complete!"