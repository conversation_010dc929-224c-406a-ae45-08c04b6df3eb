import psycopg2
import psycopg2.extras
import logging
from contextlib import contextmanager
from typing import Optional, List, Dict, Tuple, Union

# Configure logging level (consider using a config file in a real application)
logging.basicConfig(level=logging.INFO)


@contextmanager
def get_db_connection(
    host: str = "127.0.0.1",
    port: int = 5432,
    database: str = "search_arena",
    user: str = "llmproxy",
    password: str = "dbpassword9090",
) -> psycopg2.extensions.connection:
    """
    Gets a database connection using a context manager.  Handles connection closing.
    """
    conn = None  # Initialize conn to None
    try:
        conn = psycopg2.connect(
            host=host, port=port, database=database, user=user, password=password
        )
        yield conn
    except Exception as e:
        logging.error(f"Error connecting to the database: {e}")
        raise  # Re-raise the exception to be handled by the caller
    finally:
        if conn is not None:
            conn.close()


def ensure_db_name_is_provided(db_name: str, host: str):
    """Ensures the specified database exists; creates it if it doesn't."""
    with get_db_connection(host=host, database="postgres") as conn:
        with conn.cursor() as cur:
            # Create database if not exists
            cur.execute("SELECT 1 FROM pg_database WHERE datname = %s", (db_name,))
            if cur.fetchone() is None:
                # Need to close the transaction to create DB
                conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)
                cur.execute(f"CREATE DATABASE {db_name}")
                logging.info(f"Database {db_name} created")
                conn.set_isolation_level(
                    psycopg2.extensions.ISOLATION_LEVEL_READ_COMMITTED
                )  # Reset to default


def get_arena_db_connection_and_execute(
    sql: str,
    args: tuple = (),
    pg_host: str = "127.0.0.1",
    db_name: str = "search_arena",
) -> Union[int, List[Dict]]:
    """
    Executes SQL queries against the Arena database, handling connection and errors.

    Args:
        sql: The SQL query to execute.
        args: Parameters for the SQL query.
        pg_host: Database host.
        db_name: Database name.

    Returns:
        For INSERT/UPDATE/DELETE/CREATE queries: the number of rows affected.
        For SELECT queries: a list of dictionaries, each representing a row.
        Returns an empty list if there's an error during a SELECT.
    """
    try:
        with get_db_connection(host=pg_host, database=db_name) as conn:
            with conn.cursor() as cur:
                cur.execute(sql, args)
                if sql.strip().upper().startswith(
                    ("INSERT", "UPDATE", "DELETE", "CREATE")
                ):
                    conn.commit()
                    logging.info(
                        f"sql: {sql}, args: {args}, rows affected: {cur.rowcount}"
                    )
                    return cur.rowcount
                else:  # Assuming SELECT
                    # Fetch all results as dictionaries
                    columns = [desc[0] for desc in cur.description]
                    return [dict(zip(columns, row)) for row in cur.fetchall()]

    except Exception as e:
        logging.error(
            f"Error executing SQL:{sql}, args:{args}, Error:{e}, db_name:{db_name}"
        )
        if sql.strip().upper().startswith("SELECT"):
            return []  # Return empty list for SELECT errors
        else:
            raise  # Re-raise exceptions for non-SELECT queries
