{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "sys.path.append(\"../\")\n", "from datetime import datetime,timedelta\n", "\n", "from odps_client import get_odps_sql_result_as_df\n", "yesterday=datetime.now()-<PERSON><PERSON><PERSON>(days=1)\n", "yesterday=yesterday.strftime('%Y-%m-%d')\n", "\n", "sql=f\"\"\"select * from summerfarm_tech.ods_follow_up_record_df \n", "where ds=max_pt('summerfarm_tech.ods_follow_up_record_df') \n", "and follow_up_way in ('有效拜访','普通上门拜访') \n", "and add_time between '{yesterday} 00:00:00' and '{yesterday} 23:59:59'\n", "order by admin_id,add_time\n", "\"\"\"\n", "\n", "follow_up_records_df=get_odps_sql_result_as_df(sql)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["follow_up_records_df[follow_up_records_df['admin_name']=='张延京'][[\"admin_name\",'city','m_id','poi_note','add_time']].head(300)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import requests\n", "import time\n", "\n", "\n", "def calculate_navigation_distance(origin, destination, waypoints=None):\n", "    \"\"\"\n", "    使用 Google Maps Directions API V2 计算两个坐标点之间的导航距离，允许设置途经点。\n", "\n", "    Args:\n", "        origin (tuple): 起点坐标，格式为 (纬度, 经度)。\n", "        destination (tuple): 终点坐标，格式为 (纬度, 经度)。\n", "        waypoints (list, optional): 途经点坐标列表，每个途经点格式为 (纬度, 经度)。默认为 None。\n", "\n", "    Returns:\n", "        dict: 包含距离和持续时间信息的字典，如果 API 调用失败则返回 None。\n", "    \"\"\"\n", "    # Setup proxy\n", "    proxy_url = \"http://127.0.0.1:7890\"\n", "\n", "    headers = {\n", "        \"Content-Type\": \"application/json\",\n", "        \"X-Goog-Api-Key\": os.getenv(\"GOOGLE_MAP_API_KEY\"),  # 请替换为您的实际 API 密钥\n", "        \"X-Goog-FieldMask\": \"routes.duration,routes.distanceMeters,routes.polyline.encodedPolyline\",\n", "    }\n", "    request_body = {\n", "        \"origin\": {\n", "            \"location\": {\"latLng\": {\"latitude\": origin[0], \"longitude\": origin[1]}}\n", "        },\n", "        \"destination\": {\n", "            \"location\": {\"latLng\": {\"latitude\": destination[0], \"longitude\": destination[1]}}\n", "        },\n", "        \"travelMode\": \"DRIVE\",\n", "        \"routingPreference\": \"TRAFFIC_AWARE\",\n", "        \"computeAlternativeRoutes\": <PERSON><PERSON><PERSON>,\n", "        \"routeModifiers\": {\n", "            \"avoidTolls\": <PERSON><PERSON><PERSON>,\n", "            \"avoidHighways\": <PERSON><PERSON><PERSON>,\n", "            \"avoidFerries\": <PERSON><PERSON>e,\n", "        },\n", "        \"languageCode\": \"zh-CN\",  # 修改为中文\n", "        \"units\": \"METRIC\",  # 修改为 METRIC，使用米和秒\n", "    }\n", "\n", "    # 添加途经点\n", "    if waypoints:\n", "        request_body[\"intermediates\"] = [\n", "            {\"location\": {\"latLng\": {\"latitude\": wp[0], \"longitude\": wp[1]}}}\n", "            for wp in waypoints\n", "        ]\n", "\n", "    proxies = {\n", "        \"http\": proxy_url,\n", "        \"https\": proxy_url,\n", "    }\n", "    url = \"https://routes.googleapis.com/directions/v2:computeRoutes\"\n", "\n", "    max_retries = 3  # 设置最大重试次数\n", "    retry_delay = 5  # 设置重试间隔时间（秒）\n", "\n", "    for attempt in range(max_retries):\n", "        try:\n", "            # 调用 Directions API V2\n", "            response = requests.post(\n", "                url, headers=headers, json=request_body, proxies=proxies\n", "            )\n", "            response.raise_for_status()  # 检查请求是否成功\n", "\n", "            directions_result = response.json()\n", "\n", "            if (\n", "                directions_result and directions_result[\"routes\"]\n", "            ):  # 检查 'routes' 列表是否存在且不为空\n", "                route = directions_result[\"routes\"][0]  # 获取第一个 route\n", "                duration_seconds = route[\n", "                    \"duration\"\n", "                ]  #  duration 字段已经是秒为单位的字符串，例如 \"120s\"\n", "                distance_meters = route[\"distanceMeters\"]\n", "\n", "                # 将 duration 转换为文本格式 (例如 \"15 分钟\")\n", "                duration_text = \"\"\n", "                seconds = int(duration_seconds[:-1])  # 去掉 \"s\" 并转换为整数\n", "                minutes = seconds // 60\n", "                if minutes > 0:\n", "                    duration_text = f\"{minutes} 分钟\"\n", "                    seconds %= 60\n", "                if seconds > 0:\n", "                    duration_text += f\" {seconds} 秒\" if duration_text else f\"{seconds} 秒\"\n", "\n", "                result = {\n", "                    \"distance_text\": f\"{distance_meters/1000:.2f} 公里\",  # 转换为公里并保留两位小数\n", "                    \"distance_meters\": distance_meters,\n", "                    \"duration_text\": duration_text.strip() if duration_text else \"0 秒\",\n", "                    \"duration_seconds\": seconds,  # 返回秒数，整数类型\n", "                }\n", "                print(f\"起点: {origin}\")\n", "                print(f\"终点: {destination}\")\n", "                print(f\"途经点：{waypoints}\")\n", "                print(f\"导航距离: {result['distance_text']} ({result['distance_meters']} 米)\")\n", "                print(f\"预计行程时间: {result['duration_text']} ({result['duration_seconds']} 秒)\")\n", "\n", "                return result\n", "            else:\n", "                print(f\"API 调用失败: 未找到 routes\")  # 修改错误信息\n", "                return None\n", "\n", "        except requests.exceptions.HTTPError as e:  # 捕获 HTTP 错误\n", "            print(f\"HTTP API 调用失败: {e}\")\n", "            print(f\"Response text: {e.response.text}\")  # 打印详细的错误响应\n", "            return None\n", "        except requests.exceptions.ConnectionError as e: #捕获连接错误\n", "            print(f\"发生错误: {e}\")\n", "            if \"Max retries exceeded with url\" in str(e):\n", "                if attempt < max_retries - 1:\n", "                    print(f\"将在 {retry_delay} 秒后重试 (第 {attempt + 1}/{max_retries} 次)...\")\n", "                    time.sleep(retry_delay)\n", "                    continue  # 继续下一次循环，尝试重试\n", "                else:\n", "                    print(\"达到最大重试次数，放弃请求。\")\n", "                    return None\n", "        except Exception as e:\n", "            print(f\"发生错误: {e}\")\n", "            return None\n", "\n", "\n", "# 示例坐标，北京故宫到长城，途经天安门\n", "origin_coordinates = (39.916668, 116.397222)  # 故宫\n", "destination_coordinates = (40.431908, 116.570375)  # 长城 (八达岭)\n", "waypoints = [(39.908722, 116.397499)]  # 天安门\n", "\n", "result = calculate_navigation_distance(origin_coordinates, destination_coordinates, waypoints)\n", "print(result)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from math import radians, cos, sin, asin, sqrt\n", "import pandas as pd\n", "\n", "def get_distance(lon1, lat1, lon2, lat2):\n", "    \"\"\"\n", "    计算地球上两点之间的距离（以米为单位）\n", "    \"\"\"\n", "    lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])\n", "    dlon = lon2 - lon1\n", "    dlat = lat2 - lat1\n", "    a = sin(dlat / 2) ** 2 + cos(lat1) * cos(lat2) * sin(dlon / 2) ** 2\n", "    c = 2 * asin(sqrt(a))\n", "    r = 6371  # 地球平均半径，单位为公里\n", "    return c * r * 1000\n", "\n", "# 将 poi_note 列分割为经度和纬度，并转换为数值类型\n", "# 先去除 poi_note 中的空字符串，这些空字符串会导致后续的 astype(float) 报错\n", "follow_up_records_df = follow_up_records_df[follow_up_records_df['poi_note'] != '']\n", "print(f\"数据量: {len(follow_up_records_df)}\")\n", "follow_up_records_df[['longitude', 'latitude']] = follow_up_records_df['poi_note'].str.split(',', expand=True).astype(float)\n", "\n", "# 按照 admin_name 和 add_time 排序，确保计算距离的顺序正确\n", "follow_up_records_df = follow_up_records_df.sort_values(by=['admin_name', 'add_time'])\n", "\n", "# 初始化一个字典来存储每个销售的统计信息\n", "admin_stats = {}\n", "\n", "# 遍历每个销售\n", "for admin_name, group in follow_up_records_df.groupby('admin_name'):\n", "    total_distance = 0\n", "    total_navigation_distance = 0  # 初始化导航距离\n", "    # 获取城市，假设每个销售的城市是固定的，取第一个\n", "    city = group['city'].iloc[0]\n", "    # 统计拜访门店数，使用 m_id 去重\n", "    visited_stores = group['m_id'].nunique()\n", "    # 获取拜访日期，add_time 转换为日期\n", "    visit_date = group['add_time'].iloc[0].strftime('%Y-%m-%d')\n", "\n", "    points = group[['longitude', 'latitude', 'add_time']].dropna(subset=['longitude', 'latitude']).to_dict('records')\n", "    if len(points) > 1:\n", "        # 构建起点、终点和途经点\n", "        origin = (points[0]['latitude'], points[0]['longitude'])\n", "        destination = (points[-1]['latitude'], points[-1]['longitude'])\n", "        waypoints = [(p['latitude'], p['longitude']) for p in points[1:-1]]\n", "\n", "        # 调用 calculate_navigation_distance 计算导航距离\n", "        navigation_result = calculate_navigation_distance(origin, destination, waypoints)\n", "        if navigation_result:\n", "            total_navigation_distance = navigation_result['distance_meters']\n", "\n", "        for i in range(1, len(points)):\n", "            lon1, lat1 = points[i-1]['longitude'], points[i-1]['latitude']\n", "            lon2, lat2 = points[i]['longitude'], points[i]['latitude']\n", "            distance = get_distance(lon1, lat1, lon2, lat2)\n", "            total_distance += distance\n", "\n", "\n", "\n", "    admin_stats[admin_name] = {\n", "        'total_distance_meters': total_distance,\n", "        'total_navigation_distance_meters': total_navigation_distance,  # 添加导航距离\n", "        'city': city,\n", "        'visited_stores': visited_stores,\n", "        'visit_date': visit_date\n", "    }\n", "\n", "# 将结果转换为 DataFrame 方便查看\n", "admin_distance_df = pd.DataFrame.from_dict(admin_stats, orient='index')\n", "admin_distance_df.index.name = 'admin_name'\n", "admin_distance_df = admin_distance_df.reset_index()\n", "admin_distance_df.to_csv(f\"./销售运动距离分析结果{yesterday}.csv\", index=False, encoding=\"utf-8-sig\")\n", "admin_distance_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["admin_distance_df.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["admin_distance_df[\"total_distance_meters\"].quantile([0.25, 0.5, 0.75, 0.9])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["admin_distance_df[admin_distance_df[\"total_distance_meters\"]>=47146]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 2}