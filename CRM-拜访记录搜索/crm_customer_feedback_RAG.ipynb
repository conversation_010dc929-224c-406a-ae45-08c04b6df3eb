{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "from datetime import datetime, timedelta\n", "\n", "sys.path.append(\"../\")\n", "\n", "from odps_client import get_odps_sql_result_as_df\n", "\n", "add_time_range = (datetime.now() - <PERSON><PERSON><PERSON>(30)).strftime(\"%Y-%m-%d\")\n", "print(f\"add_time_range:{add_time_range}\")\n", "\n", "all_feedback_df = get_odps_sql_result_as_df(\n", "    f\"\"\"\n", "SELECT  a.admin_name 拜访人\n", "        ,a.admin_id 拜访人ID\n", "        ,a.m_id 客户ID\n", "        ,a.add_time 拜访时间\n", "        ,b.mname AS 客户名\n", "        ,b.last_order_time AS 客户最后下单时间\n", "        ,b.size AS 是否大客户\n", "        ,b.city 客户城市\n", "        ,b.province 客户省份\n", "        ,c.area_name 运营服务区\n", "        ,d.large_area_name 运营服务大区\n", "        ,a.condition 拜访文字记录\n", "        ,a.follow_up_way 拜访方式\n", "        ,a.follow_up_pic 拜访图片\n", "FROM    summerfarm_tech.ods_follow_up_record_df a\n", "INNER JOIN summerfarm_tech.ods_merchant_df b\n", "ON      b.ds = MAX_PT('summerfarm_tech.ods_merchant_df')\n", "AND     b.m_id = a.m_id\n", "INNER JOIN summerfarm_tech.ods_area_df c\n", "ON      c.ds = MAX_PT('summerfarm_tech.ods_area_df')\n", "AND     c.area_no = b.area_no\n", "INNER JOIN summerfarm_tech.ods_large_area_df d\n", "ON      d.ds = MAX_PT('summerfarm_tech.ods_large_area_df')\n", "AND     c.large_area_no = d.large_area_no\n", "WHERE   a.ds = MAX_PT('summerfarm_tech.ods_follow_up_record_df')\n", "AND     a.add_time >= '{add_time_range} 00:00:00'\n", "AND     a.follow_up_way = '有效拜访'\n", "ORDER BY b.area_no, a.add_time DESC\n", ";\n", "\"\"\"\n", ")\n", "\n", "\n", "print(all_feedback_df.head(1).to_json(force_ascii=False, orient=\"records\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "\n", "all_feedback_df[\"拜访日\"] = all_feedback_df[\"拜访时间\"].apply(\n", "    lambda x: x.strftime(\"%Y-%m-%d\")\n", ")\n", "all_feedback_df.drop_duplicates(\n", "    subset=[\"拜访人id\", \"客户id\", \"拜访日\", \"拜访文字记录\"], inplace=True\n", ")\n", "\n", "# 设置中文字体\n", "plt.rcParams[\"font.sans-serif\"] = [\"Arial Unicode MS\"]\n", "plt.rcParams[\"axes.unicode_minus\"] = False\n", "all_feedback_df.groupby([\"拜访日\"]).size().plot(kind=\"bar\", figsize=(8, 3))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandasql\n", "import os\n", "\n", "for (day, large_area), group_df in all_feedback_df.groupby(\n", "    [\"拜访日\", \"运营服务大区\"]\n", "):\n", "    file_name = f\"./data/{day}_{large_area}.md\"\n", "    os.makedirs(os.path.dirname(file_name), exist_ok=True)\n", "    with open(file_name, \"w\", encoding=\"utf-8\") as f:\n", "        f.write(f\"## {day} {large_area}\\n\\n\")\n", "        for index, row in group_df.iterrows():\n", "            f.write(f\"### 拜访人: {row['拜访人']},{row['运营服务区']}\\n\\n\")\n", "            f.write(f\"- 客户: {row['客户名']}:{row['客户id']}, {row['客户城市']}, {row['客户最后下单时间']}, {row['是否大客户']}\\n\")\n", "            f.write(f\"- 拜访日: {row['拜访日']}\\n\")\n", "            f.write(f\"- 拜访文字记录: {row['拜访文字记录']}\\n\")\n", "            f.write(f\"- 拜访方式: {row['拜访方式']}\\n\")\n", "            # if row['拜访图片']:\n", "            #     f.write(f\"- 拜访图片: <img src=\\\"{'https://azure.summerfarm.net/' + row['拜访图片']}\\\" style=\\\"width:120px;height:120px;object-fit:contain;\\\">\\n\")\n", "            f.write(\"\\n---\\n\")\n", "    print(f\"file {file_name} created\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "import numpy as np\n", "from lightrag import LightRAG, QueryParam\n", "from lightrag.llm import (\n", "    azure_openai_complete_if_cache,\n", "    locate_json_string_body_from_string,\n", "    azure_openai_embedding,\n", ")\n", "\n", "#########\n", "# Uncomment the below two lines if running in a jupyter notebook to handle the async nature of rag.insert()\n", "import nest_asyncio\n", "\n", "nest_asyncio.apply()\n", "#########\n", "\n", "WORKING_DIR = \"./data_lightrag\"\n", "\n", "os.environ[\"OPENAI_API_VERSION\"] = \"2024-10-01-preview\"\n", "os.environ[\"AZURE_OPENAI_ENDPOINT\"] = \"https://esat-us.openai.azure.com\"\n", "os.environ[\"AZURE_OPENAI_API_KEY\"] = os.getenv(\"AZURE_API_KEY_XM\")\n", "\n", "\n", "async def my_azure_openai_complete(\n", "    prompt, system_prompt=None, history_messages=[], keyword_extraction=False, **kwargs\n", ") -> str:\n", "    keyword_extraction = kwargs.pop(\"keyword_extraction\", None)\n", "    result = await azure_openai_complete_if_cache(\n", "        \"gpt-4o-mini\",\n", "        prompt,\n", "        system_prompt=system_prompt,\n", "        history_messages=history_messages,\n", "        **kwargs,\n", "    )\n", "    if keyword_extraction:  # TODO: use JSON API\n", "        return locate_json_string_body_from_string(result)\n", "    return result\n", "\n", "\n", "if not os.path.exists(WORKING_DIR):\n", "    os.mkdir(WORKING_DIR)\n", "\n", "rag = LightRAG(\n", "    working_dir=WORKING_DIR,\n", "    llm_model_func=my_azure_openai_complete,\n", "    embedding_func=azure_openai_embedding,\n", ")\n", "\n", "import glob\n", "\n", "for file_path in glob.glob(\"./data/2025-02-19_*.md\"):\n", "    with open(file_path, \"r\") as f:\n", "        rag.insert(f.read())"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Perform naive search\n", "# print(rag.query(\"客户对草莓的反馈如何?\", param=QueryParam(mode=\"naive\")))\n", "\n", "# Perform local search\n", "# print(rag.query(\"浙江大区的客户对水果的反馈如何?\", param=QueryParam(mode=\"local\")))\n", "\n", "# Perform global search\n", "# print(rag.query(\"客户对草莓的反馈如何?\", param=QueryParam(mode=\"global\")))\n", "\n", "# Perform hybrid search\n", "# print(rag.query(\"客户对草莓的反馈如何?\", param=QueryParam(mode=\"hybrid\")))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Perform hybrid search\n", "print(rag.query(\"上海大区的客户集中反应最多的问题有哪些？?有哪些具体的例子。\", param=QueryParam(mode=\"hybrid\")))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(rag.query(\"客户对于草莓的反馈是怎样的？请你列举明确的客户反馈，我需要准确的事实数据\", param=QueryParam(mode=\"hybrid\")))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 2}