from neuralprophet import Neur<PERSON><PERSON><PERSON><PERSON>, set_log_level
import multiprocessing
import pandas as pd
from datetime import date, timedelta, datetime
import numpy as np
import warnings
import os
import time
import sys

from odps import ODPS
from odps.accounts import StsAccount
from odps import DataFrame

warnings.filterwarnings('ignore')
set_log_level("ERROR")

def load_data():

    ALIBABA_CLOUD_ACCESS_KEY_ID = 'LTAI5tJjdqu75w9fAqsfHTcY'
    ALIBABA_CLOUD_ACCESS_KEY_SECRET = '******************************'
    odps = ODPS(
        ALIBABA_CLOUD_ACCESS_KEY_ID,
        ALIBABA_CLOUD_ACCESS_KEY_SECRET,
        project='summerfarm_ds_dev',
        endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',
    )

    query = """
    SELECT  sku_id, warehouse_no, date, quantity
    FROM    summerfarm_tech.app_estimated_sku_warehouse_record_df
    WHERE   ds = MAX_PT('summerfarm_tech.app_estimated_sku_warehouse_record_df')
    AND     date >= '********'
    """

    instance=odps.execute_sql(query);
    instance.wait_for_success()
    with instance.open_reader() as reader:
        sales_records_df = reader.to_pandas()


    sales_records_df.rename(columns={'date' : 'ds', 'quantity' : 'y'}, inplace=True)
    sales_records_df['ds'] = pd.to_datetime(sales_records_df['ds'], format='%Y%m%d') 

    sku_warehouse = sales_records_df[['sku_id', 'warehouse_no']].drop_duplicates()

    ############################# 根据worker数量切分数据 ##############################
    # 从环境变量中获取当前JOB的WORLD_SIZE
    # world_size = int(os.environ.get("WORLD_SIZE", "{}"))
    # print(f'world_size:{world_size}')

    # # 从环境变量中获取当前JOB的RANK
    # rank = int(os.environ.get("RANK", "{}"))
    # print(f'rank:{rank}')

    # if str(rank) == '0':
    #     sys.exit()

    # total_rows = len(sku_warehouse)
    # rows_per_worker = total_rows // (world_size - 1)  # Subtract 1 from world_size because rank 0 is not included

    # # Adjust the start and end indices to account for rank 0 being skipped
    # start_index = (rank - 1) * rows_per_worker
    # end_index = rank * rows_per_worker if rank != world_size - 1 else total_rows

    # # Slice the DataFrame
    # sku_warehouse.sort_values(by=['warehouse_no'], inplace=True)
    # sku_warehouse_slice = sku_warehouse.iloc[start_index:end_index]
    # print('warehouses: ', sku_warehouse_slice['warehouse_no'].unique())
    ##################################################################################

    return sales_records_df, sku_warehouse


def predict_period(dataframe, period, num_periods, quantiles, train_end_date):
    df = dataframe.copy()

    # fill in the missing dates
    start_date = df['ds'].min()
    date_range = pd.date_range(start=start_date, end=datetime.today())
    df.set_index('ds', inplace=True)
    df = df.reindex(date_range, fill_value=0).reset_index().rename(columns={'index': 'ds'})

    # Handle weekly aggregation if needed
    if period == 'week':
        week_end_day = train_end_date.weekday()
        offset = pd.offsets.Week(weekday=week_end_day)
        df = df.groupby(pd.Grouper(key='ds', freq=offset)).sum().reset_index()

    df_train = df[df['ds'] <= train_end_date]

    m = NeuralProphet(epochs=300, quantiles=quantiles)
    m = m.add_country_holidays('CN', lower_window=1, upper_window=3)
    m.fit(df=df_train, minimal=True)

    # mae = metric['MAE'].iloc[-1]

    df_future = m.make_future_dataframe(df_train, n_historic_predictions=True, periods=num_periods)
    forecast = m.predict(df_future)

    forecasted = forecast[forecast['ds'] > train_end_date].copy()
    forecasted.reset_index(drop=True, inplace=True)
    forecasted.drop(columns=['y'], inplace=True)

    forecasted.loc[forecasted['yhat1'] < 0, 'yhat1'] = 0
    forecasted.loc[forecasted['yhat1 5.0%'] < 0, 'yhat1 5.0%'] = 0
    forecasted.loc[forecasted['yhat1 95.0%'] < 0, 'yhat1 95.0%'] = 0

    forecasted['estimated_sales_cnt'] = forecasted['yhat1'].apply(lambda x: round(x))
    forecasted['estimated_sales_lower_cnt'] = forecasted['yhat1 5.0%'].apply(lambda x: round(x))
    forecasted['estimated_sales_upper_cnt'] = forecasted['yhat1 95.0%'].apply(lambda x: round(x))

    forecasted['type'] = period
    # forecasted['mae'] = mae
    # forecasted['epochs'] = metric.shape[0]

    return forecasted

def predict_day(dataframe, num_of_days, quantiles, train_end_date):
    return predict_period(dataframe, 'day', num_of_days, quantiles, train_end_date)

# def predict_week(dataframe, num_of_weeks, quantiles, train_end_date):
#     return predict_period(dataframe, 'week', num_of_weeks, quantiles, train_end_date)


def train_and_predict_skus(slice, sales_records_df, num_of_days, num_of_weeks, quantiles, yesterday):

    counter = 0
    total_steps = len(slice)

    predictions_list = []
    for index, row in slice.iterrows():
        counter = counter + 1
        print(f"训练进度: {counter}/{total_steps}")

        try:
            warehouse_no = row['warehouse_no']
            sku_id = row['sku_id']

            record_df = sales_records_df[(sales_records_df['warehouse_no'] == warehouse_no) & (sales_records_df['sku_id'] == sku_id)]

            # if record_df.shape[0] < 30:
            #     continue
            
            df = record_df[['ds', 'y']]

            prediction_day = predict_day(df, num_of_days, quantiles, yesterday)

            prediction = pd.DataFrame()
            prediction = pd.concat([prediction, prediction_day], axis=0)
            prediction['sku_id'] = sku_id
            prediction['warehouse_no'] = warehouse_no

            predictions_list.append(prediction)

        except Exception as e:
            print(e)
            continue

    return predictions_list
    

if __name__ == '__main__':
    num_of_days = 60
    num_of_weeks = 10

    confidence_level = 0.9
    boundaries = round((1 - confidence_level) / 2, 2)
    quantiles = [boundaries, confidence_level + boundaries]

    yesterday = datetime.strptime('20231230', "%Y%m%d")

    sales_records_df, sku_warehouse = load_data()

    predictions_list = []
    with multiprocessing.Pool(multiprocessing.cpu_count()) as pool:
        slices = np.array_split(sku_warehouse, pool._processes)
        # get number of processes
        print(f"总计进程数: {pool._processes}")
        results = [pool.apply_async(train_and_predict_skus, args=(slice,sales_records_df, num_of_days, num_of_weeks, quantiles, yesterday)) for slice in slices]
        for i, result in enumerate(results):
            prediction = result.get()
            if prediction is not None:
                predictions_list.extend(prediction)

    predictions = pd.concat(predictions_list, axis=0)

    predictions.rename(columns={'ds': 'view_date','yhat1' : 'estimated_sales', 'yhat1 5.0%' : 'estimated_sales_lower', 'yhat1 95.0%' : 'estimated_sales_upper'}, inplace=True)
    predictions = predictions[['view_date'
                            ,'sku_id'
                            ,'warehouse_no'
                            ,'estimated_sales'
                            ,'estimated_sales_lower'
                            ,'estimated_sales_upper'
                            ,'estimated_sales_cnt'
                            ,'estimated_sales_lower_cnt'
                            ,'estimated_sales_upper_cnt'
                            ,'type']]

    num_of_models = predictions[['sku_id', 'warehouse_no']].drop_duplicates().shape[0]
    print(f"总计训练: {num_of_models}")

    predictions.to_csv('result20231230.csv', index=False)

    # # 写入odps
    # odps_df = DataFrame(predictions)
    # today = yesterday + timedelta(days=1)
    # today = today.strftime('%Y%m%d')
    # partition_spec = 'ds=' + today
    # table_name = 'summerfarm_ds.np_estimated_sku_warehouse_sales_df'


    # for attemp in range(10):
    #     try:
    #         odps_df.persist(table_name, partition=partition_spec, drop_partition=False, create_partition=True, overwrite=False)
    #         print("成功写入odps")
    #         break
    #     except Exception as e:
    #         print(e)
    #         time.sleep(30)


