{"cells": [{"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-10-18 16:44:01 - INFO - Tunnel session created: <InstanceDownloadSession id=202410181644000331f60b176f42e6 project_name=summerfarm_ds_dev instance_id=20241018084357669g98gbxzqavt3>\n", "2024-10-18 16:44:21 - INFO - sql:\n", "SELECT  sku_id, warehouse_no, date, quantity, concat(sku_id,'_',warehouse_no) as ID\n", "    FROM    summerfarm_tech.app_estimated_sku_warehouse_record_df\n", "    WHERE   ds = MAX_PT('summerfarm_tech.app_estimated_sku_warehouse_record_df')\n", "    AND     date >= '20230924'\n", "columns:Index(['sku_id', 'warehouse_no', 'date', 'quantity', 'id'], dtype='object')\n"]}], "source": ["from odps_client import get_odps_sql_result_as_df\n", "import pandas as pd\n", "from datetime import datetime, timedelta\n", "\n", "days_13_month_ago = (datetime.now() - timed<PERSON><PERSON>(days=13 * 30)).strftime(\"%Y%m%d\")\n", "\n", "all_sku_df_query = f\"\"\"SELECT  sku_id, warehouse_no, date, quantity, concat(sku_id,'_',warehouse_no) as ID\n", "    FROM    summerfarm_tech.app_estimated_sku_warehouse_record_df\n", "    WHERE   ds = MAX_PT('summerfarm_tech.app_estimated_sku_warehouse_record_df')\n", "    AND     date >= '{days_13_month_ago}'\"\"\"\n", "\n", "all_sku_df = get_odps_sql_result_as_df(all_sku_df_query)\n", "if \"id\" in all_sku_df.columns:\n", "    all_sku_df.rename(columns={\"id\": \"ID\"}, inplace=True)"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.20      1.0\n", "0.50      1.0\n", "0.75      8.0\n", "0.80     20.0\n", "0.90    121.0\n", "0.95    204.0\n", "Name: days_from_now_since_last_order_date, dtype: float64"]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["# 为每个ID创建earliest_order_date和last_order_date列\n", "all_sku_df[\"date\"] = pd.to_datetime(all_sku_df[\"date\"])\n", "all_sku_df[\"earliest_order_date\"] = all_sku_df.groupby(\"ID\")[\"date\"].transform(\"min\")\n", "all_sku_df[\"last_order_date\"] = all_sku_df.groupby(\"ID\")[\"date\"].transform(\"max\")\n", "\n", "# 创建days_from_now_since_last_order_date列\n", "current_date = datetime.now().date()\n", "# 确保 last_order_date 列是日期时间类型\n", "all_sku_df[\"last_order_date\"] = pd.to_datetime(all_sku_df[\"last_order_date\"])\n", "\n", "# 创建 days_from_now_since_last_order_date 列\n", "all_sku_df[\"days_from_now_since_last_order_date\"] = (\n", "    current_date - all_sku_df[\"last_order_date\"].dt.date\n", ").apply(lambda x: x.days)\n", "\n", "# 构建onsale_days字段\n", "all_sku_df[\"onsale_days\"] = all_sku_df.groupby(\"ID\")[\"date\"].transform(\"count\")\n", "\n", "# 构建onsale_days_ratio字段\n", "all_sku_df[\"onsale_days_ratio\"] = all_sku_df[\"onsale_days\"] / (\n", "    (all_sku_df[\"last_order_date\"] - all_sku_df[\"earliest_order_date\"]).dt.days + 1\n", ")\n", "\n", "all_sku_df[\"days_from_now_since_last_order_date\"].quantile(\n", "    [0.2, 0.5, 0.75, 0.8, 0.9, 0.95]\n", ")"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4088\n", "3432\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sku_id</th>\n", "      <th>warehouse_no</th>\n", "      <th>date</th>\n", "      <th>quantity</th>\n", "      <th>ID</th>\n", "      <th>earliest_order_date</th>\n", "      <th>last_order_date</th>\n", "      <th>days_from_now_since_last_order_date</th>\n", "      <th>onsale_days</th>\n", "      <th>onsale_days_ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1001218743130</td>\n", "      <td>10</td>\n", "      <td>2023-12-24</td>\n", "      <td>2</td>\n", "      <td>1001218743130_10</td>\n", "      <td>2023-12-24</td>\n", "      <td>2024-08-25</td>\n", "      <td>54</td>\n", "      <td>2</td>\n", "      <td>0.00813</td>\n", "    </tr>\n", "    <tr>\n", "      <th>930127</th>\n", "      <td>1001218743130</td>\n", "      <td>10</td>\n", "      <td>2024-08-25</td>\n", "      <td>1</td>\n", "      <td>1001218743130_10</td>\n", "      <td>2023-12-24</td>\n", "      <td>2024-08-25</td>\n", "      <td>54</td>\n", "      <td>2</td>\n", "      <td>0.00813</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               sku_id  warehouse_no       date  quantity                ID  \\\n", "0       1001218743130            10 2023-12-24         2  1001218743130_10   \n", "930127  1001218743130            10 2024-08-25         1  1001218743130_10   \n", "\n", "       earliest_order_date last_order_date  \\\n", "0               2023-12-24      2024-08-25   \n", "930127          2023-12-24      2024-08-25   \n", "\n", "        days_from_now_since_last_order_date  onsale_days  onsale_days_ratio  \n", "0                                        54            2            0.00813  \n", "930127                                   54            2            0.00813  "]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["print(len(all_sku_df[all_sku_df['days_from_now_since_last_order_date']<60]['sku_id'].unique()))\n", "\n", "print(len(all_sku_df[all_sku_df['days_from_now_since_last_order_date']<30]['sku_id'].unique()))\n", "\n", "all_sku_df[all_sku_df['sku_id']=='1001218743130']"]}, {"cell_type": "code", "execution_count": 66, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAxYAAAJOCAYAAAAqFJGJAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAADpp0lEQVR4nOzdd3QUVRsG8GfTdtMTkpBGCCEBEpp0pEaqgILYUIo0FVA6glKkK9gQBOkgTYqiIFIFpIaOgLSEkkJI73WzySa53x8x87HspkCS3RCe3zl7YO7cmfvu7DLMu3fuHZkQQoCIiIiIiKgMjAwdABERERERPfuYWBARERERUZkxsSAiIiIiojJjYkFERERERGXGxIKIiIiIiMqMiQUREREREZUZEwsiIiIiIiozJhZERERERFRmTCyIiAxApVJV6P6TkpJw7do1rfLg4GBkZWVVaNsA8Pfff+PMmTPS8p07d7BixYoKb7c4J06cQHh4uEFjeBq3bt1Cfn5+keuPHTuGxMREPUZUvrKzs5GWlmboMIioHDCxICL6j0qlQlBQUImvp70wv3//PlasWIGuXbuiWrVqOHHihMb69PR0tGvXDkeOHCnze9m0aRP8/f3xzz//aJQPHjwYn3zySZn3X5KVK1diy5Yt0vLVq1cxa9YsAEBKSorWMU1ISChyX4mJiVAqlRplly9fRmBg4BPFNGLECOzateuJtimrjRs3QiaTlepVo0YNre1VKhV69OiBfv36IS8vT2cb3bt3x6VLl3SuU6vVpfpOP/5KTk4u8j09fPgQQghpOScnB4cPH0ZKSkqxx2LChAl46aWXtMpXrlyJxo0bF7stET0bTAwdABFRZXHt2jW0adOmxHqnT59G+/btda67ceMGMjIykJ6ejgcPHiA4OBiBgYE4f/484uLiYGlpie7du2PHjh1a+7C2tkbnzp3xyiuv4Ny5c2jevDmsrKyQmZlZbDyPXuQVmjhxIi5evIiBAwfi2rVrUCgU2L9/Py5duoR169aV+B6vXbuGpk2blljv8bbz8vIQGBiItLQ0GBsb4+bNmwCAiIgI5OXl4ebNm9i/fz+mTp2qsd3s2bMxZ84cnW3MmjUL9+/fx19//SWVDRs2DN27d8eiRYtKjBEAAgMDce/ePfTs2bNU9cuTs7OzRhL5119/Yc6cOTh37pxUtnv3bixfvlxrW4VCgX379qFjx45YsGABZs6c+URtR0ZGws/P74ljXrx4MSZMmKBVLoRAr169MGDAAEybNg1AQe/Yq6++it9//x29e/d+4raIqOpgjwURGdydO3cwcOBA1KpVC+bm5qhduzbee+893L59W6ozdOhQyGQynduPGzcOMplMulgt/JX48R6B0uwLKLj1JDQ0VOt1//79Yt9Hbm4uGjdujLZt26JPnz6YOXMmjh49Cmtra4wfPx4nT55EcnIydu3ahT59+sDERPu3nfnz5+PVV1/F119/DQC4cuUKAgMDpVebNm0wePBgjbLHXbx4ERs3bsQLL7yA+/fv45tvvsFPP/2EoUOHok2bNrhw4QI2btyIpKSkYt8PABw9elSjrcLXjz/+qLN+amoq2rdvjxMnTuCPP/5A+/bt0b59e8yePVtad/XqVdSrVw9CCAgh0K5duyLbv337Nn766Se89957GuUfffQRtm7dWmTSNWfOHI3egPr16wMAfH19i+01KK7n5FG9e/fGqlWrSlX3ccXd1qTLCy+8gIULFyI6OvqJ26pVq5Z0nB9/ubi44IcfftC5TldSAQAbNmxAUFAQ3n33XanMxcUFffv2xerVq584vrI4dOgQmjVrhtzcXL22S0TFEEREBnTt2jVhaWkpunbtKnbs2CFOnDghNm3aJJo2bSrkcrkICAgQQggxZMgQoeuUNW7cOAFAzJw5UyrbsGGDACCOHz+us82i9nXu3DkBoMTX6dOnde5XrVYLAOLIkSNPcST+Lz4+XiiVSp3runTpIsaPH1/s9uPHjxfm5ubC09NTeHp6CicnJyGTyaRlDw8PAUBcvXq1yH1cvXpVABChoaE61+/evVvnMSz05ptvipEjR0rL27dvFw4ODkIIIZYtWybq1asnrWvXrp2YPXu21j7S0tJEo0aNRJcuXUR+fr7GuuzsbOHn5yeGDBmitU6IgmMYGBgoAgMDxY0bN4SdnZ2YP3++VFbUKzc3t8j39Oh78fHxEWq1usS6hd/F0rzc3d1L3J8uxsbG4uDBg0+0zaVLlwQAcfv27VJvc/PmTWFpaSnmzp2rte7u3btCoVCIn376qcjtx48fL/z9/bXKFy9eLDw9PTXKoqKiivzuPeqll14SCxcuLLEeEekHb4UiIoP6/vvv4ejoiEOHDsHY2Fgq79OnD+rUqYOFCxdi3759OredMGECli5dii+++AIzZswot5h+/PFHyOVyrfL8/HyMHDmyxO2joqIQFBRUqrZcXFxgZ2cHlUqFsLAwjXXm5ual2ocunTt3lo7bH3/8gaFDh0r7T0hIgJOT01Pvu7RWr16t8Su2g4OD9Pc7d+5o9Bp17dpVY9u0tDT06NED6enpOHTokFYPk5mZGTZv3gx/f3/k5uZi/fr1Gp+Zo6MjHB0dAQA7d+5Eeno6PvjgA7i4uODDDz9EnTp18Omnnz7xe8rNzcWMGTMwffp0nT1Oujx+K9Thw4cxe/bsEm+Fio+P1zh+HTp0gL+/P5RKpVavh0qlQkZGhrRsaWmpccxUKpXO71NhT06hhQsXat2mBhT04nXu3BkdO3bU+W+tTp06WLRoET744ANkZGRg7NixWnUK43j0e/7ov/lC58+fx9tvvw0vLy+cOnVK534KTZ48GQMHDsTo0aNhbW1dbF0iqnhMLIjIoAoHfObl5WlcZNjZ2eH7778vcruJEyfihx9+wDfffIMpU6aUSyzOzs4YOXIkbty4UWSdkSNHwtXVtdj9DBkypNRtrly5EqNGjcLNmzfRsmVLqby4MQelsX//fq2L8eJu/6oI77zzjvQeDhw4gAULFkjrvLy8cODAAQDAwIEDNbb7559/8O677yI3NxfHjh2Dm5ubzv23aNECR44cwRtvvIF69ephxYoV6NWrl0adnJwczJgxAy+//DJcXFwAFAw+trW1far39MsvvyAmJgb9+vUrVX1jY2OkpKSgSZMmAAq+57m5uZDL5VJZoccHbyuVSgQEBAAouL1NpVLB398f9evXx4MHDzTqvv766xrL9+7dg4+Pj1Y8a9euLXJ80Msvv6yz/Oeff8ZHH32Edu3aYdeuXTqTAQD4+OOPIZPJMH78eOzYsQMbN25EnTp1NOpcuHABXl5e0rKzs7OUyCQkJGDhwoVYunQpunXrpjH4vyg9evSAhYUF1qxZo5dJCYioeEwsiMigevbsiT///BNt27bF8OHD0blzZ/j6+gKA1n31hSZNmoQlS5Zg0aJFmDRpUpljmDNnDubOnVvq+oW/Io8ePVrnWIPjx4/rnP2mOC1atJAGQj9+wfk0XnrpJaxcuRJAwdSv06ZNw8WLFwEUJHOlGaQOQOMisDyZmZlJn/Pjv6RHRkaifv36+Omnn+Dv749bt24VuZ+2bdvi2rVrGDNmjNRD8aivvvoK9+7dw9ixY6VepMzMTCQlJWn0KtnY2BSZwDxqx44d6NatW6l/HX/jjTcQEhKCYcOG4dixY5g+fTo8PDzw22+/wcPDA1u3bsXKlSuxYcMGrYtwT09PHDp0CAA0vk/nz5/XGFdQq1YtbNy4UaNOUclvjRo1pOP+OFNTU53lgYGB+PjjjzFp0qQSe9GmT5+Os2fPYtasWahevbrW+g4dOuDw4cPSskwmw8qVKxEdHQ0PDw84OzvrHFNTFGNjY/Ts2RM7duxgYkFUCTCxICKDGjlyJEJDQ7FkyRKMHj0aQMGvmD179sSoUaPQunVrjfqTJ0/G4sWLARTcKlIexowZg3fffRd+fn746aefpIvurVu3Yu3atVixYgXi4uI0trGxsUGXLl2K3GdqamqJg22LusCrTI4ePQp3d3et8r///htjxowpdttffvkFv/zyi7T86K1QxenTpw/69OkDANi7dy+ys7MRHx+Pjh074tdff0WjRo0AFFzEKpVKuLi44LffftPaz9mzZzFv3jzY2Nhg3LhxGusCAgKwYcMGafmdd97Bjh07io0rJycHx44dk2ZDKo3U1FScPHkS8+fPh4ODA7755hsMHjwY169fx3vvvYd///0Xs2bNQq1atUq9z8Kel0c5OjrqnK62PHz55ZcA/j/jF1DQI/b555/j6tWrUr2uXbvC2toaLVq0kHqjHmdkZASFQqFVrlAosH79evTp0wdmZmZPFF+7du2wYcMGxMfH6+UWPyIqGhMLIjIomUyGr7/+GlOmTMHevXtx6tQpHD9+HBs3bsSmTZvw/fffa8xQs3jxYqxZswYnT57EN998g65du2pd4D/pLT+P3o//8ccfS7d6qNVqODk54dChQ1i3bh0sLS1hbGwMpVKJwYMHY8CAAUXuc+fOnfjwww+LbVfomCa2vJw4cUJrmtGnmXbU29tb50VvacaQjBw5Upo5aceOHRqJSGhoqJRYhYeHa42xKFTYYxIREQGZTIZu3brBzs5OWl9Uz0FUVBTeeecdNG7cGL1798aePXukBwb26NEDDRs2xHfffQcA0riAkkRERECpVGr1LBTHzc0Nhw8fxpgxY7BlyxYYGxtj+PDh2L59O4YOHYrVq1djwYIFOHTokF6mai3LdLvGxsbSZ/bzzz/D19dXIzlWKpVPPc7B3t4eb7311lNtW6dOHQghcOvWrSfuKSSi8sXpZomoUnB0dMSwYcOwYcMGhIWF4fz586hbty4+/fRTjSlAN2zYgA8//BDLly+Hu7s73nvvPa2eCwsLCwAFiYEu2dnZsLS01LluxYoVuHz5Mi5fviwN7l2zZg2aNWuGhQsXIiEhATVq1NB5gVb44LzCX2Q9PT11TuV5/PjxJzw6T2bJkiUa7e3evRu2trZacZTHLVePu3//PmQyGX7//XesXr1amsa1f//+SExMhEwmw8GDB9GsWTPpYWzNmjUrcb9Hjx7FCy+8oJFUJCUl6fyFOjMzE71794axsTH27dtX5Gf9pGJjYwFAI4bSMDExwVdffQUvLy8MHToUKpUK586dw9y5c6FQKODi4oK33noL/fr103oQYHlbu3atzumDAwMDUbNmzVLv5+jRoxoX8fn5+UhNTTVIj0HhjwIxMTF6b5uINLHHgogMJiwsDC+++CLmz5+v9et+69atMX/+fPTr1w/BwcFS+eDBgwEAtra22LhxI7p27YqhQ4di3759Uk9F4YVGUbciRUVFFXkBNHz4cI3lwtuAPvroI8yaNQvx8fHIzc3VGiQMACEhIQCAmjVrSg+G0ycfHx+NY/W4onpyhgwZgo0bN5ZLDJ6entLtMrt374a/vz/Mzc1x4cIF6UJ05cqVCA8PL/U+1Wo1tmzZonXrVXx8vM7bghQKBfr374++ffvCzc0NRkbl8xta4fEr7XMoDh06pDMB3bFjh9ZtV3K5HHv27EGvXr2wf//+ckuGHvc0Yywed/v2bVy4cEG6JREoeDp6fn6+zs/jac2bNw+jRo3SOVZDF31PTkBE2phYEJHB1KhRA0IIrFmzBoMGDdIaGHr37l2YmJjA29tb5/adO3fGhAkTsHjxYixZsgQTJ04EADRv3hzm5ubYsWOHlIgUioyMxIULF7RmIir06BiLzZs3Y/PmzQAKHqq3Zs0azJs3D1u3btU5iPXPP/+En59fqQYBl9bVq1dL9QRsoGAa05ycHK3yxwdvP664GZKCg4OhUqm0yiMjI3XWNzU1ha+vL9LS0rB06VK4uroiMTERU6ZMwfLly/HRRx8hJCQEjRs3LtV7AoBly5ZBqVRixIgRGuURERE6xxUYGxtj8uTJ0nJRF5wZGRnIz89HcnKyzvv+H1d40ZyYmFiquLt27Ypz585BLpdDLpfjwIEDaNCgAYKCgrB48WJcvXoVS5cuhVwuxyuvvAJra2v06dMHd+/eLfYzP336NDp06FCqGCrC1KlT0b59e40JACIiIgBoz2xVnNDQ0CJvqUtJScHs2bPRtm3bIm+TK5ScnAxA99gTItIvJhZEZDAmJiZYtmwZ+vfvj1atWuGjjz6Cr68vsrKycOTIESxfvhxz587VOdtPoYULF+Lw4cOYOnUq/P390axZM9jY2GDWrFmYNm0a+vXrh0GDBsHGxgZ3797Fd999BysrK8ycOVPax40bN6SnfO/YsQNbtmzBvXv3EBERIfVY3Lt3DzExMTAyMsJvv/2G119/XSO5SEtLw7JlyzTm+Fer1TovnErza31ISAj69OmDe/fu6Xy6ti61a9fWWR4UFAQjI6OnGixe0kVdUcaNGwdHR0cMHDgQMpkMQUFBGDt2LHx9fREQEKCVJBTl9OnTmD59On788UeNwd+RkZFIS0tDvXr1StxHUYnFpUuX0LlzZwAFyUtJPDw8YG9vX+rPAygYA/L3339j8+bNuH37NhYvXgxra2sYGRnBysoKQggcPHgQM2fORP369fHpp58WeYtaaGgoXn/9dezdu/epnzYdERFR5MV8UbcOPurbb7/F0aNHceHCBY3y27dvQ6FQwNPTU2sbIQRCQkIQHh6OO3fuoEuXLrh69SqSk5Ph4eGBKVOmIC0tDZmZmVJPzc6dO2FhYaExBXNRgoKCIJPJSvVdIKIKpu8n8hERPe7MmTPizTffFG5ubsLU1FQ4OzuLLl26iEOHDkl1inpathAFT4k2MzMTderUEenp6VL5tm3bRIcOHYSdnZ0wMzMTNWrUEEOHDhVhYWEa22/ZskVYWVmJli1biqFDh4pvvvlG7N+/X3z++efC3d1dbN26VdjY2Ih33nlH3Lx5U9SuXVvUqVNH7N69WyM+b29voVKphBBCrF27tsQnLRfFyclJABC9e/cW169fFxkZGeLq1avi9u3bolmzZmLixIlPdHx3794tbG1tn2ibsjx5e+HChcLCwkJcu3ZNKlOr1aJVq1Zi0KBBwsLCQiiVShEVFSXu378vGjZsKBYsWKCxj/z8fLFmzRphbm4uJk+eLIQQ4scffxRLliwR586dE1OnThUKhUJkZmaW+F5Wr14tunTpIi2//PLL4pNPPhF5eXkiNDRUREVFlbiPQm+99ZZ46aWXSqynUqlE48aNBQBRp04d8fnnn4vo6GghRMF3Q9eTpmfMmCEsLS3F+vXrtfZXp04dAUC8/PLL4ubNmxrrSvPk7aysrFI9/buop1inp6eLcePGCTMzM7Fr1y4hhBCTJk0SGzduFFeuXBFdunQRHTt21LntiRMnBAAhl8tFy5YtxUcffSTWrVsnrl69KnJycsSNGzeEtbV1qWN53KhRo0TLli1LVZeIKhYTCyIiUXAh+7hly5YJd3d38fbbb4vp06dLdRISEsSIESPEDz/8IIQQIjU1Vbz88svi4sWL0ra6Lh4LHT9+vNjE4o8//hBnzpyRlpVKpXThZWdnJ/76668nem9Pk1ioVCoRGBgocnJydK7Pzs4W8fHxWuVXr14VCoVC/Pnnn1rrMjIyRK9evcTIkSOFEEKsXLlSGBsbi9q1a4ugoCCp3pUrV0SDBg2EtbW1WLVqlVS+b98+0bhxY2FlZSXs7e2l4/+kChOLp7Fr1y5hamqq870/LiYmRkomHlXcd+Phw4c6j/nOnTuLTB6eJLEorp63t7fOi/nNmzeL6tWrC29vb3H69Gmp/IsvvhDe3t5CLpcLT09PcerUqSL3Xdx3SYiCxCUoKEgEBgaKwMBAERsbW+z7KZSbmytcXFzE999/X6r6RFSxZEJU4HyHRERVgBCCA0OfQGxsLJydnXWuy8rKQnp6erEDcnNzc/Hjjz9iwIABpR64qy/5+fnw8/PDxx9/jPHjxxs6nCeSkZEBc3PzIp+cXZSHDx/i4MGDGDJkCORyeQVF93T279+PoUOHIiwsrMIGvBNR6TGxICIiegK7d+/GpEmTcOfOnSd+mBuVr9atW2PQoEEYO3asoUMhIvA5FkRERE/k9ddfR7t27aQZw8gwDh06BEtLS4wePdrQoRDRf9hjQUREREREZcYeCyIiIiIiKjMmFkREREREVGZMLIiIiIiIqMz45O2nkJ+fj6ioKFhbW3MKSiIiIiKqsoQQSE9Ph5ubG4yMiu+TYGLxFKKiouDh4WHoMIiIiIiI9OLhw4eoUaNGsXWYWDwFa2trAAUH2MbGRm/tqtVqHD58GN27d4epqane2mUMjKEyts8YGENlap8xMIbK1D5jYAzlKS0tDR4eHtL1b3GYWDyFwtufbGxs9J5YWFhYwMbGxqD/OBgDY6gM7TMGxlCZ2mcMjKEytc8YGENFKM3t/1Vu8HZOTg7q16+PEydOFFnnypUraNGiBRQKBZo2bYoLFy7oL0AiIiIioiqoSiUW2dnZGDhwIAIDA4usk5GRgV69eqFHjx64c+cOevTogVdffRXp6el6jJSIiIiIqGqpMonF7du38eKLL+L+/fvF1tu5cyfMzc0xf/58eHp6YsGCBbCxscHOnTv1FCkRERERUdVTZRKLU6dOoVOnTggICCi23vnz59G+fXvpPjGZTIa2bdvydigiIiIiqpSEEIYOoVSqzODtUaNGlapeREQEGjVqpFHm5uaGW7duFblNdnY2srOzpeW0tDQABYNx1Gr1U0T7dArb0mebjIExVNb2GQNjqEztMwbGUJnaZwzPdgzZufm4H5eBwJh0BEanIygmHffiMnBqckcoTI0rMlSdnuTYycSzkgI9AZlMhuPHj+Oll17SWtelSxe0b98ec+fOlcpmzZqFM2fO4O+//9a5vzlz5mjUL7Rt2zZYWFiUW9xERERE9PzIywcSs4HgNBmC02SIyJQhVgXkC+0ZmD5plIuaVvqPUalUYsCAAUhNTS1xNtQq02NRWgqFAiqVSqMsOzsb5ubmRW4zbdo0TJo0SVounM+3e/fuep9u9siRI+jWrZtBp0xjDIyhMrTPGBhDZWqfMTCGytQ+Y6h8Mew7dAS+zdsiPjMXUSkqhCVm4mJYMm5HpyMvX/s3fjtzU/i6WMHXxRp+rtbwdbGGj5MVzEz0P4qh8E6d0njuEgt3d3dER0drlEVGRsLd3b3IbeRyOeRyuVa5qampQb6khmqXMTCGytg+Y2AMlal9xsAYKlP7jEF/MQghEJ+ejXtxGbgfl4GQ+AxEpqgQlZKFqJQspGSZABcv6txWbmKE+m426ODjiBc87FDfzQYuNopSPTdCH57kuD13icWLL76IL774AkIIyGQyCCFw5swZzJo1y9ChEREREVElpczJxYNEJR4mKfEwOQsPk5SISFbiYVIWHiYroczJK3Z7a4UJ3O3M4WZnDnc7czStaYdWXtXgZmsOI6PKkUSU1XORWCQkJMDa2hpyuRxvvfUWPvvsM8ycORMjRozAmjVroFQq8fbbbxs6TCIiIiIyIJU6D+FJSoQmZOJBYiZCE5QITchAWIISMWmqYrc1kgGeDpbwqW4Fbycr1LAvSCCqW5ngxoXTeLNPd4P33FS05yKxcHJywoYNGzB06FDY2Nhg3759GDVqFL799ls0bNgQ+/fvh5WVAUbDEBEREZFe5eTmIyolC+FJSjxMVhb8maREYHQ6whIzUdy0RrbmpqhZzQIe1czhYW+BGtUs4GFvDo9qFqhhbw65ifasTWq1GvefiyvuKppYPD7R1ePLrVu3xtWrV/UZEhERERHpQeF4h/AkJcLi03EsQoaTu24iMkWFh0kFPQ86xktLrBUm8HK0RM1qFqjtaIla/728HCxhb2mmvzfyDKqSiQURERERVV3pKvV/PQ1ZiHik1yE8SYmI5Cxk5+Y/UtsYeBilsb3C1Age9hb/9T4U9DbUdbZGfTcbOFiaVZqB088aJhZEREREVCmp1HmISFbiSngKzt5PQEhCJsKTlEhRFv/QNiMZ4GprDg97BURGIto0rgsvJyvUsC+4jcnJSs7koQIwsSAiIiIig0lVqvEgKRMPEgt6HB4k/v/vMWmqIsc8VLM0g8cjYxxqVrOQeiFc7RQwNTaCWq3GgQMH0Oul2lV+4HRlwMSCiIiIiMpVVk4e4lKzEJEJBNxPRFp2HlKUaiRl5iBZmYPEzBxEJCnxoBS9D1ZyE9RxtkIHH0c0dLdFTQcL1LC3gJWcl7GVDT8RIiIiIiq17Nw8xKSqEJWiQnRqFqJTCx4EV/hnVEoW0lS5/9U2Aa7/U+I+Ha3k8HSwgGc1C9R0sICngwVqVrNELQcLVOOYh2cGEwsiIiIiAgDk5QvEpRckDQXJQtZjCYQKCRnZpdqXqbEM5kb5cK1mDXtLM1SzNIOdhRmqWZjB3tIM7nbm/yUQFrBk70OVwE+RiIiI6DmSoszB3dgMhCVkIjQxEw+TlIhOVSE6JQux6dnIK24u1v/ITYzgZmcOV1sF3OzM4WargOsjyy62CiiMBA4ePIhevdpyfMNzgokFERERURUkBJCYkY3ItAxpQPSF0ERcDE0q9jkOJkYyONso4GangKutOVztFHCzNddIJOwtTEu8PUmtLn7sBFU9TCyIiIiInkH5+QLxGdmISC54dkPhKzo1C1HJWXiQaIzs8yd1butuZ47aTpao5VDwIDg3u/8nEE7WchgbcUwDPTkmFkRERESVVE5uPmLTVIhNUyE6VYXg+Azci83Andh0hCcqkZOXX8zWBcmBm60CNR0sUMvBEnWdrdGtvjM8qlno5w3Qc4WJBREREZGB5OcLxKVnazw5+mHy//8em1b8QGljIxlcbRWoYW+OGvYW0niH6lamuPfvRQx47WVYWyj09G7oecfEgoiIiKgCqdR5iErJQmRKFiKTs/AgSYmQ+AyExGfiQZISObnF9ToAZsZGcLaVw8VGAU8HS9R1tkJdZ2t4O1nBxbbgQXCPU6vVSL8HKEyNK+ptEWlhYkFERERUDlKValyPTMHVB0k4ftcI6x+eR1RKdonTsxobyeBmp5CeHO1RzUJ6knQNe3M48DkO9IxgYkFERERUSkIIJGTkIDypYJalB4lKhCRk4kZECsISlY/UNAKQJi2ZmxrD3d4c7nbmqFnNArWdLFHbyQpeDpZws1PAREevA9GzhokFERER0WMSMrJxJyYdoQmZCE9SStO1hicpoczJK3K7mtUs0MjNBsZpkejethk8Ha3hbl+66VmJnnVMLIiIiOi5lZCRjdtRaQiMTkNEchbCk5QIjE5DXHrRty/JZICbbUHPg6eDBWo6WKCBmy0au9vC3tIMarUaBw5E4OUGznwwHD1XmFgQERFRlZefLxCepMTt6DTcikrF7ag03I5OK3LWJZkM8KxmAW8nK9R0sIBnNQt4OliipkPBuAe5CQdFEz2OiQURERFVKfn5Aulq4FZUGuIy1Dh8OxZ/3YpBuipXq65MBng5WMLPzea/8Q7mqOdiDV8Xa1jKeZlE9CT4L4aIiIieGSp1HuLSshGTpkJMmgqxqQV/xjzyZ1y6Cuo8E+DyeY1tzUyM4OtijfquNmjgZoP6bjbwdbFhAkFUTvgviYiIiAwuP18gJUuN2EcShuhUlbQc89/fk5XqUu1PBgEHKzlcbBVo5G6L15vWQNOadjqf+UBE5YOJBREREVW4/HyB2HQVQhMyEZqQibCETIQmKBGRrERiZg6SMnOQly9KtS+5iRFcbBVwtlHAxUbx2N/lcLQwwT8Bx9H71Zc4eJpIj5hYEBERUbkQQiA1S43wDOCPa1EITcz6fyKRmAmVuvgnTANANUuz/5IEuVby4GJb8Hdb8+KnblWr1bjGjgkivWNiQURERKWSny8QnaZCZHIWolOzEJWiQnRqFiKSsxAcn4HI5Czk5gsAJsCNm1rbGxvJ4GFvDi9HS9RytISXoyVqVrOAk7UcDpZyVLM0g5kJMwKiZxUTCyIioudcRnYuYlJVUKkLHvwmBCAgkJObj2sPU3DtYQqC4zMRmpBRql4Ha1MBP/dq8HG2Rm1HS9R2soSXoxVq2JtzjANRFcbEgoiIqIoSQiAtKxdRqVnSAOjoVBViUrP++7PglZ6tPQ1rUUyNZXC1NYerrQLuduZwtVPAza6gF8LTwRI2ZjIcO/IXevVqyfENRM8ZJhZERETPqPx8gYSMbESkZCEyOQvhiRk4F2KE3VuuIDo1G5EpWcgoZdJgLTeBuZkxZDJABhmMZIBMJkMdZyu0rFUN9Zyt4V3dCh725jApptdBrS7drE1EVPUwsSAiIqqk8vMFIlOy8DBZicjkLET+l0BEpmQhKqVgjENO3uO3JhkBsQkaJfYWplIvQ+EAaBdbBVxtzaVB0VZ8lgMRlRHPIkRERAZWmEDcjU3H3dgM3ItNx924dNyPK3lMg5EMcLFRwN3eHK42CmQlRqJj84ao6WgFdztzuNuZw9zMWE/vhIieZ1UqscjKysLo0aOxc+dOWFhYYPz48Zg+fbrOugcPHsRnn32GkJAQ1K9fH0uXLsWLL76o54iJiOh5IYRAfEY27sdm4F5cBu7HZeBeXDrCEpRIyMj+bzYlbWbGRnC3N5eSBOnv//3pYquQBkSr1WocOPAQvVrW4PgGItK7KpVYTJkyBdevX8fFixcRGRmJt99+G15eXujfv79GvQcPHuCNN97A/Pnz8fbbb2PHjh145ZVXcPfuXTg4OBgoeiIiqgpy8/IRn/n/wdG3o1NxKSwZd2LSkZpV9PgDU2MZajtaoa6LNepWt0IdZ2vUdbZCzWoWxY5pICKqLKpMYpGVlYX169fj4MGD8PPzg5+fHyZOnIiVK1dqJRYXL16EpaUlJk+eDAD47LPP8M033yAgIACvvfaaIcInIqJnROFMS/EZKsSlZ+NhkhIh8Zm4H5eOG2HG+OTC30X2PhjJgJrVLOBT3Ro+1a1Qp7oVajtZwtlGASdrOadiJaJnWpVJLK5duwa1Wo22bdtKZe3atcPChQshhNB4QmejRo2QlpaGvXv3onfv3ti3bx+Sk5Nha2triNCJiKgSeXSK1kcfAhedosL9+AzcjU0vZtyDDICAiZEMzjYKuNoqUNPBAq29qqGRux1qO1lCYcrxDkRUNVWZxCIiIgKOjo4wMzOTytzc3KBSqZCYmAhHR0ep3NfXFytWrEDfvn1hYmICtVqN999/H/7+/oYInYiIDCArJw8hCRkIjs9ESHzBn8FxGQhLzIQyJ6/E7W0UJnC0lsPdzhy1HS3hWc0c8SG38G6vl+DhYA0jI1mJ+yAiqkqqTGKhUqkgl8s1ygqXs7KyNMpv3LiB8ePHY968eejduzd+++033Lp1C1lZWbCwsNDad3Z2NrKzs6XltLQ0AAWD5PQ5X3dhW4acI5wxMIbK0j5jYAylaV8Igdj0bIQmZCIkPhPBCUrp71GpqmL3WTBFq0J6udgoULOaOeo5W8PdTgH5Yz0ParUaR5JuwsnSBHl5ucgrOTcpd4b+HBhD5WifMTCG8vQkccuEELpvBH3G7Ny5E2PGjEFsbKxUFhgYiPr16yM+Pl6jx2LkyJEIDw/HwYMHpbJu3bqhffv2mD17tta+58yZg7lz52qVb9u2TWciQkRE+iMEkKYGElRAokqGhGwZElRAXJYMcVlAdn7RPQeWJgLVzQFncwFn84K/V1cI2MsBUw53ICKCUqnEgAEDkJqaChsbm2LrVpkeC3d3dyQmJkKtVktT7EVGRkKhUGjN9PTw4UM0btxYo6xp06a4ePGizn1PmzYNkyZNkpbT0tLg4eGB7t27l3iAy5NarcaRI0fQrVs3g00jyBgYQ2VpnzE8fzEkK3NwKyodoQmZCE9S4mFylvRncc96MDaSwcO+4Hal2k6WBX86WsDL0RLVLM2K3O5JPS+fA2Oo/O0zBsZQngrv1CmNKpNYNGnSBCYmJjh79qw0VuL06dNo1aqVxsBtAPD29satW7c0yoKCguDi4qJz33K5XOs2KwAwNTU1yBfEUO0yBsZQGdtnDFUrhqycPNyMSsW92AwkZmQjMTMHcekq3IxMQ3iSssjtjGSAnZmAr7sDPB0tUbOaJbwcLeFTveDvZib6636oCp8DY6ga7TMGxlAeniTmKpNYWFhYYMiQIfjkk0+wadMmxMXFYenSpVi9ejUAICEhAdbW1pDL5fj444/RpEkTLF68GH379sWff/6JAwcO4PTp0wZ+F0REVZ9KnYeQ+Ezci0vHg0RlQa/Df6/oNBWKu0HXy9ESdapbwdPBAjUdLFGzmgU8q1nAydIERw8fQq9eLZ7J/7iJiKqCKpNYAMCiRYvw8ccfo3Xr1rCyssL06dPRr18/AICTkxM2bNiAoUOHws/PD4cOHcKUKVMwc+ZM+Pj4YNeuXWjTpo2B3wERUdUhhEBkShZuR6XhdnQa7sSk405sQTKRV8RzHgDAyVqOhm42qG6tQDUrMzhYmsHXxQaN3G1ha6E7aXhWB0USEVUlVSqxsLKywubNm7F582atdY+PUe/UqRMuX76sr9CIiKq8DDVw5HYcrjxMxc2oVNyOSkOaKldnXVtzU9R1toKXY0Gvg0c1C9Swt0DNahZwtDLTuoWViIgqvyqVWBARUcUTQiA6VYWgmDQExaTjTkw6bkamIjjeBLh8TaOuqbEMdapbw8/VBn6u1qjnYo16ztZwspYzeSAiqmKYWBARUZGycvIQGJOG21H/3coUk46gmKJ7IrydLNG6tgOaeNihgZsN6lS31uvAaSIiMhwmFkREBCEEYtJUuBubgbsx6bgVlYpbUWkIjs+AruEQxkYyeDtZop6LDXxdrOHtaI7EO5fR77V2HDxNRPScqpSJRWRkJNzd3Q0dBhFRlZOdm4fI5Cw8TM5CcFwG7sWlFyQTselIL6IXwtHKDPXdbOHnYg1fV2vUc7aBd3VLyE3+/+RptVqNAyH6ehdERFQZGTSx+OijjzB16lR4enpKZeHh4WjQoAGsrKzwxRdf4P333zdghEREz56kzBzcj8vA/bgMPEjMRGRKFqJSshCdqkJMMdO5GhvJUMvBAnWdrVHf1QYN3G3QwM0W1TkegoiISsGgiYVcLscbb7yBixcvwti44JevmjVrIj09HcePH0evXr0wfPhw/odGRKRDbl4+gmLScSEkAUeDjbBl3UWEJCiRlJlT7HbmpsbwqGaOWg6WqOtsjTrOVqjrbI3aTpq9EERERE/CoInFokWL0L59e3z33Xf47LPPNNZ16tQJ2dnZUKvVMDMzM1CERESGJ4RASEJmwcxLcRm4H1/QGxGWoEROXv5/tYwApEjbuNuZw7u6FWo7WqKGvTnc7MzhaquARzULOFhyOlciIip/Bk0sjI2N8eOPP6Jbt24YP348FAqFxnr+x0dEz5vkzBzci8vA7ahUXH2YgtCETDxIVCI1S/cD4KzkJmhe0w5yZRxebvMC6rnaoraTJSzMKuUQOiIiqsL0/j/Pxx9/rLEshEBubi7eeecdjQHbiYmJsLS0ZG8FEVVJKcocadD0/biCP+/GZiAhI1tnfbmJERq626JOdSv4VLeCd3Ur+DhZwd3OHHl5uThw4AB6veDKGZmIiMhg9J5YODs7ayzLZDJ88sknWvXc3Nwwbtw4fYVFRFQhUpQFPRB3Y9Nx779E4l5cBuLTdScQQMFtTPVcrNHEww6+LtZwtzcv9nkQeXkVFT0REVHp6T2xmD17tsayjY0NEhMTNX5li4qKQsOGDfHpp5/qOzwioqeSmqVGaGS6lEAUTuNaUgJROHC6TvWCP72rW8FKztuYiIjo2WPw/70yMjIgHpv70M3NDaamprhy5QpefPFFA0VGRKQpNy8fcenZiEjO+v/tSzFpuPnQGGnnjhe53aMJhE/1///JBIKIiKoSg/+vVtQA7ebNm+PSpUtMLIhIr4QQSHzkORD34zIQHJ+BkPhMxKSpkKfrMdQoOI+525n/lzhYoY6zNRMIIiJ6ruj9f7ulS5dCpVJJy0IIfPfddzAx0QwlLS0NFy9e1Hd4RPScyM8XiEzJwv34jIIpXAtf8RlIUeqegQkATIxkcLVToLajFepUt0JtRwvE3/8X773WHdWszfX4DoiIiCoXvScWd+7cgVKplJaHDBmCe/fuadWzs7PDpUuX9BkaEVVB6rx83I1Nx42IVNyOTkNMqgqRKVkIic9Ellr3qGeZDKhhbw4fp4IZmHyqW8HbyQo17C3gZC2HsdH/e1rVajUOxP4LawV7JYiI6Pmm9/8Jly9fXqp6MTExaNSoEXJzc7V6M4iIdMnLFwiOz8D1iFTciEjB9chU3I5KQ3Zuvs76psYyeDlaFiQPTv9N4VrdCrUdrWBuxidQExERPYlKe8Xu4uKC4OBgJhVEpJM6Lx/34jJwOV6Gawfv4FZUOm5GpUKZo90LYa0wQeMatmjobgsPews42yjg7WSJmtUsYGKsewpXIiIiejKV+qrdxsbG0CEQUSWgUufhVlQarj1Mwb8PU3AnJh0hCRlQ5wkAxsD9B1JdCzNjNHS3RWN3WzSqYYvGNezgWc0CRka6J4ogIiKi8lEpEovly5dDJpNpPZWbiJ4/+fkCIQkZuBqegn8jUvDvw1QERqchV8dsTJZmxnAyy0WHhp5o4mGPxjVsUdvJSmMMBBEREemHwROL8PBwzJkzB7/99puhQyEiPcrOzcPDpCxce5iCm5GpiEhWIiI5C+FJSp23MzlamaGJhx1eqGGHBu42qOtsjeqWJjh48CB69fLVeMgmERER6Z9BE4uQkBC8+uqrGDt2LAICAhAYGAhHR0c4ODjA3d0dPj4+MDLi/c9Ez7LM7FwExxdM5XojMhW3ItMQnqREbLoKQtcjIQAoTI3QyN22IJHwsEMTDzu425lrPfdGrS56WlgiIiLSL4MkFteuXcPKlSvx22+/4fPPP8e4ceNgamqK/v37IyEhATExMQgNDYVMJsPUqVMxbdo0Q4RJRE8gIzsXMakqPEjMxM3INNyITMXtqFREpaqK3Mbc1Bj13WzQ1MMOtRwtUcPeHDXsLVDLgYOqiYiInjUGSSwyMzNhbW2Nf/75B7Vq1UJeXsFtD1u3btWot2vXLgwZMoSJBVElkp8vEJuuwo2HyfgrQob9268hMCYdD5OyitzG0coMtZ2sUN/VBo1r2KKWoyU87C3gaGWm1QtBREREzyaDJBbt2rVDu3btNMp0XVz4+vpCLpfrKywiekyaSo17sRm4H5eO21FpuBSWjPtxGcjJK3wuhDGAOKm+tdwEbnbmaOBmgwbutmjoZoN6LtawszAzSPxERESkPwYfvF1ICIH+/fujQ4cO6N27Nzw8PHD+/Hm8/PLLhg6NqMoTQiA6VYWgmDTcikzDzahU3IxMQ2SK7l4IYyMZvBwsYCvS0a2FLxrXsEd9NxsmEERERM8xgyQWGRkZWL9+PT744ANYWlpK5S1btsRff/2FGTNm4IMPPsCsWbMwfPhwQ4RIVKVFpWQh4H4CbkSk4k5MOgJj0pCuytVZ18VGgTrOVqhT3RrNPAtmZXK1VUDk5+HAgQPo1a4WZ2QiIiIiwyQWCQkJOHjwIBYtWoStW7eibdu2AIBJkyZh0qRJCA0Nxbhx49C3b18cOHCAt0MRPSUhBB4mZeF2dCpuRaXhdlQabkenIVrHgGoTIxm8nazg52qNhu4FT6n2c7WBrbnupEGdrz0lLBERET2/DJJY1KpVC4cOHcLPP/+M1157DWvXrsU777wjrffy8sKePXvw2muvYezYsVizZo0hwiR6pggh8CBRiSvhyQXTukalITAqDenZ2j0RRjLgBQ87tKxVDX6u1vB1sYG3kxXMTDgTExERET0dg46xGDRoEJydnfH222/jyJEjGuuMjIywZcsW1K5dG59++il8fHwMFCVR5ZSZnYt7qTKsOhmCfyPTcDU8BYmZOVr1zIyNUNelYEamBm62qO9mAz9XG1jJK80QKyIiIqoCDH5l0a1bN8ydOxe///47WrZsqbHOzs4On376KZRKZan2lZWVhdGjR2Pnzp2wsLDA+PHjMX36dJ11w8PDMWrUKJw8eRKurq5YsGAB+vXrV+b3Q1QRHu2NuBKejCsPUnAnNh15+cbA7ftSPTNjIzRwt0ETDzs0cLNFAzf2RBAREZF+GDyxAIDx48dDFPEI3qlTp5Z6P1OmTMH169dx8eJFREZG4u2334aXlxf69++vUS8vLw+vvvoqvL29cf36dQQEBGDQoEHw8/NDo0aNyvReiMpDZnYurkek4kp4Mq6GJxfZG2FnJtCmjgua16qGpjXt0dDdBnITYwNETERERM+7SpFYALqfY/EksrKysH79ehw8eBB+fn7w8/PDxIkTsXLlSq3E4sCBA3j48CHOnj0LKysreHt7Y9euXTh37hwTC9K7onsjNJPtwt6IZjXt0aymPRq5WeHqmWPo1esFzspEREREBmeQxKJx48Y4d+6cxlSzZXXt2jWo1Wpphimg4EF8CxcuhBBCI3E5ceIEunbtCisrK6lsz5495RYLUXFK2xvhaqtAs5r2aFrTTmdvhFqtxlV9Bk5ERERUDIMkFjdv3kReXh7y8/MRFBRUYv369euXWCciIgKOjo4wM/v/A7rc3NygUqmQmJgIR0dHqTw4OFgaFL59+3ZUr14dc+bMQe/evXXuOzs7G9nZ2dJyWloagIILO7VaXWJs5aWwLX22yRjKFoMQAuFJWbj6MKXgFZ6Ku3EZWr0RpsYyNHCzQVMPOzT1sEUTj4JnRWjuLB9qdb60aOjjYOj2GQNjqEztMwbGUJnaZwyMoTw9SdwyUdTghgpkZGSElJQUqNVqODk5wdHREUIIJCUlwd7eHjKZDEIIJCcnw8HBAXFxcSXuc8uWLfj888/x4MEDqSwkJATe3t4IDw+Hh4eHVN61a1dcuXIFb7/9NkaPHo0TJ07gk08+wfnz59G8eXOtfc+ZMwdz587VKt+2bRssLCye8ihQVZSdB4RnyBCWAYSlyxCWLkNGrvZtfrZmAl5WArWsC14elgDHVxMREVFlo1QqMWDAAKSmpsLGxqbYupUiscjPz9cot7GxQUpKCqpVqyatK8nOnTsxZswYxMbGSmWBgYGoX78+4uPjNXosevTogXv37uHevXswMiq4mnvttdfg4uKC1atXa+1bV4+Fh4cHEhISSjzA5UmtVuPIkSPo1q2bwe6pZwyaMbTz74x/ozJwNjgR50KScCf2KXsjyhCDoY6DodtnDIyhMrXPGBhDZWqfMTCG8pSWlgZHR8dSJRYGG7xdOObh0bEPj//9SQZ0u7u7IzExEWq1WvrQIiMjoVAo4ODgoFHXxcUFQggpqQAAX19f3Lx5U+e+5XK5zqd/m5qaGuQLYqh2GQOQqlTjbHACTtyJw5lAY0y+eBrqPM1EwsVGgWaedv+Nj6j4mZoM/VkYun3GwBgqU/uMgTFUpvYZA2MoD08Ss8ESi44dO6JFixYaZWXpPGnSpAlMTExw9uxZ+Pv7AwBOnz6NVq1aaSUorVq1wpdffqmRhAQGBqJ27dpP3T5VTdGpWbgYmoRLYUm4HJaMO7Hp+P/XVAZAwN3OHO18HNDOxxGtvKrB1dbcgBETERERGYbBEotPP/0UJ0+eLLf9WVhYYMiQIfjkk0+wadMmxMXFYenSpdKtTQkJCbC2toZcLkf//v0xb948fPTRR5g6dSqOHz+OQ4cO4cqVK+UWDz17hBAITcjEpbAkXAxNxsWwRDxMytKqV9vJEh18HGCUGIpBr/ijdnWbMk+XTERERPSsM1hi8corr6Bbt25Yu3atVFbWi7NFixbh448/RuvWrWFlZYXp06dLT9N2cnLChg0bMHToUNjb2+PYsWMYPXo0GjRoAHd3d2zZsgUNGzYsU/v0bMnJzcetqFT88yAZl8OScflBMhIysjXqGMmAhu62aOFZDa287NHcsxqcrOVQq9U4cCAENatZMKkgIiIigoEfkFc4+9POnTuRn58PIQR27doFCwsLZGZmSuvefPNNjfEQRbGyssLmzZuxefNmrXWP32ZVv359HD9+vNzeCz0bEjKy8cfVSBy5HYt/I1KgUmtODmBmYoQmNezQ0sserbwc0KymHawVz979kERERET6ZpDEovAXXlNTU3Ts2BErVqwAAPj7+2PTpk1SPX9/f6xcuRJ9+vTROXiaqDRSs9T4/Z8IHLoZg8sPkvDopE12FqZo4VnQE9Gilj0a17Ct0IHWRERERFWVQRKLwt4DGxsbnDhxwhAh0HMgNUuNFSfuY/PZB8hS50nlL3jY4c1m7mjr7YDajlYwMuKtTERERERlZZDEYvbs2VAoyj6PP9HjVOo8nL6XgIM3o3HkdizSVbkAgHrO1ujfygNd6zujhj0fakhERERU3gyWWBCVl+zcPJy+m4C916Nw9HYsMnP+3ztRp7oVpvb0RWff6hxkTURERFSBDDp4Gyi4Ler48ePo3LmzoUOhZ0i+AC6EJmHv9VgcvBmNtP96JgDA1VaBlxu4oGdDF7SoVQ3GvNWJiIiIqMIZPLHIyclBt27dkJeXV3Jleu4FRqfh938e4rcrxkg5f1kqr24txyuNXfFqYzc09bDjuAkiIiIiPTN4YgGU7YnbVPVFpmRhz7VI7LkahTux6f+VymCtMEGvhq54rakbWns5sGeCiIiIyID0nlgolUqNZZVKBZlMhqysrCITDAsLDrZ93uTlCxwNjMWms2E4G5wolZsZG8G/riM88qIx6d0usLLgJABERERElYHeEwsrKyutQbRCCFhZWWnVFUJAJpPxNqnnSFyaCnuuRWHz+TA8TMqSylt7VcPrTd3Rs6ErLEyBAweiIDfl8yaIiIiIKgu9JxZqtVpjWaVSwcbGBjk5OfoOhSqR4PgMLDwQhL+DYlHYcWVnYYp3W9bEoBdrakwR+/h3iIiIiIgMT++JhbGx5q/MJiYmOsup6svMzsX+69E4fDsWJ+7EIfe/R2I3q2mHt1t4oG8Td5ib8XtBRERE9CyotIO3Dx48CGdnZzRr1swAEVFFuh2Vhm0XH+CPq1HIyP7/NLGdfatjei8/+FTXvi2OiIiIiCo3gycWZmZmOH78uFa5qakphg8fjmvXruk/KCpX+fkClx8kY8+1SJy8G4+I5P+PnajtaIm+Td3R1c8Z9d1sDBglEREREZWFQRKL06dP48SJE5g5cyYCAwPx4MEDbN68WaOOEAJxcXHYtm0bBgwYYIgwqYzy8wUO347BkqP3EBSTLpWbGsvQvYELBrauiTa1HfhEbCIiIqIqQO+Jxbfffov58+fj888/R35+Pg4ePIilS5eiVatWWnXr1aunNT0tVX7KnFz8fiUSGwJCEZKQCQCwNDNGr0au6NXIFa28qsFSbvDOMiIiIiIqR3q9usvPz8edO3cQEBCAxo0bS+Xt2rXDtm3b9BkKVYCYVBU2nwvD1gvhSM0qmLnJWmGCoW1r4YP2tWFrYWrgCImIiIiooug1sTAyMsK6des0yry9vZGfn6/PMKic5eULrDoZjCVH70KdVzAQv2Y1CwxrVwtvt/CAFXsniIiIiKo8g1/x9e3b19AhUBn88yAJX+wPxNXwFABAy1r2+KBDbXT1c4axEcdOEBERET0vDJ5Y6BITEwMHBwfcv38ftWvXhlwuN3RI9JiEjGwsPHQTf/4bBaBgDMWcPg3wVvMaHIxNRERE9BwyMkSj+fn5Gq9HBQcHo23btli+fDneffddBAQEGCJEKsaNJBl6Lj2LP/+NgpEMeKeFB/7+5CW83cKDSQURERHRc8ogPRampv8fxCuTyaBUKmFmZgalUokePXrA0tISPj4+sLS0RJcuXQwRIumQm5ePb/66i3V3jAGoUd/VBl+92QiNa9gZOjQiIiIiMjCDJBZCCKxatQoymQwjR47EunXr4O/vj/Xr18PU1BRmZmY4dOgQhg8fbojwSIf49GyM3nYFF0OTAABD2tTEjFcawMzEIJ1eRERERFTJGOyq0MTEBGZmZgCAmzdv4r333kOPHj2wYcMGKJVKBAQEoHPnzoYKjx6RlJmDAWvP42JoEizlxhhaNw+f9/JlUkFEREREEoNfGcpkMnz99deQyWQ4c+YMfHx8kJWVhT179qB27dqGDu+5l6ZSY/BPF3AvLgPONnLsGvkimjoIQ4dFRERERJWMwWaFmjlzJoQouECNjo7Gpk2b0K5dO/Tp0wdRUVF4/fXXpbpXrlwxVJjPNWVOLoZvuISbkWmoZmmGrR+0hqe9AkGGDoyIiIiIKh2DJRYffPABjIyMMHfuXPTs2RP//vsvJk+ejM8++wy2trb4/vvvOcOQAUWnZmHstqu4/CAZ1goTbB7eCj7VraFWqw0dGhERERFVQga7FcrT0xO1atUCADRo0ADjx49HZGQkjh07BgDYtGkTnJ2d4e/vb6gQn1uHb8Wg5w+ncflBMizNjLFxWEs0dLc1dFhEREREVIlVijEWy5Ytw88//4yTJ0/i999/R05ODrp06YLZs2cbOrznikqdh9l7bmLEln+QolSjkbst9o3rgOae1QwdGhERERFVcga7FWrNmjUACqae3bVrF2rVqoWTJ0+ievXq6N+/P/r164dJkyYhJydHmj2KKk5wfAbGbLuKwOg0AMAH7b3waQ/O/EREREREpWOQq8a1a9fiww8/xIcffoi1a9fi/fffx19//YXq1asDANRqNczMzNCiRQucP3++1PvNysrC8OHDYW1tDWdnZyxYsKDEbVJSUuDq6oqNGzc+7dt55l0KS8Lry88gMDoNDpZm2DCsJT5/tT6TCiIiIiIqNYP0WLz//vtaZTY2NtLfQ0JCAAB9+vSBp6dnqfc7ZcoUXL9+HRcvXkRkZCTefvtteHl5oX///sVuExMT8wTRVy1/3YrBuO1XkZ2bj2Y17bBqUHNUt1EYOiwiIiIiesYY7Fao4hQmE6NGjSr1NllZWVi/fj0OHjwIPz8/+Pn5YeLEiVi5cmWRicWpU6dw6tQpuLq6lkvcz5qtFx5g5h83kS+Arn7OWNa/KczNjA0dFhERERE9gwySWJw6dUpnuampKezs7ODj4wNTU9Mn2ue1a9egVqvRtm1bqaxdu3ZYuHAhhBBaU9dmZ2dj5MiRWL58OT744IMnfxPPuPUBoZi/7zYAoH8rD8x/rSFMjHnrExERERE9HYMkFi+99FKx6y0tLTF27NhSjZEoFBERAUdHR42B3m5ublCpVEhMTISjo6NG/QULFqBZs2bo2rVrifvOzs5Gdna2tJyWVjDAWa1W6/W5DoVtlaVNIQTWnA7Dd0fuAQBGdvDCJ918IPLzoM7P00sMZcUYKkcMhm6fMTCGytQ+Y2AMlal9xsAYytOTxC0ThY+/rgSEEEhNTcX58+cxZswYfPjhh/jss89Kte2WLVvw+eef48GDB1JZSEgIvL29ER4eDg8PD6k8MDAQL730Eq5fvw5nZ2fUqlULc+bMwdChQ3Xue86cOZg7d65W+bZt22BhYfFkb9KA8vKBX0KMcCG+oGeiu3s+ennkg88hJCIiIiJdlEolBgwYgNTUVI0x0bpUqjEWMpkMdnZ26NGjB5YtW4Zx48aVOrFQKBRQqVQaZYW9DObm5lKZEAIjRozA3Llz4ezsXKp9T5s2DZMmTZKW09LS4OHhge7du5d4gMuTWq3GkSNH0K1btye+VUwIgZl/BuJCfASMZMDnvXzx3os19RpDeWEMlSMGQ7fPGBhDZWqfMTCGytQ+Y2AM5anwTp3SqFSJxaMaN26MiIiIUtd3d3dHYmIi1Gq19KFFRkZCoVDAwcFBqhceHo6AgABcv34dU6dOBVBwwD7++GP89ttv2Ldvn9a+5XI55HK5VrmpqalBviBP0+7GM6H45XIEZDJg5aDmeLmBi95jKG+MoXLEYOj2GQNjqEztMwbGUJnaZwyMoTw8ScyVNrGIiYmBnZ1dqes3adIEJiYmOHv2LPz9/QEAp0+fRqtWrTQGbru7uyM0NFRj2/bt22PChAkYNGhQucRe2ey+GoG5/w3UntbTt8xJBRERERHR4yplYpGamopp06ahR48epd7GwsICQ4YMwSeffIJNmzYhLi4OS5cuxerVqwEACQkJsLa2hlwuR61atTS2NTExgaOjI1xcqt4F99Hbsfjk138hBDDoxZr4sENtQ4dERERERFWQQRKLTp06aU3/ChSMA0hPT0dgYCDq1q2LrVu3PtF+Fy1ahI8//hitW7eGlZUVpk+fjn79+gEAnJycsGHDhiIHaFdF6So1pu2+gXwB9GtRA/P6NNR53ImIiIiIyqpSTTdrYmICGxsb+Pn5oXPnzjAyerLnKlhZWWHz5s3YvHmz1rriJr8KCwt7onaeFT8cvYf49Gx4OVpift+GMDJiUkFEREREFcMgicWMGTNgYlJy05GRkXB3d9dDRFXP/bgMbDwbBgCY3bs+5CZ8ojYRERERVRyDPGq5d+/eGg+c02XdunVo2LChniKqer4/cge5+QJd/arjpXrVDR0OEREREVVxBkksbty4gW7duumcFzciIgI9evTAiBEj8PLLLxsgumffjYhUHLgRA5kMmPKyr6HDISIiIqLngEESi7NnzyI+Ph6dO3dGYmKiVL527Vo0bNgQQUFB2LdvH3bs2GGI8J55i47cAQD0beKOei7WBo6GiIiIiJ4HBkksatasiTNnzsDExATt27fHmTNn0K1bN3z88ccYPnw4bt++jV69ehkitGfe/bh0nLgTDyMZMKFrHUOHQ0RERETPCYM9x6JatWo4duwY3nrrLXTs2BENGjTAhQsX0KxZM0OFVCVsPvcAANDVzxmeDpYGjoaIiIiInhcG6bEoZGFhgb1792LgwIGIjY194ullSVO6So3f/4kAAAxpW8uwwRARERHRc8UgPRbz5s3TWPb29oaRkRE6deqEsWPHakxFO2vWLH2H98zafTUSmTl58HayRFtvB0OHQ0RERETPEYMkFsePH9cq8/UtmL3o9OnTUplMJmNi8QR+vfwQADCwtSefsE1EREREemXQxCIxMRH29vbSLVD//PMPTp48CXt7e7z11luwtuaMRqV1JyYdNyPTYGosQ9+mfKggEREREemXQQY1KJVKvPrqq6hevTqCg4MBAD/99BNatWqFJUuW4Ouvv0ajRo0QGxtriPCeSb9fKRhb0aledVSzNDNwNERERET0vDFIYrFgwQKEhITg8OHD8PLyglKpxKRJk9C8eXPcv38fQUFB6NGjB6ZOnWqI8J45uXn52H01EgDwZvMaBo6GiIiIiJ5HBrkV6tdff8WaNWvw0ksvAQD27NmDtLQ0jBs3DmZmBb+2jx07Fj169DBEeM+ci2FJiE/Phr2FKTrVq27ocIiIiIjoOWSQHovIyEjUrVtXWj569CiMjIw0Eglra2vEx8cbIrxnzvGgOABAJ9/qMDPhlL1EREREpH8GuQqtUaMG7t+/DwDIz8/Hvn370KZNGzg6Okp1zp8/jxo1eFtPaRz7L7Ho7MveCiIiIiIyDIMkFsOHD8f48ePx559/Yvz48YiMjMRHH30krb9x4wamTZuGN954wxDhPVPCE5UIjs+EsZEMHeo4GTocIiIiInpOGWSMxeTJk5GamopRo0YhPz8fs2bNwoABAwAAU6ZMwaJFi9CpUyfMmTPHEOE9U44FFcyc1cLTHrbmpgaOhoiIiIieVwZJLIyNjbFgwQIsWLBAa92gQYPw5ptv4sUXXzRAZM+e43cKxqHwNigiIiIiMiSDJBbFeeGFFwwdwjNDCIEr4ckAgHY+jiXUJiIiIiKqOJxC6Bn2MCkL6apcmBkboZ4Ln1JORERERIbDxOIZdjMqFQDg62oNU2N+lERERERkOLwafYbdjCxILBq42Ro4EiIiIiJ63jGxeIbdjEoDADR0tzFwJERERET0vGNi8YwSQuAWeyyIiIiIqJJgYvGMiklTITEzB8ZGMvhy4DYRERERGRgTi2fUzciC26DqVLeCwtTYwNEQERER0fOOicUzKii6ILGo78rxFURERERkeEwsnlHhSUoAgJejpYEjISIiIiKqYolFVlYWhg8fDmtrazg7O2PBggVF1t25cycaNmwIKysrtG7dGufOndNjpGVXmFh4VLMwcCRERERERICJoQMoT1OmTMH169dx8eJFREZG4u2334aXlxf69++vUe/06dMYPHgw1q1bh3bt2uGnn35Cjx49cOfOHbi4uBgo+ifzkIkFEREREVUiVabHIisrC+vXr8d3330HPz8/dO3aFRMnTsTKlSu16m7cuBH9+vXDwIEDUatWLcybNw8uLi7Yt2+fASJ/cjm5+YhOUwEAajKxICIiIqJKoMr0WFy7dg1qtRpt27aVytq1a4eFCxdCCAGZTCaVjx07Fqamplr7yMzM1EusZRWVmgUhAHNTYzhamRk6HCIiIiKiqpNYREREwNHREWZm/7/QdnNzg0qlQmJiIhwdHaXyJk2aaGz7999/4+7du+jUqZO+wi2Th0lZAACPauYaCRMRERERkaFUmcRCpVJBLpdrlBUuZ2VlFbndnTt3MGDAALzzzjto3LixzjrZ2dnIzs6WltPSCqZ6VavVUKvVZQ291ArbCkvIAADUsDPXa/uPxqDvdhlD5YvB0O0zBsZQmdpnDIyhMrXPGBhDeXqSuGVCCFGBsejNzp07MWbMGMTGxkplgYGBqF+/PuLj4zV6LAqFhoaiY8eOcHR0xKlTp2BtrfsJ1nPmzMHcuXO1yrdt2wYLC/2PcdjzwAjHoozg75KPN7zy9d4+ERERET0flEolBgwYgNTUVNjYFP/8tCrTY+Hu7o7ExESo1Wpp/ERkZCQUCgUcHBy06t+7dw+dOnWCk5MTjhw5UmRSAQDTpk3DpEmTpOW0tDR4eHige/fuJR7g8qRWq3HkyBGY2DoDUfFo38wPvdp46q39R2Po1q2bznEqjOH5icHQ7TMGxlCZ2mcMjKEytc8YGEN5KrxTpzSqTGLRpEkTmJiY4OzZs/D39wdQMK1sq1attMYhJCQkoFu3bnBzc8ORI0dga2tb7L7lcrnWbVYAYGpqapAvSERKwYxQXo7WBvuCGuq9M4bKF4Oh22cMjKEytc8YGENlap8xMIby8CQxV5nEwsLCAkOGDMEnn3yCTZs2IS4uDkuXLsXq1asBFCQT1tbWkMvlmDlzJjIyMnD48GGo1WokJCRI+zDErU1PKiK5YMxITYfKHysRERERPR+qzHMsAGDRokWoX78+Wrdujf79+2P69Ono168fAMDJyQnbt28HAPz2229ITExEvXr14OTkJL2++eYbQ4ZfKspcIE2VCwCoYW9u4GiIiIiIiApUmR4LALCyssLmzZuxefNmrXWPjlGPj4/XZ1jlKl0NONvIkS8AC7Mq9fERERER0TOMV6bPGGdzIGCKP2BkbOhQiIiIiIgkVepWqOeJqTE/OiIiIiKqPHh1SkREREREZcbEgoiIiIiIyoyJBRERERERlRkTCyIiIiIiKjMmFkREREREVGZMLIiIiIiIqMyYWBARERERUZkxsSAiIiIiojJjYkFERERERGXGxIKIiIiIiMqMiQUREREREZUZEwsiIiIiIiozJhZERERERFRmTCyIiIiIiKjMmFgQEREREVGZMbEgIiIiIqIyY2JBRERERERlxsSCiIiIiIjKjIkFERERERGVGRMLIiIiIiIqMyYWRERERERUZkwsiIiIiIiozJhYEBERERFRmTGxICIiIiKiMmNiQUREREREZcbEgoiIiIiIyoyJBRERERERlVmVSiyysrIwfPhwWFtbw9nZGQsWLCiy7pUrV9CiRQsoFAo0bdoUFy5c0GOkRERERERVS5VKLKZMmYLr16/j4sWL2Lp1K7799lts375dq15GRgZ69eqFHj164M6dO+jRowdeffVVpKenGyBqIiIiIqJnX5VJLLKysrB+/Xp899138PPzQ9euXTFx4kSsXLlSq+7OnTthbm6O+fPnw9PTEwsWLICNjQ127txpgMiJiIiIiJ59VSaxuHbtGtRqNdq2bSuVtWvXDpcuXYIQQqPu+fPn0b59e8hkMgCATCZD27ZteTsUEREREdFTMjF0AOUlIiICjo6OMDMzk8rc3NygUqmQmJgIR0dHjbqNGjXS2N7NzQ23bt3Sue/s7GxkZ2dLy6mpqQCApKQkqNXq8nwbxVKr1VAqlUhMTISpqane2mUMjKEyts8YGENlap8xMIbK1D5jYAzlqXCowOM/1OtSZRILlUoFuVyuUVa4nJWVVaq6j9crtHDhQsydO1er3MvLqywhExERERE9E9LT02Fra1tsnSqTWCgUCqhUKo2ywl4Gc3PzUtV9vF6hadOmYdKkSdJyfn4+kpKS4ODgIN1OpQ9paWnw8PDAw4cPYWNjo7d2GQNjqIztMwbGUJnaZwyMoTK1zxgYQ3kSQiA9PR1ubm4l1q0yiYW7uzsSExOhVqulbqbIyEgoFAo4ODho1Y2OjtYoi4yMhLu7u859y+VyrR4OOzu78gv+CdnY2Bj8i8kYGENlaZ8xMIbK1D5jYAyVqX3GwBjKS0k9FYWqzODtJk2awMTEBGfPnpXKTp8+jVatWmn1Krz44osICAiQ7hUTQuDMmTN48cUX9RozEREREVFVUWUSCwsLCwwZMgSffPIJbt26hePHj2Pp0qUYPXo0ACAhIUG6Neqtt95Ceno6Zs6cifDwcMycORNKpRJvv/22Id8CEREREdEzq8okFgCwaNEi1K9fH61bt0b//v0xffp09OvXDwDg5OQkPSzPxsYG+/btw/79+1GnTh0cPHgQ+/fvh5WVlSHDL5FcLsfs2bO1bstiDIzheWyfMTCGytQ+Y2AMlal9xsAYDEUmSjN3FBERERERUTGqVI8FEREREREZBhMLIiIiIiIqMyYWRERERERUZkwsnhFZWVkYPnw4rK2t4ezsjAULFlR4m6GhoejduzdsbW3h5eWFhQsXIj8/HwBw5coVtGjRAgqFAk2bNsWFCxcqNJaRI0fipZdekpZDQkLQqVMnKBQK1KtXD/v27auwtoUQmD17NlxcXGBvb48RI0ZID1jURxwxMTF48803YWdnh5o1a2LhwoXSuopuPycnB/Xr18eJEydK3ebhw4fh5+cHc3NzdOzYEXfv3i33GI4fP46WLVvCysoKjRs31orh559/Rq1atWBhYYHevXsjNja23GMopFar0ahRI8yZM0ejXB/HITk5GQMGDIC1tTVq1KiBZcuW6T2GCxcuoE2bNrCyskLDhg2xf//+co+hLOei8vouFBfDzp070bBhQ1hZWaF169Y4d+6cxrbfffcdXF1dYWVlhcGDByMjI6PcYyiUkpICV1dXbNy4UaNcH8chPDwcvXr1gqWlJXx8fPDrr7+WewzFtX/w4EE0btwYVlZWaNWqFc6fP18hx+DevXt4+eWXYW1tjbp162Lr1q3SOn2dH4uLQV/nx+JiKFTR58fiYtDX+bG4GPRxfqx0BD0TRo8eLZo3by5u374tjhw5Iuzs7MS2bdsqrL3s7GzRoEEDMWjQIHH//n1x6NAh4eTkJFasWCHS09OFs7OzmDFjhggLCxNTp04Vjo6OIi0trUJiOX36tJDJZMLf318IIUReXp5o1KiRGDZsmAgLCxPLli0TCoVChISEVEj7X3/9tahevbo4fvy4uHbtmqhbt6747LPP9BZHjx49RKdOnURgYKA4deqUcHV1FRs2bKjw9lUqlXjrrbcEAHH8+HEhRMnH/sGDB8LCwkKsXLlShIaGisGDBws/Pz+Rl5dXbjHcu3dPWFhYiMWLF4vQ0FCxYsUKYWpqKq5fvy6EEOL8+fNCoVCI33//Xdy7d090795ddO/evVyPw6O++OILAUDMnj1bKtPHcRBCiJ49e4q2bduK27dvi/379wuFQiEOHDigtxgyMjKEo6OjmDhxoggLCxMbN24U5ubmIigoqNxiKMu5qLy+C8XFcOrUKaFQKMTPP/8sQkNDxcyZM4WNjY2Ijo4WQgixc+dOYW9vL44fPy5u3LghmjZtKkaMGFGuMTzqgw8+EADEhg0bpDJ9HIfc3FzRqFEj0bdvX3H//n2xcePGcv93WVz7YWFhQqFQiG+//VaEhYWJr776SlSrVk0kJCSU6zHIy8sT9erVE4MHDxYhISHiwIEDws7OTpw4cUJv58fiYtDX+bG4GB5VkefHkmLQx/mxuBj0cX6sjJhYPAOUSqVQKBQaFxRz584VHTp0qLA2T506JeRyuVAqlVLZl19+Kdq1ayd++uknUatWLZGfny+EECI/P1/Url1brF+/vtzjyM7OFvXr1xcdOnSQEotjx44JCwsLkZGRIdXr1KmTmDlzZrm3n5eXJ5ycnMTPP/8slf3888+iS5cueovDwsJC7N27V1qeNGmS6Nu3b4W2f+vWLdGkSRPRpEkTjQvJktqcNWuW9DkJIURmZqawtLQUf//9d7nFMG/ePNGxY0eNut26dRMzZswQQggxePBgMWTIEGldWFiYkMlkIjg4uNxiKHT37l3h5uYmGjRooPEfpz6Ow/Xr14WxsbEICwuT6o4ePVqvn8XFixcFAJGeni7VbdasmViyZEm5xVCWc1F5fReKi2H48OFi8ODBGvXr1q0r1q5dK4QQomPHjhrfjZMnTwq5XC4yMzPLLYZH9123bl3px4dC+jgOf/75p7Czs9P4LvTp00esXr263GIorv1ff/1VODg4aNSvVq2a+OOPP8qtfSGEiIqKEm+++abGD2l9+/YVEyZM0Nv5sbgY9HV+LC6GQhV9fiwuBn2dH4uLQR/nx8qIt0I9A65duwa1Wo22bdtKZe3atcOlS5ekp4eXN19fX+zduxfm5uZSmUwmQ2ZmJs6fP4/27dtLTzSXyWRo27ZthdwOtXDhQjRt2hSdO3eWys6fP49mzZrB0tJSKmvXrl2FtH/r1i0kJCSgT58+UtnAgQNx9OhRvcXRtGlTbN++HdnZ2YiNjcVff/0FW1vbCm3/1KlT6NSpEwICAjTKS2rz/Pnz6NChg7TOwsLiqW+VKyqGd955B4sWLdIoK/xu6orB09MT7u7u5RpDoZEjR2Lu3LlwdHTUKNfHcThx4gSaNm0KT09PqezHH3/EvHnz9BaDt7c3rK2tsXHjRgghcOnSJQQFBcHW1rbcYijLuai8vgvFxTB27Fh8+umnWttkZmZCCIGLFy9qxPDiiy8iNzcX//77b7nFAADZ2dkYOXIkli9fDjMzM41t9XEcTpw4ga5du2o8D2rPnj0YMWJEucVQXPuNGjVCWloa9u7dCwDYt28fkpOTi/wuPu0xcHV1xW+//QZra2sAwMWLF3Hy5MlSnZPL699kcTHo6/xYXAyFKvr8WFwM+jo/FheDPs6PlRETi2dAREQEHB0dNf6zcHNzg0qlQmJiYoW06eTkhG7duknL2dnZ+Omnn9C5c2dERETA3d1do76bmxsiIyPLNYagoCCsWrUK33//vUa5vtoHgODgYDg5OeH06dNo2rQpvLy88MknnyAnJ0dvcfz8888ICAiAlZUVXF1dkZeXh88//7xC2x81ahS+//57jf8ggZKPfXnGVFQMdevWRYsWLaTlwMBAHD9+XEo+9REDAGzYsAE5OTl4//33tdbpI4bg4GB4eXnhm2++Qe3atdGwYUOsW7dOrzFUq1YNO3bswJQpU6BQKNCqVSt06dIF7777brnFUJZzUXkdg+JiaNKkCRo0aCCt+/vvv3H37l106tQJCQkJUKlUGjGYmZnBwcGhXGMAgAULFqBZs2bo2rWr1rb6OA7BwcHw8PDAp59+Cg8PDzRv3ly6yC+vGIpr39fXFytWrEDfvn0hl8vRp08fvP/++/D39y/XY/CoOnXqoHXr1qhduzbGjh2r1/NjUTHo8/xYVAyA/s6PRcWgz/NjUTHo4/xYGTGxeAaoVCqtpzUWLmdlZVV4+3l5eRg4cCASExMxefLkIuMpz1iEEBg5ciTmzJmD6tWra6zTR/uFMjIykJGRgenTp2Px4sXYsGED9uzZg08//VRvcQwePBheXl44e/Ys/vjjD9jb2yM+Pl6vx6FQSW3qO6a4uDj07dsXzZs3x6uvvqq3GOLj4zFt2jSsWrVK+rX8UfqIISMjA0ePHsXZs2fx+++/Y+rUqRg3bhz++OMPvcUQHR2NYcOG4YMPPsDFixfxww8/IDMzE2lpaRUSw5OeiyriGDwew6Pu3LmDAQMG4J133kHjxo2lSR4qOobAwECdP8IU0sdxyMjIwMaNG5Gamor9+/djyJAheOONN/DPP/9USAyPt3/jxg2MHz8e8+bNw6VLl/D5558jKSmpQr8LO3fuxF9//QULCwvcuHHDIN/Hx2N4lL7Oj4/HYIjz4+MxGOL8+HgM+j4/VhYmhg6ASqZQKKT/oAplZ2cDgEaXcEXIz8/H8OHDsXfvXhw4cACurq5FxlOesaxbtw5qtVrqRn+UQqFAQkJChbZfyMTEBEqlEkuWLJFmpfr2228xYMAADB06VGt2l/KO49y5czhz5gwiIiLg6uoKAEhPT8fAgQPRs2dPvR2HQiUd+6K+GzY2NuUeS1JSErp16waVSoXffvtN+g9MH9/PCRMmYOjQoWjYsKHO9fo4DiYmJsjJycHPP/8MKysrNG3aFJcuXcLKlSvRt29fvcSwceNGODg4YOnSpZDJZHjhhRdw48YNfPbZZ9iwYUO5xvA056Ly/i7oiqFQaGgounbtCjc3N6xdu1ZqH0CFxuDi4oJ+/fph7ty5cHZ21rmNPo6DiYkJ7O3tsXLlShgZGaFx48b4+++/sWbNGqxevbpcY9DV/pw5c9CxY0fMmDEDANC4cWN069YN3377LWbPnl0h54UmTZoAKEjyhw0bhl69eun9/Ph4DCEhIZDJZHo9Pz4eQ9u2bfV+fnw8hu7du+v9/Ph4DCNGjNDb+bEyYY/FM8Dd3R2JiYlQq9VSWWRkJBQKBRwcHCqs3by8PAwaNAi//vorfv/9d3Tp0kWKJzo6WqNuZGSkVpdeWWzbtg1Xr16Fvb097Ozs8NVXXyEgIAB2dnZwdnau8PYLubi4AADq1asnlfn6+kKlUsHFxaXC43j48CEcHR01LmCaNm2K0NBQVK9eXW/HoVBJn70+vhtAwS9x/v7+SE1NxbFjxzT2r6/v5/Lly2FnZwc7OzsEBATgq6++QuPGjfUWg4uLC9zd3TXuaff19UVERITeYnj48CEaNWqk8atk06ZNcfHixXKN4WnPReV5DIqKASiYbrJDhw5wdHTEkSNHpPutHRwcIJfLNWLIyclBQkJCucUQHh6OgIAAfPbZZ9L3MTw8HB9//LH0K7U+joOLiwt8fHxgZPT/y4qK+D4W1f7Dhw+lf3+FKuK7GBsbK/3qXcjX1xdhYWElnpP1EUNCQoJezo/FxaCv82NxMZiYmOjl/FhcDOHh4Xo5P1Y2TCyeAU2aNIGJiQnOnj0rlZ0+fRqtWrXS2c1YXsaPH48//vgD+/btk/6DAgoGHwYEBEgDx4UQOHPmDF588cVya3v79u0IDAzEtWvXcO3aNYwaNQotWrTAtWvX8OKLL+LKlSvSYDSg4HiUZ/uFmjRpAlNTU1y5ckUqCwwMhLW1Ndq3b1/hcXh7eyMhIUFjnvGgoCCYmZmhTZs2ejsOhUo69i+++CJOnz4trcvMzMTVq1fLNSaVSoVevXohKysLAQEB8Pb21orx0RhCQ0MRGRlZrjGEhobixo0b0vezRYsWGDVqFA4cOKAzhoo4Dq1atcKDBw+QlJQklQUGBqJ27dp6i8Hb2xu3b9/WKAsKCpIS8vKK4WnPReX5XSgqhoSEBHTr1g1ubm44ceKExkBVIyMjtGrVSiOGM2fOwMzMTPp1s6wxuLu7IzQ0FP/++6/0fXRzc8O8efOke8r1cRxatWqFmzdvavwAVtz38WljKKp9b29v3Lp1S6Nucd/Fp20/LCwMb775JmJiYqSyy5cvw8LCAh06dNDL+bG4GCwtLfVyfiwuhuDgYL2cH4uL4ZVXXtHL+bG4GHx8fPRyfqx0DDYfFT2RESNGiObNm4ubN2+KY8eOCTs7O/HLL79UWHvnz58XAMSPP/4o4uPjpVdSUpJITU0Vjo6OYsaMGeLBgwdixowZonr16hpTqpW32bNnazzHws/PT5orfMWKFcLc3FxjWrnyNGLECOHj4yPOnj0rLl26JOrWrSs+/fRTvcXRvn170a1bN3Hr1i1x6tQp4eXlJUaNGqW39vHYcyyKazMkJEQoFAqxfPlyERYWJoYNGyYaNmxY5nm5H43hq6++EnK5XJw7d07ju1n4/QsICBByuVz89ttvIjg4WPTs2VP07NmzTO0/HsPj/P39NaZT1MdxyMnJEQ0bNhSvvvqqCAoKEr///ruwsLCQ5mnXRwxxcXHCzs5OTJkyRQQHB4uff/5ZKBQKsWPHjnKLoSznovL6LhQXw6hRo4SDg4O4c+eOxrrC6WS3bdsm7OzsxN9//y1u374tmjdvLj766KNyjeFxnp6eGtPN6uM4JCUlCWdnZ/H++++Le/fuiTVr1ghTU1Nx48aNcouhuPZv374tzMzMxPfffy9CQkLEkiVLhLGxsTh79my5HoO8vDzRunVr8eqrr4o7d+6IQ4cOCRcXFzFt2jS9nR+Li0Ff58fiYnhcRZ0fi4tBX+fH4mLQx/mxMmJi8YxIT08X7733nrC0tBTOzs7im2++qdD2Jk+eLABovTw9PYUQBSf4Jk2aCDMzM9GsWTNx6dKlCo3n0cRCCCHu3Lkj2rdvL+RyuahXr544ePBghbWdnZ0tJkyYIKpVqyasra3Fhx9+KFQqld7iiI+PF/379xfVqlUT7u7u4pNPPpHmcddH+49fUJfU5t69e0XdunWFXC4X/v7+4v79++UaQ4sWLXR+Nx+dm339+vXCw8NDKBQK0adPHxEXF1euMTzu8f84haj44yCEEBEREeK1114T5ubmwtXVVfzwww96j+HatWuiQ4cOwsrKStSrV096fkN5xVDWc1F5fBeKi8HR0VHnuke/D19++aWoXr26sLS0FEOHDn3iZ1iU5jg86vHEQoiKPw5CFDzv5KWXXhJmZmbCy8tLuoAqrxhKav/YsWOiefPmwtLSUrzwwgtiz5495X4MhCh4dsEbb7whbG1thZeXl/jyyy+FWq0WQujv/FhUDPo8PxZ3HB5VkefH4mLQ1/mxuBgq+vxYGcmEqKAHIRARERER0XODYyyIiIiIiKjMmFgQEREREVGZMbEgIiIiIqIyY2JBRERERERlxsSCiIiIiIjKjIkFERERERGVGRMLIiIiIiIqMyYWRETPsUuXLmHatGnIy8t7ou1WrVqFd999t8R6d+7c0Spr2rQpwsLCit1u165dWLx48RPFVEitVmPr1q0aZVlZWTh8+HCx24WEhODGjRsaZcuWLcODBw+eKo6iHD9+HOfOncPly5fRokULAMAvv/yC1NRULF68GN9++225tkdEpC9MLIiInlGrVq2CTCaTXm+99RYWLFgAKysrrVfHjh117qNu3bo4ffo05syZU+7xBQUFwd/fX2Pf4eHhuH//Pjw8PIrdtmXLlli+fDnWrFmDmJgYjff5+Gvjxo0a26rVavz444+YNm2aVLZhwwZ88cUXxbb5888/Y9WqVdJyTk4OJk+eXGTStXHjxmLjevyzKWRsbIzXXnsN586dAwDMnj0b06dPhxAC4eHh4HNriehZZWLoAIiI6OkNGDAAmzZtwk8//YTDhw8jJycHb731Fr777juNeqamprh8+TJatmypcz9nzpzRuvD29PQssWehOL6+vrhw4QLatGkDFxcXjBo1Crt370ZGRgZMTLT/+0lOToadnR0AwMPDA9u3b8eePXvg4uICtVot1Rs9ejScnZ0xa9YsAAUX6o8yNjbGli1b0Lx5cwwfPhwymQxz5szB7t27oVKpIJfLIZPJpPpCCOTm5mLLli1Yt24dcnNzYWJigoCAADg6OiIuLg5xcXEAAHd3dykpGjBgAPr27avR9oYNG7Bnzx788ccfGuVmZmYACnpOlEolhg0bhujoaKSlpSE1NRXTp09HcnIy7ty5g9atWz/5wSYiqgSYWBARPcNkMhlMTExgZFR8B7StrS1atGgBtVqNK1eu4Nq1azhz5gy2bNmCkSNH4pVXXsEHH3yAvn37okaNGpgyZYrOi/8n5enpiS1btiA4OBj5+flYvXo1fv31V/Tu3Vuqc+jQIUycOFFKKgq1bNlSSoQejUUmk8HIyKjI+BwcHJCZmQmgoEemUPv27QEAV69eRZMmTaTyX375Bf379wcAvPTSSwCA0NBQ7Ny5E1ZWVvj8888BAMHBwRg0aBDmz58PoCBZKEwYHmVpaan1XgqlpKRgzpw5iIuLQ3R0NIyNjXH+/HmcP38exsbGOHv2LA4ePCjFA4A9GET0zOCtUEREz7CtW7dCJpPhww8/lMq2b98OHx8f+Pj4wNvbG05OTtI6ExMT3L17F3/++Sc2btyIWbNmYdWqVejduzdatmyJ77//HjNnzoQQQqsn4HGFv/Q/+tJ1EdylSxeMGDEC69evR25uLt58800cOHAA9+/fh0KhwIEDBzQSjTlz5ki3EI0aNeqpjktYWBjUajWWL1+OAQMGQK1WQ61Ww9PTU2d9f39/CCEghIBcLkdycjJ+/vln7N69G0ePHsXRo0fRr18/mJqaFttuQkICrKysilzv6uqKP//8EwqFAl9++SV8fX3h4eGB4cOHo0aNGkhNTcXNmzehVqtRu3ZtnWNUiIgqKyYWRETPqA8//BBZWVno0KEDfv31V2zfvh0AMGzYMKSkpCAlJaXIC9O8vDx07doVq1atwuzZs7F582ZcuHABP/74Y6nb//XXX2FqaqrxWr16tVa9rKwsZGZm4uuvv8aqVatgZGSEX3/9FRcvXgQAtGnTBmPGjJHqz5o1C2q1GjNnznySw6GhVq1aMDU1xejRo7Ft2zYpvtIOxJ49ezY6duyIevXqSWU5OTmQy+Ua9aysrDTGUixYsAC//vqr1hiLQ4cOSft49dVXMXv2bLRr1w4AsH79ely4cAHffvst7O3tcefOHeTm5iIuLg4+Pj5PfQyIiPSNt0IRET2jlixZgrp168LIyEi6cC4NIyMjHDlyRFqeO3eu9PfPPvus1O2/88472LFjR4n1/vzzT+zfvx+3bt2SLswjIiKkX/aHDRumFV/h62k9fPgQNWrUwKpVqxAQEICff/4ZQEHCoUt+fj5UKpW0nJmZiS+++AK+vr44cOAA6tSpg4yMDFhbW2tsl5qaKvXSqNVqeHh4wNnZGZ07d9aY1aqw98fMzAznz5+HkZERnJycsGrVKtjY2KBXr14YPHgw5s2bh3///Rc1a9ZE/fr1y3QMiIj0jWcsIqJn1I0bN6Rf4IcNG4auXbsCKOiNUKlUUKlUyM7O1tgmNzcXCxYswD///CPVMTY2RlRUlHS7kFqt1jl24GmdOHECjRs3lpKK7Oxs/PvvvyVO//q0FAoFfHx8oFAoMHbsWGzfvh0KhQIKhQIxMTE6L9ZPnz4Nc3NzmJubIzs7G+vXr0ebNm0waNAg9O3bF7m5uUhJSdFKLIyNjWFiYgITExOpV2LRokXYtWsXAEjrHh0sbmRkhPv378PY2BhvvvkmAMDPzw+TJ09Gjx49sHv3bvz1119SjwYR0bOCiQURURUwatQozJgxAwCwbt066SL50V/oMzIyYGpqilu3bqFJkyZSL0deXh7c3Nw0bml64403yiWunJwc7Ny5U2MMxapVq+Dj44ODBw/i/v375dLOoxISEqSkadmyZejfv7+0rFKp0LhxY61tHh9jUejzzz+HkZERNm3ahJiYGI3xKo9KSUnBxIkTMW3aNHTr1g3VqlXDggULtOqFhYVBJpOhTp06iI2NlW6VatCgAebOnYsGDRpApVJh9erVGDBgQPkdFCIiPWBiQURUBbRu3RqdOnUCAIwcOVK6SC58AQXjAYQQcHd3x9WrV6V1hbMRvfzyy0hOTtY5TuJprV27FnXq1JHGKly4cAGff/45vv76a0yePBlDhgzR6lXRJT8/v8Q6DRo00Hp+x4QJE/DLL7/ofLbHpUuXStynsbEx9u3bhwEDBiAkJATu7u5adZKTk/Hmm2+iXr16mDBhAoyNjbF69WosWLBAa9rZWrVqITY2FjY2Njhz5gwSEhJga2urMa3va6+9BqVSiebNm5cYHxFRZcLEgojoGRYYGIjY2FjMmzcPbdq0eaJtY2Njcf78eQghsHbtWgQEBGDMmDE6bxXKy8vTmP0pPz9f56xQhesAIDo6GnPnzsXs2bMBFDxN++WXX8bChQvRvXt3TJgwAS4uLujSpUuRPRdqtRpff/219MyK4ty6dQsZGRkaryVLluCdd97RKs/IyNB4psfJkyel3oPCREetViMnJweenp5IS0tDbGws6tSpo9HmsWPH0KpVK1haWuKPP/6QxlK0bdsW69evx7vvvovvvvsOubm50jbp6en44IMP0K9fPzRr1gyvvvqqNFtVcHAwNm/eDBMTkxIf6EdEVNkwsSAiegZt2bIFO3bswN69e9G1a1d89913OH36NABg9erVOp/+HBERobGP0aNHo1+/fnjllVdQo0YNrFmzBrdu3UJSUpJWe7a2thq3So0ePVrnrFCmpqYYPnw4AODo0aPo2bMnOnfujLfffhsTJkzAjh07pBmgZDIZfv31V3To0AFNmjSRnhdRKCkpCRs2bMCuXbvwxhtvICkpCUlJScjMzNQYs1AeOnbsKI0vKbwVSgiBF198EcePH8e6devQrl07KBQKaZt+/frh1VdfxYABA7B7926Ym5tr7HPgwIHYu3cvVq1ahebNmyMjIwMAYG9vjzp16kAmk6FJkybYv38/tm3bhsOHD6N9+/YYM2YMTpw4gcWLF2PWrFl8jgURPTsEERE9cxITE8Xly5e1ymfPni1GjBgh1Gq11quQu7u7uHr1qta2qampQi6XC5lMJhYuXFgucebn5wshhDhy5IjIzMwssl54eLgIDAzUKLt27ZrYvHmztI8vvvhCABCWlpbi8OHDpWp/5cqVYuDAgcXW2b59u/D395eWv/32W5GSkiKEEGL+/PmiXr16wtHRUQQEBGhsFxQUJB4+fFhiDCqVSpw6dUoIIURgYKCwtLQUr7/+uvT5XbhwQaxbt044ODiI7du3S9vdunVLtGjRQkRGRpbqvRIRGZpMCP4UQkREpC9KpRIWFhZa5ZmZmbC0tDRARERE5YOJBRERERERlRnHWBARERERUZkxsSAiIiIiojJjYkFERERERGXGxIKIiIiIiMqMiQUREREREZUZEwsiIiIiIiozJhZERERERFRmTCyIiIiIiKjMmFgQEREREVGZMbEgIiIiIqIyY2JBRERERERlxsSCiIiIiIjKjIkFERERERGVGRMLIiIiIiIqMyYWREQVQKVSVej+k5KScO3aNa3y4OBgZGVlVWjbAPD333/jzJkz0vKdO3ewYsWKCm+3OCdOnEB4eLhBY3gat27dQn5+fpHrjx07hsTERD1GVDkJIQwdAhGVgIkFET03VCoVgoKCSnw97YX5/fv3sWLFCnTt2hXVqlXDiRMnNNanp6ejXbt2OHLkSJnfy6ZNm+Dv749//vlHo3zw4MH45JNPyrz/kqxcuRJbtmyRlq9evYpZs2YBAFJSUrSOaUJCQpH7SkxMhFKp1Ci7fPkyAgMDnyimESNGYNeuXU+0TVlt3LgRMpmsVK8aNWpoba9SqdCjRw/069cPeXl5Otvo3r07Ll269MSx+fj4lOl4zJgxA6NGjdL5Km2i88svv+Dy5csl1svOzsaqVauQlZWFHTt2wMfHB0DBv6ljx44BAIYMGYIffvjhqd8PEVU8JhZE9Ny4du0a/Pz8Snw9frH+qBs3buDcuXM4fPgw1q5di6lTp+K1116Ds7Mz6tSpg08//RQ2NjbYsWMH2rdvr7GttbU1OnfujFdeeUVqw8rKqsQLUl0mTpyIXr16YeDAgVLvyP79+3Hp0iWMHTu2VMeiNBfDj8vLy8PNmzeRlpaG5ORk3Lx5Ezdv3kRERIS0bvXq1VrH9McffywyllmzZuH111/XKBs2bBjWrVtX4vsoFBgYiHv37qFnz56l3qa8ODs7IzAwUHotWbIEdnZ2GmULFizQua1CocC+fftw5MiRIuuUxsyZM3H9+nWNsuDgYKSlpWmUzZ07V+P7HRERofNznzNnDrZu3Ypbt24hNzdXeiUlJWH16tVIT0+X9pGYmIhRo0bp7EGbPHkyzp49W2L8qamp2LZtG9q1a4fMzEwABT01/v7+OHz4MAAgJCSkyOSLiCoHJhZEVOHu3LmDgQMHolatWjA3N0ft2rXx3nvv4fbt21KdoUOHFnkRPW7cOMhkMkydOhXA/38lfrxHoDT7AgpuPQkNDdV63b9/v9j3kZubi8aNG6Nt27bo06cPZs6ciaNHj8La2hrjx4/HyZMnkZycjF27dqFPnz4wMTHR2sf8+fPx6quv4uuvvwYAXLlyReMCtE2bNhg8eLBG2eMuXryIjRs34oUXXsD9+/fxzTff4KeffsLQoUPRpk0bXLhwARs3bkRSUlKx7wcAjh49qtFW4auoRCA1NRXt27fHiRMn8Mcff6B9+/Zo3749Zs+eLa27evUq6tWrByEEhBBo165dke3fvn0bP/30E9577z2N8o8++ghbt26VLjIfN2fOHI0L4fr16wMAfH19i02Uius5eVTv3r2xatWqUtV9XHG3NenywgsvYOHChYiOjn6q9gDg22+/1fj3VJTFixfjxo0b0rKLi4v0mVtbW+OHH35AYGAgxowZA6Cgl2Dt2rXIy8vDtGnTdCY/xsbGiIqKQps2bbBnzx6p/OHDh4iIiED16tV19gw++v00NzfHL7/8grp16yIxMRH5+fkIDw/HgAED8NlnnwEAwsPDUbt27Sc6Lu+++y4WLVr0RNsQURkIIqIKdO3aNWFpaSm6du0qduzYIU6cOCE2bdokmjZtKuRyuQgICBBCCDFkyBCh65Q0btw4AUDMnDlTKtuwYYMAII4fP66zzaL2de7cOQGgxNfp06d17letVgsA4siRI09xJP4vPj5eKJVKneu6dOkixo8fX+z248ePF+bm5sLT01N4enoKJycnIZPJpGUPDw8BQFy9erXIfVy9elUAEKGhoTrX7969W+cxLPTmm2+KkSNHSsvbt28XDg4OQgghli1bJurVqyeta9eunZg9e7bWPtLS0kSjRo1Ely5dRH5+vsa67Oxs4efnJ4YMGaK1ToiCYxgYGCgCAwPFjRs3hJ2dnZg/f75UVtQrNze3yPf06Hvx8fERarW6xLqF38XSvNzd3Uvcny7Gxsbi4MGDxdaRy+Vi+/btGmUAxIYNGzTKbG1ttcoeXbdz505p2dPTU6xdu1ZkZWUJAOLUqVPi3r17Or83+fn54v333xfm5uYiMDBQCCHEokWLij0ej8bboEGDYusGBQUJAKJp06bC399fes2aNavY43Lv3j1hY2MjgoODi61HROVD++c0IqJy9P3338PR0RGHDh2CsbGxVN6nTx/UqVMHCxcuxL59+3RuO2HCBCxduhRffPEFZsyYUW4x/fjjj5DL5Vrl+fn5GDlyZInbR0VFISgoqFRtubi4wM7ODiqVCmFhYRrrzM3NS7UPXTp37iwdtz/++ANDhw6V9p+QkAAnJ6en3ndprV69GqtXr5aWHRwcpL/fuXNHo9eoa9euGtumpaWhR48eSE9Px6FDh7R6mMzMzLB582b4+/sjNzcX69ev1/jMHB0d4ejoCADYuXMn0tPT8cEHH8DFxQUffvihdFvak8rNzcWMGTMwffp0nT1Oujg7O2v0nh0+fBizZ8/GuXPnpLLdu3dj+fLlGtvFx8drHL8OHTrA398fSqVSq9dDpVIhIyNDWra0tCy2V648Fd5qZ2ZmVmQdmUyGZcuWITo6GkqlEnl5eVi9ejWmTp2KhQsXatQ9f/482rRpgxdeeEEq+/3335GYmIi+ffvC29sbkZGRmDBhAjw8PFCnTh1cvnwZMpkMr7/+OmQyGY4ePYq4uDg0adKk2Nh9fHzQvXt3zJ8/Hxs2bHj6g0BEpcLEgogqVEpKCoCCe/MfTSzs7Ozw/fffF7ndxIkT8cMPP+Cbb77BlClTyiUWZ2dnjBw5UuNWkMeNHDkSrq6uxe5nyJAhpW5z5cqVGDVqFG7evImWLVtK5bNnz8acOXNKvZ/H7d+/X+vCUl8XmoXeeecd6T0cOHBA4zYZLy8vHDhwAAAwcOBAje3++ecfvPvuu8jNzcWxY8fg5uamc/8tWrTAkSNH8MYbb6BevXpYsWIFevXqpVEnJycHM2bMwMsvvwwXFxcABbfg2NraPtV7+uWXXxATE4N+/fqVqr6xsTFSUlKkC9y8vDzk5uZCLpdrXfQ+PnhbqVQiICAAQMHtbSqVCv7+/qhfvz4ePHigUffxMSj37t2TBjgXSk9P17rVKyMjQ6NM6JhZycXFBbGxsQCAt99+GwA0BubHx8cDKPj3k5ubq30Q/mNubo79+/cDADZs2ICQkBCMGTMGqampiI2NRd26dQEUfFdcXV2lZaDg+zJv3jw4ODhg5MiR+OKLL5CZmYkPPvgAZ8+exZYtWyCEwMCBA1G7dm3cvn0bTZs21TouugwbNgyvv/46FixYUOK/bSIqGyYWRFShevbsiT///BNt27bF8OHD0blzZ/j6+gKA1n31hSZNmoQlS5Zg0aJFmDRpUpljmDNnDubOnVvq+oW/Io8ePVrnWIPjx4/jpZdeeqIYWrRoIV3UlfQra2m89NJLWLlyJYCCqV+nTZuGixcvAihI5tq0aVOq/Xh5eZU5Fl3MzMykz/nxnpnIyEjUr18fP/30E/z9/XHr1q0i99O2bVtcu3YNY8aMkXooHvXVV1/h3r17GDt2rNSLlJmZiaSkJI1eJRsbmyITmEft2LED3bp1g7W1dane5xtvvIGQkBAMGzYMx44dw/Tp0+Hh4YHffvsNHh4e2Lp1K1auXIkNGzagTp06Gtt6enri0KFDAKDxfTp//rzGBXytWrWwceNGjTq6LpBHjBiBESNGaJSNHTu2xMH8AQEByM3NRatWrfDFF1+ge/fucHNzw+effw6gYIC3iYkJ3NzcSjWdb0REBCZNmoSJEyfC3d0dJ06cQJcuXZCbmwuZTIYjR47g/fff1/ihYeTIkThz5gyOHj0qzSI1c+ZMREVFYfv27Thy5Ajs7Oxw48YN1K5dGzdu3Cj1QP1u3brByMgIu3btwujRo0u1DRE9HSYWRFShRo4cidDQUCxZskT6T93Z2Rk9e/bEqFGj0Lp1a436kydPxuLFiwH8/5fSshozZgzeffdd+Pn54aeffpIuurdu3Yq1a9dixYoViIuL09jGxsYGXbp0KXKfqampJQ62LbywrsyOHj0Kd3d3rfK///5bGsBblF9++QW//PKLtPzorVDF6dOnD/r06QMA2Lt3L7KzsxEfH4+OHTvi119/RaNGjQAA06dPh1KphIuLC3777Tet/Zw9exbz5s2DjY0Nxo0bp7EuICBA49aXd955Bzt27Cg2rpycHBw7dgzTpk0r1fsACr4HJ0+exPz58+Hg4IBvvvkGgwcPxvXr1/Hee+/h33//xaxZs1CrVq1S77Ow5+VRjo6OOqerLRQZGanVG+Hk5IRly5bh3Xff1Sh/PGkq7PkwMjKCm5ub1vf20qVLqF+/frG3QhVat24dmjRpgn79+knJfEJCAmxtbaUetYCAAGRnZ2tst3DhQigUCkRHR+Pzzz9Hp06dAADLli1Du3bt0Lt3b1SrVg1nz56Fv78/goKC0KxZsxLjAQBTU1O0bNkShw4dYmJBVMGYWBBRhZLJZPj6668xZcoU7N27F6dOncLx48exceNGbNq0Cd9//z0mTJgg1V+8eDHWrFmDkydP4ptvvkHXrl21LvCf9JafR+/H//jjj6VfStVqNZycnHDo0CGsW7cOlpaWMDY2hlKpxODBgzFgwIAi97lz5058+OGHxbar67aT8nLixAn4+flplD2+XBre3t46L3pLM4Zk5MiR0sxJO3bs0EhEQkNDpQvU8PBwrTEWhQp7TAqnPe3WrRvs7Oyk9UX1HERFReGdd95B48aN0bt3b+zZs0ea7rRHjx5o2LAhvvvuOwDABx98oDE+oSgRERFQKpVaPQvFcXNzw+HDhzFmzBhs2bIFxsbGGD58OLZv346hQ4di9erVWLBgAQ4dOoTevXuXer9P4siRI1i9erXO5MvKykqjp2fp0qVQq9VP9KyTv/76Cw0bNsTUqVPx5ptvFlkvLy8PEydOxI4dOzTGjgQHB2v0ThgbG8PCwkJjW1NTU6hUKvz6668wMjLC/PnzERMTg6ysLLi5uWHRokU4c+YMZsyYgYYNG6J69epP9H2vU6cO/v7771LXJ6Knw+lmiUgvHB0dMWzYMGzYsAFhYWE4f/486tati08//VTjHvANGzbgww8/xPLly+Hu7o733ntPq+ei8KJErVbrbCs7OxuWlpY6161YsQKXL1/G5cuXpcG9a9asQbNmzbBw4UIkJCSgRo0aOm+zKHxwnkKhAFBwK4v4b0rVR1/Hjx9/wqPzZJYsWaLR3u7du2Fra6sVR3nccvW4+/fvQyaT4ffff8fq1aulaVz79++PxMREyGQyHDx4EM2aNZOmFS3NL8tHjx7FCy+8oJFUJCUl6RyEnpmZid69e8PY2Bj79u0r8rN+UoXjDB6NoTRMTEzw1VdfwcvLC0OHDoVKpcK5c+cwd+5cKBQKuLi44K233kK/fv20HgRYHiIjI3H06NFS1b148aLGoPLHqdVqHDt2DKdOnYK3tzfi4uKkW/++/vprKJVKeHp66hzYfunSJSiVSq3nt5w5cwYJCQnF/rvw9/eHq6sr5syZg3v37sHV1RWurq6oXbs2XnjhBdSuXRt9+vRBYmIiZs6ciUGDBsHIqPSXMI6OjtLnS0QVh4kFEVWYsLAwuLi4YO3atVrrWrdujfnz50OtViM4OFgqHzx4MADA1tYWGzduRExMDIYOHarx63/hL7BF3YoUFRVV5KxIw4cPlx7a9sUXX0jlH330ERYuXIj58+cjNzdXa5AwUPCALgCoWbNmSW+9Qvj4+Oh8NsPrr7+O1NTUIp/dMHTo0HKLwdPTU+Ohb2fOnMGVK1ewcuVKqdzHx0fn7TxFUavV2LJli9aA6fj4eJ37USgU6N+/P44ePQo3N7cnusAsTmFPWGmfQ1E4m5VMJoO9vT1u3bqFvLw87NixA82aNYOLiwtq1aqFb775BjKZDHv27EGvXr2KfDaHIcTExODgwYP48ssvkZWVhffeew+9e/fG3bt38ffff+PixYto2rSpdGuSp6cnwsLCdN6WVfiAu0cHzt+5cwcHDhxAixYtip2s4Ny5c/jyyy/h4eGBhIQEJCUloW7duvjss8+k59fY2tqif//+ePDggdaEAKWh78kNiJ5HvBWKiCpMjRo1IITAmjVrMGjQIK1BvHfv3oWJiQm8vb11bt+5c2dMmDABixcvxpIlSzBx4kQAQPPmzWFubo4dO3ZIiUihyMhIXLhwocgLj0fHWGzevBmbN28GUPBQvTVr1mDevHnYunWrzqlg//zzT/j5+ZVqEHBpXb16FU2bNi1V3cOHDyMnJ0er/PHB248rboak4OBgaTrRR0VGRuqsb2pqCl9fX6SlpWHp0qVwdXVFYmIipkyZguXLl+Ojjz5CSEgIGjduXKr3BBTcR69UKrUGHkdEROi8gDU2NsbkyZOl5aIuGDMyMpCfn4/k5GSpl6k4hUlMYmJiqeLu2rUrzp07B7lcDrlcjgMHDqBBgwYICgrC4sWLcfXqVSxduhRyuRyvvPIKrK2t0adPH9y9e7fYz/z06dPo0KFDqWIoq4iICPTt2xdNmjTB5MmT0bVrV7Rr1w5mZmZYsWIFDhw4oLOn4eDBg3j55ZelpC4lJQU///wzvvrqK6lOTk4OPvzwQ3h7e2Pfvn3w9vbGpk2bdM6qZm1tjQsXLiA6OhqvvPIKXnjhBURFRWHs2LHSZ6dUKnH+/HkABeOjnuQ7lpycDGdn5yc6NkT05JhYEFGFMTExwbJly9C/f3+0atUKH330EXx9fZGVlYUjR45g+fLlmDt3rs7ZfgotXLgQhw8fxtSpU+Hv749mzZrBxsYGs2bNwrRp09CvXz8MGjQINjY2uHv3Lr777jtYWVlh5syZ0j5u3LghPZV4x44d2LJlC+7du4eIiAhp4PK9e/cQExMDIyMj/Pbbb3j99dc1kou0tDQsW7ZM43kaarVa51iE0sycExISgj59+uDevXs6n66tS1FPHQ4KCoKRkdFTDRYvauxDScaNGwdHR0cMHDgQMpkMQUFBGDt2LHx9fREQEKCVJBTl9OnTmD59On788UeNwd+RkZFIS0tDvXr1StxHUYnFpUuX0LlzZwAFyUtJPDw8YG9vX+rPAyi4IP7777+xefNm3L59G4sXL4a1tTWMjIxgZWUFIQQOHjyImTNnon79+vj000+LvEUtNDQUr7/+Ovbu3VvstK665Ofn6/wuRkdHa5SnpaVpDMJu0aIFUlNTNRKvwil8v/76a6xatQrt2rWTehWzs7ORkJCA1157DadOncKLL/6vvTsPi6rs3wB+zzAwwzDs+yai4q65i/uWpqZlmZbLa2qlpvUr9yz33myx0rJcStNcKvXNNM3KzA0X1MoVwQ2QHWRfhoGBeX5/ICdHFlGGAeT+XBcXzlnmuecwHubLOc/zBAIAPvroIygUCqlfUvHVj7///hsnTpyAu7s7VqxYgalTp8Ld3R0DBw4skXXPnj24fv06Hn/8cZw+fRq+vr5IS0uDt7c3srKy8MwzzyAlJQWffPIJZs6cCU9PT6P+WeUJCwt7qD5IRPSAzDsfHxHVRSdOnBDDhw8XXl5ewtLSUri7u4t+/fqJ3377TdqmrNmyhSiaJdrKykoEBASIrKwsafl3330nevToIRwcHISVlZXw8fER48ePF5GRkUb7b9myRWg0GtGxY0cxfvx48dFHH4lffvlFzJ8/X3h7e4tt27YJOzs78fzzz4vLly+LBg0aiICAAPHTTz8Z5WvYsKHQ6XRCCCG+/vrr+860XBZXV1cBQAwdOlRcvHhRZGdni3PnzokrV66Idu3aienTpz/Q8f3pp5+Evb39A+1TmZm333//faFWq8X58+elZXq9XnTq1EmMHTtWqNVqodVqRVxcnLhx44Zo2bKlWLZsmdFzGAwG8dVXXwlra2sxa9YsIYQQX3zxhVi5cqU4deqUeOutt4RKpRI5OTn3fS3r1q0T/fr1kx4/8cQTYubMmaKwsFBERESIuLi4+z5Hseeee0707t37vtvpdDrRunVrAUAEBASI+fPni/j4eCFE0XvDz8/PaPu4uDjxzjvvCBsbG7Fhw4YSzxcQECAAiCeeeEJcvnzZaN39Zt5+kNm/AYjhw4eX+VwHDx4UDRs2FPb29mL37t3S8pycHOHp6Sk9R+vWrUVhYaEQQojg4GChUCjEZ599JoQQIiQkRLRr1064urqKY8eOGT3/O++8IxQKhZg+fbpITk4WQghRWFgojh07JubMmSOcnJxE69atxU8//SQGDRokvLy8xP79+0VAQIDw9fWVjs3SpUsFADF79uxSZ2a/m16vFxqNRnz55ZflbkdElcfCgojqhNI+fKxatUp4e3uLESNGiLffflvaJjk5WUyaNEn6oJSRkSGeeOIJcebMGWnf0j48Fjt8+HC5hcXu3bvFiRMnpMdarVbY2toKAMLBwUH8/vvvD/TaHqaw0Ol0IjQ0VOTn55e6Pi8vT9y+fbvE8nPnzgmVSiV+/vnnEuuys7PF4MGDxeTJk4UQQqxZs0ZYWFiIBg0aiLCwMGm7f/75R7Ro0ULY2tqKtWvXSsv37dsnWrduLTQajXB0dJSO/4MqLiwexq5du4SlpWWpr/1eCQkJUjFxt/LeG9HR0aUe8507d5ZZPFSksKjoz3/MmDHlFhaJiYli+vTpIiEhocS6zMxMERoaKkJDQ40Kvt9//12MGDFCGAwGsWXLFqFQKMSgQYPErVu3Sm1j586dwsfHR6jVanHr1i2h1+tFu3btRPfu3cW3334rFSwFBQXiwoULokePHmL48OElfiZr164Vb7zxxn1f8759+4RSqSz1Z0VEpiUTogrHQyQiqgWEEOzY+QASExPLvF89NzcXWVlZcHNzK3P/goICfPHFFxg9enS521UHg8GAZs2aYerUqXjjjTeqO06tk5OTg6CgoFJvdbqbXq/H2bNn0bVrV+mxpaVlqdvm5+dXaA6NsowYMQIuLi7ShJJEVHVYWBAREd3lp59+wowZM3D16tVKfaCl6nf58mX06NEDly9fLnUiSCIyLQ43S0REdJdnnnkG3bp1k0YMo9rro48+wocffsiigshMeMWCiIiIiIgqjVcsiIiIiIio0lhYEBERERFRpXGCvIdgMBgQFxcHW1tbjiRDRERERI8sIQSysrLg5eUFubz8axIsLB5CXFwcfH19qzsGEREREZFZREdHw8fHp9xtWFg8BFtbWwBFB9jOzs5s7er1ehw4cAADBgwoc7xvZmCGutI+MzBDTWqfGZihJrXPDMxgSpmZmfD19ZU+/5aHhcVDKL79yc7OzuyFhVqthp2dXbX+52AGZqgJ7TMDM9Sk9pmBGWpS+8zADFWhIrf/s/M2ERERERFVGgsLIiIiIiKqNBYWRERERERUaSwsiIiIiIio0lhYEBERERHVQBlaPU7dTMG3JyMhhKjuOPfFUaGIiIiIiKqREALRqbkIictAaHwmrsRnIjQ+C7HpudI2/Zu7w8vBuhpT3h8LCyIiIiIiM8rOK8CZiBRciM5AZEoOzkSkIj5DV+q2Po7WaOZpB52+0MwpHxwLCyIiIiIiExFCIF2rR2x6LuLScxGdmoPjkXL89sMFxGfmIS49F7ez83DvnU1WFnI08bBFM09bNPe0QzNPOzT1tIO9de2Z+4KFBRERERHRA8rQ6hGdpkV0qhbRaVpEJGtxIykLN5KykabV37O1HIhPNFpSz0mNTv5OaOBqgxZe9uhU3wnWVhbmewFVgIUFEREREdE9hBC4nZWH8OQcRCbnICKl6Ht0ai6i07TI0hWUu7+LRglvBxU87JTIS0tA1zbNUM/ZBl4O1vB2sIazRmmmV2I+LCyIiIiIqM7KzS9EdJoWofGZCEvIwq2UHEQka3ErJQfa/PL7NbhorODjqIavkxr1ndVo5KZBIzcNGrhopKsPer0e+/fvx+CufrC0rD23NT0MFhZERERE9MgyCCA2PRfxmZnSbUvRqVpEpWoRnZaL21l5Ze4rlwE+jmrUd7GBv3PR93pORYWEj6M11Fb8KH03Hg0iIiIiqrWEEEjT6u8qFrRFtyulahGVmoOYNAsYgoPKfQ5bpQIB7ho097KDv4sG/i5q1HMqKiKsFJz2raJYWBARERFRjSaEwK0ULcKTsxGVUnSlISq16MpDTFousvPK6+8gg6WFDD6ORVcZfJ3URVcdHO98d7KGvbUlZDKZ2V7Po4qFBRERERHVCAaDQEKmDrdSiq423ErR4laqFuej0o0miyuNu51SKhZ8nNTwdbSGl70Vrp8LxgtPD4JKaWWmV1F3sbAgIiIiIrMoNAhk5OqRmpOH6NRc3ErJwa1UbVEBkZKD6LRc5BcYSt3XSiFHI1eNdJXB905fB987VyJUliWHatXr9Ui+AljIeTXCHFhYEBEREdFDMxgEUnLyEZeei/iMXMSl65CYqUNytg5XI+T4NvYM0rV6pGnzkZ6rLzEx3L0Uchm8Ha1Rz0mN+s428HNWI8Dd9pGY5+FRx8KCiIiIiEolhEBmbgHiMv4tGooKiH+/J2TokF9Y+lUGQA6kpZdYaqtSwNvBGn7Oavg5F3WS9nNWw8/JBl4OKigs2GG6NmJhQURERFRH6fSFiEkrKhri03VFBcSd78WFw/3mcgAAmQxws1XC094aXg4qeNhZw8HaArHhV9GjUzu42FnDycYKDmpLOFhbcaSlRxQLCyIiIqI6QF9owN+30nAgJBFhCZmITM5BfKbuvrcmAYCj2hJeDtZS4XDvd3c7FSzvucqg1+uxPycMT7Rwf+QnhqMiLCyIiIiIHgEGg0Bydh7iMnSITsnG4TgZzuwNRVRaLm6laBGbnotCQ8kqwsbKoqhocLCGl73qTgHx73dPe2v2baAKYWFBREREVEvoCw2ISM7BtcQs3ErRIiataB6HmLRcxKbl3tPXwQK4FW20v721Jfo3d0dnfyc0cLVBfWcbONlYcQ4HMgkWFkREREQ1REGhAfEZOkTdmUU6Jk2LhIw8JGbqEJ9RNCmcvrDse5fkMsDNVgUPeyWgTUNgi4Zo4GqLes5FIyy52Soh59CrVEVYWBARERGZUW5+ISJTchB+OweRKTmIvlNERKdpEZeuK/V2pbvZWFkgwN0WDVxtpDkcfJ2KvnvYFY2opNfrsX//fgzuH8D+DWQ2LCyIiIiITKig0IAELXD02m0kZOkRm150m1Lx94RMXbn7Wynk8Lkzj4OPY1GHaQ87FTzsVfBzVsPbwZq3LlGNxMKCiIiI6CHpCw2IStXiUkwGLsSk42JMBkLiMqDTK4AL58rcz97aEg1cbeDvbIN6zkWzRxd/5+1KVFuxsCAiIiIqR05eAW6laO/0e8iR/l3eSEtKuUB9V1v4OKrh7WgNbwdr6Xs9JzU7TNMjiYUFERER1XlCCKTm5CMqVYvQ+Cxcic/A1YQsRCRrkZydV+6+Kks5mnna4TEfB7T2sUdzDw1CzxzFkCe7sn8D1SksLIiIiKhO0ekLcTUhC1fiMxESl4ErcZkIS8gqd4ZpB7Ul/JzUqOdsc+e7Gn5OaviVMtKSXq/HVV6MoDqIhQURERE90qJTtfg9JAGXYzNwJT4TN2/nlDnykputEk08bNHc0w7NPO3Q0FWDes5q2FvzygPR/bCwICIiolotS6dHQoYOCZk6JGToEJemxZlwOX7edg7xGXm4Ep9ZYh8nGyu08LJDc087NPeyQwsvO9RzsoGVQl4Nr4Do0cDCgoiIiGqkQoPA7aw8qWBIzCwqHhKLi4g7/84p9RYmOZB4GwAgkwFdGjijSwNntPC2Q3NPe7jbKdl5msjEWFgQERGR2eXkFSAiOQcxablIyclDSnY+krPz7hQPeUjM0CEpS4f7zBUnsVUppLkeXDVWyE6KQbd2LeDtaIMW3nbwtLeu2hdERCwsiIiIqGro9IW4laJFRHI2IpK1iEzOQURKDiKSc3A7q/yRlopZyGVw1SjhYa+SCgd3OxU87JVF3+8sU1v9+5GmaNbpKAzu5MtRmYjMiIUFERERPRQhBNK0esSkaXHzdjZi03KRlKnDX2FyfBwWhJj0XIhyrjg42VihnpMarrZKONtYwVljBXc7lVHB4KJRwoKTxRHVCiwsiIiIqEyFBoGI5BzcvJ2Nm7ezEXE7B7HpuYjP0CEuPRd5BYZS9pIDyAVQdIuSv4sN/F1sUN/Z5t9/u9hwpCWiRwwLCyIiojqkoNCApKw8xGcUdYiOz8iVRlTS6Q0ABIQABID8AgMuxqQjU1dQ7nO62irh71I0v4OjWoG0mJt4uk9nNPa0h6uGnaSJ6goWFkRERI8IIQSy9cCl2AwkZukRm56LmLTcouIhMw8JGbm4nZVX4Q7RxawtLdDQzQYNXTVo4KKBr5M1PO2t4e1gDXd7JZQKC2nbov4NN9DZ34n9G4jqGBYWREREtUShQSAhU4fYtFzEpedKhUNsei5i07SITc+FTq8A/jpd7vMo5DK426ngaV/Uj8HzTodotZUCMhkgAyCXySCTAQHutmjpZQeFBed3IKLysbAgIiKqQQwGgZi0XFxLzMK1pCzcSMouKh7ScpGQqStzxui7udkq4e1YdEXB29EaXvbW8LRXwdPeGh72KjjbWEHODtFEZGIsLIiIiKpBSnYeridlI/LO0KsRKTm4npiNG0nZyNWXNuFbEYVcBk8HVVHR4KCGt6M1fO4UEO4aS1w4dQRPDRnA25CIyOxqRGGRn5+PNm3aYPXq1ejduzcAYObMmfj000+Ntlu1ahVee+01AMDWrVsxf/58JCUloV+/fli/fj3c3d0BAAaDAW+99RbWr18PIQTGjx+Pjz/+GBYWRfeAJicn45VXXsHvv/8OV1dXLFq0CBMnTjTfCyYiojpDm1+A89HpuJqQhetJ2biRmI3rSVlI0+rL3MfKQo4GrjZo7G6LADcN6jmrpasPbraqModf1ev1COEdS0RUTaq9sMjLy8PYsWMRGhpqtPzKlStYtmwZXnnlFWmZRqMBAJw+fRqvvPIKtm3bhtatW2PatGkYN24cfv/9dwDAp59+ip07d+LgwYPQ6/V4/vnn4ebmhnnz5gEAxo8fD4PBgMuXL+Ovv/7Cf/7zHzRt2hRdu3Y106smIqJHiTa/ANfiM/FPsgw3D99EQua/oy5FJOegoJTbl2QywMfRGg1cNHC3U8LbQY3G7ho09rCFn5OafRqIqNap1sLiypUrGDNmTKnrQkND8frrr8PFxaXEutWrV+P555/Hs88+CwD46quv4O/vj/DwcDRo0ABffPEFFi9ejHbt2gEAli5divnz52PevHmIiIjAL7/8goiICNSvXx8NGjTAb7/9hrVr17KwICKiUuUVFCIxIw83bmfhakI2EjN1uJ2dh9tZeYhO1SI+Q3dnSwvg+s0S+3vaq9DK2x4B7hoEuNmikZsGDV01sLayKLEtEVFtVa2FxbFjx9CnTx+8++670tUIAMjJyUFUVBQCAgJK3S84OBhz5syRHvv5+cHb2xunT5+GtbU1bt26hR49ekjru3XrhujoaMTHxyM4OBg+Pj6oX7++0fqPPvrI9C+QiIhqBX2hAVGpWtxMykZkSg7i0ovmdyiaBE6H5Oy8+z6Ho9oSDhb5aBfgjfrOmjujLVnDz1kNH0drzuVARI+8ai0spkyZUurysLAwCCHw5ZdfYs+ePXBycsLMmTMxevRoAEBMTAy8vb2N9vHy8kJsbCxiYmIAwGi9l5cXAEjry9q3LHl5ecjL+/eXSmZmJoCie1n1+rLvkTW14rbM2SYzMENNbZ8ZmOFh2k/X6otmkU7OQfjtHEQk5yA8OQdRqbml3q50N6VCDl9HazTxsIWPgzVcbK3gYmMFbwdr+LvYwMYS+OOPP9C/f5MSHacLCsqfYM5UqvvnwAw1o31mYAZTepDcMiHEA06TUzVkMhkOHz6M3r17Y9u2bXjxxRfx4YcfYsCAATh+/DjefPNNbN++HcOGDYOFhQUOHjyIPn36SPv37NkT/fv3R+/evdGzZ08YDAbpr0MGgwEWFhY4duwYjhw5goMHD+Lo0aPSvocOHUL//v1RWFj6KByLFy/GkiVLSiz/7rvvoFarTXwkiIioMgoNQGoekKSTITEXSMqVISm36N/ZBWVfNbCSC7hbA64qAScl4KAUcLACHO98t1EU9YsgIqpLtFotRo8ejYyMDNjZ2ZW7bbV33i7NmDFj8NRTT8HW1hYA0KpVK4SGhmL16tUYNmwYVCoVdDqd0T55eXmwtraGSqUCAOh0OlhbW0vrAEjry9q3LPPmzcOMGTOkx5mZmfD19cWAAQPue4BNSa/X3/lrWP9qG0aQGZihprTPDHU7Q5ZOj6jUXESlahGVmotbKTm4cDMGOTI14jN05c4s7Wmvgr+LGg1dbNDA1Qb+LjZo4GIDDztlpW5Xqos/B2aome0zAzOYUvGdOhVRIwsLAFJRUaxp06Y4dOgQgKLbnOLj443Wx8bGwtvbW7rNKT4+Hg0aNJDWFe9X3r5lUSqVUCqVJZZbWlpWyxukutplBmaoie0zw6ObQQiBzNwCJGbpcDk2AxdjMnApNgPht7PLGKpVDqDoD0dKhRz+LjZoeKeTdENXGzR01RTdsqSs2l99j9rPgRlqb/vMwAym8CCZa2RhsXTpUvz5559GtyudO3cOzZo1AwAEBgYiKChImnsiIiICsbGxCAwMhJeXF3x9fREUFCQVFkFBQahXrx48PT3RuXNnxMTEIDIyUurAHRQUhMDAQPO+SCIiQqFBID4jF9GpuYhO1SIiJQcXY9JxPTEbqTn55fZ7cNFYwddJDT8nNbwdVEiPvo4hfQLRwM0ObraVu/pAREQP7oELi759+0pXDkqzb98+rFq1SppT4mE8+eSTePfdd/HJJ5/gueeew4EDB7BlyxYEBQUBACZPnox+/fphyJAhaNu2LV577TUMGjQI/v7+AIBXX30VixcvRvPmzSGXy7F06VJMmzYNANCwYUP0798fU6dOxapVq3DhwgVs374dR44ceei8RERUvvwCAyKSc3A1MQvXE7OkyeKiU7X37TRtq1SgiYctWvs4oLWPPRq726Kesxqau6486PV67N9/DR38HGvlXwSJiB4FD1xYHDlyBL6+vmjVqhUee+wxdOvWDQMHDoRCocDhw4cxatQobN68uVKh2rdvj127dmHBggWYP38+6tevj++//x4dO3YEUDQ87OrVqzF9+nTcvn0bAwYMwPr166X9Z8+ejcTERAwYMAAA8NJLL2HWrFnS+s2bN+Oll15Cy5Yt4ebmhq+++opXLIiITCRTp8eVuExcictESFwmrsRn4kZSFvSFpRcQVhZyeDtaw9dJDV9Ha7TwskcLLzu42SnhqLaCypJzPRAR1QYPdSvU8ePHcf36dVy4cAGff/45Jk+ejC5duuDPP//E119/jWeeeeaBn/PewamGDh2KoUOHlrn9xIkTpVuh7qVQKLBy5UqsXLmy1PXu7u7Yt2/fA2ckIiJjhQaBqFQtLkal4scIOdZ8eQpXE7NQ2niDtkoFGnvYorG7bdEM0+628HexgbudChZy3rZERFTbVaiwOHnyJKysrFCvXj3IZDLUq1cP9vb2UCgUiIqKwuXLlxEVFQW5XA5XV9eqzkxERNUgJTsPVxOyEJqQhasJmQhLyMK1xCzo9IY7W8gBZAEAvB2s0dzLDi287NDc0w7NPO04SRwR0SOuQoXF559/jmvXruHmzZsQQsDb2xt5eXkYMmQIAgMDcfbsWXh7e2PPnj147rnncOzYMbRq1aqqsxMRURUw3LkKERKXiZC4DFy+c1tTWbNPKxVyNHKzgZMhA8/1bIMujVzhZqcyc2oiIqpuFSosfvjhBwBAamoqfvnlF1y7dg3r169HREQEXnjhBWmo1qeffhoLFy7EmDFjcO7cOVhY8L5YIqKaSgiBxMw8XEssuvJwPTEb15KKvmfnlT5TtJ+zGk3cbdHUwxZNPe3QxMMW9Z1tYCgswP79+zG4lQc7TxMR1VEVKiy+/fZb7N27F+PHj8eaNWuwYsUK6HQ65OXl4fbt20YjRfXr1w9JSUlVGpqIiCpGm1+AuHQd4tJzpWFdo1K1iE7T4mZSNjJ1pRcQVgo5mnrYFt3KdKczdRN32zLngDAUVuWrICKi2qBChcXYsWOxc+dO7NixA0IITJ8+HevWrZNudxo/fjyaNGmCuXPnYuHChfjyyy95tYKIyEyEEMjIB07eTMGtNB1uJGVLX0lZpd++VEwuA+o72yDAXYMm7rYIcC/qXN3A1QaWFnIzvQIiInoUVKiwmDdvHvz8/JCUlITo6GgMHjwYW7Zswfjx49G8eXMAwBdffIGxY8ciMDAQTz/9dJWGJiKqq/ILDIhMycGNpGzcTMrGhZgM/BWZivRcBfD336Xuo1Eq4OWggqe9NXydrOHrqIavkxr1nW3QwNWGw7kSEZFJVKiwcHd3R1paGrRaLdLS0uDq6goHBwcMHDgQ3bp1g0wmQ+fOnQEAZ86cQWJiItzd3as0OBHRo0wIgcgULSKSs3ErRYt/otJxOTYDUalaFJYyoZwMAn7ONmjkZouGbjZo5KpBQzcNGrpoYK9mnwciIqp6FSosRo8ejS5duuDxxx+HWq3G9u3b8eOPP2LQoEEIDg7G9u3b0aRJEwwcOBBeXl5477338Pnnn1d1diKiR0JaTj6uJ2Xf6UBdNCP1lfhMpGv1pW6vUSqKigZXGzT1sEU7X3vcOn8CTw3pzo7TRERUbSpUWPzxxx+YMWMGmjRpgpCQEKxYsQLjx4/HuXPn0LJlS0yaNAmbNm2CSqWCn58f2rdvj08++YS/4IiI7pKuzce1xGxcvzPyUtFoTNllDuNqpZCjkasG3o7WaOVtj7b1HBDgZgt3O6XRfBB6vR6xF831KoiIiEpXocJi3LhxAIC4uDgsWLAAgYGB2LFjh7R+48aNeOKJJ6THH3/8MRSKh5rUm4io1svQ6nEt6d8hXK8nFRUQt8vpSO3tYI2AO7NRB7hp0MTDFk097GClYAdqIiKqHR7o07+Xlxe8vLwAAAEBAdLyF198Ufq3wWDAhAkTTBSPiKjmytDqcSUuDScTZfj7lzDcTM55oAKikdu/3zVlDONKRERUWzzQb7K9e/eiTZs28PX1LXObwYMHY8mSJVJnbiKi2ixDq8eN21kIv52D2PRcxKXnIiYt956hXC2A8Cij/bwdrO8UDhppCFcWEERE9Ch7oN9wTz/9NBQKBZo3b44PPvgAAwcONFp/6NAh/PXXX9L8FkREtUHxDNRFcz9k4cbt4nkgcsrs/1DM014FB5kWXVv4o4mHPQLuFBIsIIiIqK554N98cXFx+OOPP6DRaIyW37p1Cy+++CJWr14NtVptsoBERKaSmpOPhIyiWaiLi4frSdkIT8pGVl7pM1ADRcVDQ1cNfByt4e1gDS8HazRwtUEjNw1UFsD+/fsxeGATDlhBRER12gMVFjKZDHZ2dhg1apTR8s2bN2POnDl45513MHLkSJMGJCJ6GKk5+bgUm4GL0em4GJuBSzEZSMjUlbm9hVwGPyc1Grpp0MhNg0auRd8b3uf2Jb2+9CFhiYiI6pqHula/f/9+7NixAxqNBn/++SdcXV2xf/9+tGvXztT5iIjuKzNXj6u3MnAxNgMXY9JxMSYDMWm5JbaTyQBnGyXc7ZRoeKdwKP7yc1ZDqeAM1ERERA+rQoXF//73P9SvX1967OHhgS5duiAmJga+vr44deoU9uzZw8KCiKqUwSAQmZKDqwlZuJqYhavxmfjrpgVunzpc6vYNXGzQyscerX0c0NrHHs097WDDvg9ERERVosIT5B08eBBCCMyePRvTp0/H5MmTpfXnzp3DqFGjEB4eji1btlRZWCKqW5Kz83AhOh3n73xdiE5Hpu7evhBFE8X5OlmjtbfDnULCHi297WGnYp8HIiIic6lQYbFu3ToAwIULF7B69Wq0bt0as2bNwvz58yGXy9G2bVucOHECXbt2xYoVKzB9+vQqDU1Ej5Z0bT5i0nIRk6bFrRQtLsZm4HxUOmLTS97OpFTI0cSjaPjWhi5qZESFYuKwfnCzt6mG5ERERFTsge4JeOyxx7BmzRr83//9Hz788EPI5f/OCOvs7Ixdu3ahZ8+eGDFiBHx8fEwelohqtwytHiFxGYhK1SL6ThFxPjq91P4QQFGfiIauGrTxdcBjvg5o6+uAJh62sLQoOvfo9Xrs338Fjmorc74MIiIiKkWFC4vs7GxMnjwZXl5eWL58OVatWgU/Pz889thjGDp0KEaOHIkWLVpgzpw5SElJYWFBVIflFxhwLTELV+Iy75oTIhtRqdoy93HRKOHjaA0fR2s087RDG9+i25p4OxMREVHtUOHCYty4ccjKysKnn34KALC3t8fZs2dx5MgRbNu2DbNmzcJrr72G+fPnw9rausoCE1HNUWhAUUfqJC3CEjIRn6HDrRQtriZkIb/QUOo+9ZzUqO9iA19Ha/g6qdHCq6iIsGUBQUREVKtVqLC4du0aYmJiEBQUBKVSKS13c3PDyJEjMXLkSFy6dAn/93//h379+uHkyZNVFpiIqkdqTj7+ikzF9aRsRKVocSU+A6FxFig4farU7e1UCrTwskcTD1s0dNOgoasNmnnYwdGGty0RERE9iipUWDRu3LhEUXGvVq1a4dChQwgLCzNZOCIyP32hATeSsnEpNgNh8Vm4npSFG0nZiM8obXI5GWyUFmjhaY/mXnbSzNQtvOzh62QNmUxm9vxERERUPSp8K1R5RUUxmUyGZs2aVSoQEZlPSnYeriZkITQhC1cTMhGWkIWwhCzkF5R+G1MjNw1a+9jD11GNhi7WSLr6D/7zTH8olbwKQUREVNeZbKYog8GAfv364cknn8SsWbNM9bREZCI6fSGuJ2bjSnwG/r6VhhM3UkodzhUAbJUKtPC2Q3NPezR21yDAXYNGrrawV//bD0Kv12N/FCCX86oEERERPUBh4eXlVea6kSNHYsWKFXj//ffRrVs3zJw5k7dAEFWjDK0eIfEZuBKXWfQVn4kbSdkoMIgS2/o5q9HUwxZNPOzQzMMWzTztUM9JzYKBiIiIHkiFC4uEhASEhIQYzV0BABcvXoTBYEBQUBD0ej2EKPnBhYiqTkGhAVeTMvBPVBr+uZWGf6LSyxzW1UFtiRZedmjpZY+ujVzQwc8RNkqTXbgkIiKiOuyBPlEEBwfj+vXrsLOzg7e3N9q0aYMTJ07gzJkzUkHx+uuv82oFURVKzs7DP7fS8FdkCg6FWOCtvw4hV1+yT4SPozVaeBXdztTCyw7Nvezgaa/i/08iIiKqEg9UWBQWFsJgMCA6OhqnT5/G/PnzkZubi9GjR2Pu3Lnw9PSsqpxEdVJBoQFhCVnlXI2QATDAVqVAG18HtKvniHZ+jmjj42DUH4KIiIioqj1QYdG9e3f07NlTeiyEwIULF/DNN9+gZcuW2LZtGwYOHGjykER1RfHViH+i0nEuKg0XYzKQqy8ssV2AmwZtfO1hkRaFcU/2QFNPB/aJICIiompV4cLC29sbAwYMgBCixK0Uo0ePxowZMzBq1Cjs2bMH3bt3N3lQokfN/a9GFClxNcLXAfbWlkWjMu2/hQA3DYsKIiIiqnYVLixGjBiB5cuXw8LCAkIIfP7553jjjTcAAOvXr4eHhwfWrFmDUaNGISwsDDY2NlUWmqg2epCrEUVFRFEx0dCVhQMRERHVfBUuLD777DN8+OGHsLCwgMFgwIwZM6TCIisrC9u3b8d7772Hr7/+GkFBQbwliuq0yl6NICIiIqptKlxYCCHQv39/yOVyCCEghEDfvn0BAOnp6dIVit27d/NqBdU5mTo9ToenSoXE/a5GtK3ngHZ+jmjEqxFERET0iHigztuvvfYaLC0tUVhYiGPHjklXLIQQmDp1KnQ6HYsKqhOSsvJwOVWGsD+u41REGi7GpOPeued4NYKIiIjqkgcqLJ566ilYWVnBYDBArVbj6aefltZ99tln+Pvvv9GtWzeThySqbrez8vD3rVScjUzD8evJuJqYBcACuBohbePvYoNO9Z14NYKIiIjqpAoXFhEREbCysgIAyOVyZGVlGa1fvnw5OnToYNp0RNVACIGYtFyciUgt+opMRURyjtE2MhngrhIIbOyFbgGu6NbIBV4O1tWUmIiIiKj6Vbiw8PPzK3c9iwqqrQwGgWtJWTgbmYazEak4G5mK+Ayd0TYyGdDE3RYd6juik78zAuvb49SRgxg8uBUsLXl7ExEREdED3QpVmqysLKSlpZVYXq9evco+NVGVyC8w4O9bafgrMhV/3UrDP1FpyNIVGG2jkMvQyscenfyd0NnfCe3rORnNZK3X680dm4iIiKhGq3BhMW3aNPz666/SY5lMhhs3bmD16tVYvHixtDw/Px9KpRJabcmhNYmqi77QgLMRqfg9JAE/X4hDmta4MFBbWaBtPQd0rO+ETvWd0KaeA9RWla67iYiIiOqMCn9ymj59OsaNGweg6B70bt26QQiBuXPnYu7cuQCA7Oxs2NnZsaigGiM5Ow+bTkTi+zNRSMnJl5a7aJTo3MAJHf0c0aG+E5p62EJhIa/GpERERES1W4U/STVq1AidO3dG586dERgYWOo2MpkMMtmDj4KTn5+P5s2b48iRI9Ky8PBw9OnTByqVCk2aNMG+ffuM9jlw4ACaNWsGa2tr9OzZE9euXTNa//HHH8PT0xMajQbjxo1Ddna2tC43NxcTJ06Era0t3N3dsWzZsgfOTDVbdKoWK/64hl4fHcYXh28gJScfLhorjGjvg40TOiJ4Xl98ObodxnfzR0tvexYVRERERJVUqXs9HqaIuFdeXh7Gjh2L0NBQaZnBYMCwYcPQoUMHbNq0CXv37sWIESNw5coV+Pv7IyoqCs888ww++eQTDBw4EIsWLcKwYcNw+fJlyOVy/O9//8OyZcuwa9cuuLi4YNy4cZg5cybWrVsHAJg9ezYuXryIM2fOIDY2FiNGjIC/vz9GjRpV6ddD1SciOQe/XIzDr5cTEBKXKS1v7WOPV3s1RP/m7iwgiIiIiKpIhQqLPn36lFpE9O3b12h5QUFBiW3Kc+XKFYwZM6bE8qNHj+LmzZs4deoUbGxs8Nprr2HXrl3YuHEjli5dig0bNqBjx46YMmUKAGDNmjVwc3PDkSNH0LdvX6xatQr/93//h969ewMAVq5ciQEDBmDFihWQyWTYsGEDfv31VzRr1gzNmjXD9OnTsWbNGhYWtVBcei72XojD3otxuBz7bzEhlwGd/Z0xunM9PNnKk/NJEBEREVWxChUWL7zwgtFjIQSOHj2KkSNHQi7/9y/AOp0OJ06cqHDjx44dQ58+ffDuu+9Co9FIy4ODg9GuXTujWby7deuG06dPS+t79OghrVOr1Wjbti1Onz6NPn364MyZM1i4cKG0PjAwEAUFBbhw4QKAohF9unbtavTc77//PoQQJrkKQ1UrI1ePk4kybN1wFmcj/x2RzEIuQ9eGzniylSf6N3eHs0ZZjSmJiIiI6pYKFRaTJ08usWzatGmYPHmyUWGRnZ2NGTNmVLjx4isO94qJiYG3t7fRMi8vL8TGxkrrn3nmmVLXJycnQ6fTGe1vZWUFZ2dnxMbGQggBFxcXabK/4n11Oh1SUlLg4uJSIk9eXh7y8vKkx5mZRX8Z1+v1Zh12tLit6hzqtLoy5OkLcfhaMn6+EI8j125DX2gBoKio6FjfEUNaeeCJFu5wtvn351qVGevyz6KmtM8MzFCT2mcGZqhJ7TMDM5jSg+Q26Xiapvprv06ng1Jp/NdmpVKJ3Nzc+67X6XTS47L2L20dAGn9vd5//30sWbKkxPIDBw5ArVZX9GWZzB9//GH2NqsjgxBAeBZw5rYcF1JkyC389/3laS3QwdWAdi4CTsrbQPJtnD56qcoz3auu/CxqcvvMwAw1qX1mYIaa1D4zMIMpPMhorxUuLL799lvpViIhhLQ8NjZWupJQ3PDZs2fRsWPHCoe4l0qlQnJystGyvLw8WFtbS+uLC4i719vZ2UGlUgFAqeutra0hhCh1HQDp+e81b948oysxmZmZ8PX1xYABA2BnZ/cQr/Dh6PV6/PHHH+jfv3+1zfZsjgx5+kLsvZSAb09FISwhS1ruYafE0NaeGNzCFVEXTz3yx6GmZ6ju9pmBGWpS+8zADDWpfWZgBlMqvlOnIipcWMjlclhYWEiPZ8yYAblcjm3btmHNmjXS8nr16uH5559HeHh4hUPcy9vbG5cuGf/1OTY2Vrq9ydvbG/Hx8SXWt2nTBs7OzlAqlYiPj0ezZs0AFA1nm5ycDG9vbwghkJKSAr1eL/1wY2NjoVKp4OzsXGoepVJZ4ioHAFhaWlbLG6S62q3qDJdiMrD9ryj8cjFemsBOZSnHU4954Zm2Pujs7wS5XAa9Xo+oi4/ucahtGaq7fWZghprUPjMwQ01qnxmYwRQeJHOFC4v//Oc/pS6fM2cO5syZU+EGKyIwMBDLli1DTk6O1IE7KChIGuUpMDAQQUFB0vY5OTk4d+4c3n33XcjlcnTq1AlBQUHo27cvAODEiROwsrJCmzZtIISAQqHAyZMn0atXL+m5O3XqxI7b1UAIgZC4THxzIgK7/omVlnvZqzCua3280NEXDmqrcp6BiIiIiGoCk/axMJXevXujXr16eP3117Fo0SLs378fZ8+exebNmwEAEyZMwPLly7F69Wo8+eSTWLJkCRo0aCAVCq+++iqmTp2KHj16wNPTE7Nnz8aECROkW51efPFFzJw5E99++y2SkpLw+eefS3NckHkkZOiw/Ww0dvwVjdj0f/u2DH3MCyPa+6BrQ2fOOUFERERUizxUYSGEwOHDh6UrAqYml8uxe/duvPTSS2jSpAnq16+PXbt2wc/PDwDg7++PnTt3YubMmZgxYwYCAwOxe/duaYSqUaNGISIiAqNGjUJOTg5GjBiBjz/+WHr+Tz75BFOnTkXnzp2h0Wjw9ttvY+TIkVXyWuhfSZk6HLuejAMhCfgzLAmFhqK+OtaWFugR4IKpfRqhja9D9YYkIiIioofyUIVFfn4++vfvj8LCQpMFubtDOAA0btzY6Hanew0ZMgRDhgwpc/3bb7+Nt99+u9R1Go0Gmzdvlq6AUNUxGAR+C0nA1uBbOBWegrt/zJ3qO2FMYD080cIDKkuLsp+EiIiIiGq8h74V6t5CgOhu+kID9l+Kx9qj4QiN/3c0gcd87NE9wAXD2ngjwN22GhMSERERkSlVqLC4d/xanU4HmUyG3NzcMguM6pjfgapfujYf352JwuaTt5CQWTSsr61SgfHd6mNkB1/4OvF9QURERPQoqlBhodFoSoyYJISARqMpsa0QAjKZzKS3SVHNl5aTjxUHr2HnXzHI1Rf97F00SrzYxQ//6eLHkZ2IiIiIHnEVKizuncpbp9PBzs4O+fn5VRKKapfDYUmY++NFJGUVTTTYzNMOL3X3x9DHPKFUsO8EERERUV1QocLi7onxAEChUJS6nOqWa4lZeHffFQRdL5olvaGrDZY+3RJdGzpzThAiIiKiOsaknbd//fVXuLu7o127dpUKRTVbgQH49OB1fB0UiQKDgKWFDOO61MesAU1gbcVik4iIiKgueqjCwsrKCocPHy6x3NLSEhMnTsT58+crm4tqqPgMHVaFWCAyOwIA8Hgzdywc0hz1nNkpm4iIiKguq3BhERQUhCNHjmDBggUIDQ3FrVu3SswDIYRAUlISvvvuO4wePdrkYal6Hb12G2/+cA5pWhlsVQp88GxrDG7lwdueiIiIiKhihcXy5cvx7rvvYv78+TAYDPj111/x+eefo1OnTiW2bdKkSYnhaan223QiAkv2XYEQgI+NwLeTAtHQ3b66YxERERFRDXHfwsJgMODq1as4fvw4WrduLS3v1q0bvvvuuyoNRzXD9rNRWLz3CgDg+Q4+6GgRiXqcj4KIiIiI7nLfwkIul2P9+vVGyxo2bAiDwVBloajm2HshDm/tugQAeKWHP2b3b4Rff42s3lBEREREVOM8VOftYcOGmTgG1US/hyRg+vbzEAIY1ake3h7cDAUFBdUdi4iIiIhqILmpnighIQF6vR6hoaHIy8sz1dNSNdAXGvD+r6GYvOVvFBgEnm7jhf8Oa8lO2kRERERUpgoXFgaDwejrbjdv3kTXrl3x5Zdf4oUXXsDx48dNHpTMIypFi+fWnsK6o+EAgDGd6+HjEY/BQs6igoiIiIjKVuFboSwtLaV/y2QyaLVaWFlZQavVYuDAgbCxsUGjRo1gY2ODfv36VUlYqlo/X4jDO7suISuvAHYqBT4c3hqDWnlWdywiIiIiqgUqXFgIIbB27VrIZDJMnjwZ69evR69evbBhwwZYWlrCysoKv/32GyZOnFiVeakKFBoEFv8cgi3BtwAAHfwc8dmotvB2sK7mZERERERUWzxQHwuFQgErKysAwOXLl/Gf//wHAwcOxMaNG6HVanH8+HH07du3SoJS1dDpC/Hq1r+xJfgWZDLg//o2wg+TAllUEBEREdEDeajO2zKZDB9++CFkMhlOnDiBRo0aITc3F3v27EGDBg1MnZGqSLo2H2PXn8aBK4mwUsixZkw7zBjQBAoLk/XpJyIiIqI64oGGm12wYAGEEACA+Ph4fPvtt+jWrRueeuopxMXF4ZlnnpG2/eeff0yblEwqKUuHsetP41piNuxUCqx/sSM6+TtVdywiIiIiqqUeqLB4+eWXIZfLsWTJEgwaNAgXLlzArFmzMHfuXNjb2+PTTz/lkKS1QFKmDqO+DsbN2znwsFPh24md0MTDtrpjEREREVEt9kD3vPj5+aF+/foAgBYtWuCNN95AbGwsDh06BAD49ttv4e7ujl69epk8KJlGWEImnll9Ejdv58DLXoXtkwNZVBARERFRpT10H4tVq1Zh69atOHr0KH788Ufk5+ejX79+WLRokakzkomcj07H8NUnEZuei/rOavwwqQv8nG2qOxYRERERPQIe6Faor776CkDR0LO7du1C/fr1cfToUbi5uWHUqFEYOXIkZsyYgfz8fGn0KKoZ4tJz8crmv5CTX4jO/k5Y95/2cFDzZ0REREREplHhwuLrr7+W/v3KK69gxIgReOaZZ+Dm5gYA0Ov1sLKyQocOHRAcHIyePXuaPi09lIxcPV7+9i/czspDUw9bbBjfERrlA9WURERERETlqvCny5deeqnEMjs7O+nf4eHhAICnnnoKfn5+JohGppCl0+PFb87gSnwmnG2s8PW4DiwqiIiIiMjkTPYJs7iYmDJliqmekkzg7Z8u43x0OhzUltj6cmf4OqmrOxIRERERPYIqXFgcO3as1OWWlpZwcHBAo0aNYGlpabJgVHknbyZj74U4yGXAN+M7opmn3f13IiIiIiJ6CBUuLHr37l3uehsbG7z++utYtmxZZTORCegLDVi0JwQAMKazH9rVc6zmRERERET0KKtwYWEwGEpdLoRARkYGgoOD8dprr8He3h5z5841WUB6ONvPRuN6UjacbKwwa0CT6o5DRERERI+4h5rH4m4ymQwODg4YOHAgVq1ahfXr15siF1WCTl+Iz/+8DgB4o18A7NW8RY2IiIiIqlalC4u7tW7dGjExMaZ8SnoIm09FIikrD94O1nihk291xyEiIiKiOsCkhUVCQgIcHBxM+ZT0gHT6Qqw7WjT075uPB0CpsKjmRERERERUF5issMjIyMC8efMwcOBAUz0lPYRfLsYjJScfXvYqPNPWu7rjEBEREVEdUeHO23369IFMJiuxXAiBrKwshIaGonHjxti2bZtJA9KD2XwqEgAwJtAPCguTXpAiIiIiIipTpYebVSgUsLOzQ7NmzdC3b1/I5fwwW13OR6fjQkwGrCzkeKEj+1YQERERkflUuLB45513oFDcf/PY2Fh4e/MWnOrw3elbAIAhrT3hrFFWcxoiIiIiqksqfHlh6NChyMvLK3eb9evXo2XLlpUORQ8uN78Q+y8lAACe59UKIiIiIjKzChcWly5dQv/+/ZGZmVliXUxMDAYOHIhJkybhiSeeMGlAqpjfQxKQnVcAXydrdKzvVN1xiIiIiKiOqXBhcfLkSdy+fRt9+/ZFSkqKtPzrr79Gy5YtERYWhn379uGHH36okqBUvv/9XTR/yLNtfSCXl+xkT0RERERUlSpcWNSrVw8nTpyAQqFA9+7dceLECfTv3x9Tp07FxIkTceXKFQwePLgqs1IZEjJ0OHEzGQAwvJ1PNachIiIiorqowp23AcDJyQmHDh3Cc889h549e6JFixY4ffo02rVrV1X5qAL+DEuEEEDbeg6o56yu7jhEREREVAc98NiwarUae/fuxZgxY5CYmMjhZWuAw2G3AQD9mrpVcxIiIiIiqqsqXBUsXbpU+nrvvffQsGFDyOVy9OnTBwsXLjRabyp79+6FTCYz+nruuecAAP/88w86dOgAlUqFtm3b4vTp00b7bt26FfXr14darcbQoUORmJgorTMYDJgzZw6cnJzg6OiI6dOno7Cw0GS5zUmnL8SJG0W3QfVhYUFERERE1aTCt0IdPny4xLKmTZsCAIKCgqRlMpkMCxcuNEE04MqVK3jyySexadMmaZlSqUR2djYGDx6Ml19+GT/++CPWrl2LIUOGIDw8HLa2tjh9+jReeeUVbNu2Da1bt8a0adMwbtw4/P777wCATz/9FDt37sTBgweh1+vx/PPPw83NDfPmzTNJbnM6HZGKXH0hPOxUaO5pV91xiIiIiKiOeuDCIiUlBY6OjtItUH///TeOHj0KR0dHPPfcc7C1tTVZuNDQUDRv3hwuLi5Gyzdu3Ahra2u8++67kMlkWLZsGXbs2IGdO3di4sSJWL16NZ5//nk8++yzAICvvvoK/v7+CA8PR4MGDfDFF19g8eLFUt+QpUuXYv78+bWysDgclgQA6NPUFTIZR4MiIiIioupR4VuhtFothgwZAjc3N9y8eRMA8M0336BTp05YuXIlPvzwQ7Rq1crolqPKunLlCgICAkosDw4ORvfu3aUP0jKZDF27dpVuhwoODkaPHj2k7f38/ODt7Y3Tp08jPj4et27dMlrfrVs3REdHIz4+3mTZzeX4ndugejfhbVBEREREVH0qfMVi2bJlCA8Px4EDB+Dv7w+tVosZM2agffv2OH78OKysrDBlyhS89dZb2Lhxo0nChYWF4eTJk/j888+Rl5eHF154AQsWLEBMTAxatWpltK2XlxdCQkIAFE3Y5+3tXWJ9bGwsYmKK5nu4e72XlxcAIDY2Fp6eniVy5OXlGc06XjxJoF6vh16vN8ErrZjitoq/5+QV4ObtbABAay+NWbLcm6E6MEPNyFDd7TMDM9Sk9pmBGWpS+8zADKb0ILllQghRkQ0bN26Mr776Cr179wYA/PjjjxgxYgQ2b96MsWPHAgBCQkIwcOBAREdHP3jqe8TGxsLHxwcTJ07E1KlTER8fj6lTp+LZZ5/FpUuX0L17dyxZskTafuHChThx4gT+/PNPWFhY4ODBg+jTp4+0vmfPnujfvz969+6Nnj17wmAwSFc8DAYDLCwscOzYMaMrGcUWL15s1Fax7777Dmp19Q3vGp4JfBaigL2VwNL2tbPzORERERHVXFqtFqNHj0ZGRgbs7Mrvz1vhKxaxsbFo3Lix9PjgwYOQy+UYOHCgtMzW1ha3b99+iMgleXt7IzMz06jPRn5+PsaMGYPevXtDp9MZbZ+Xlwdra2sAgEqlKnO9SqUCAOh0Omn74qsRxY/vNW/ePMyYMUN6nJmZCV9fXwwYMOC+B9iU9Ho9/vjjD/Tv3x+WlpbYHBwFhIShvb8bBg9uWy0ZqgMz1IwM1d0+MzBDTWqfGZihJrXPDMxgSsV36lREhQsLHx8f3LhxA15eXjAYDNi3bx+6dOli1LE6ODgYPj6mm/n53o7gTZs2hU6ng6enZ4n+ELGxsdLtTd7e3mWuL94mPj4eDRo0kNYV71capVIJpVJZYrmlpWW1vEGK2w1NKLoNqpWPg9lzVNdrZ4aal6G622cGZqhJ7TMDM9Sk9pmBGUzhQTJXuPP2xIkT8cYbb+Dnn3/GG2+8gdjYWLz66qvS+kuXLmHevHnSSEyVdfjwYdjb2yM7O1tadu7cObi4uKB79+44fvw4iu/iEkLgxIkTCAwMBAAEBgYaDYEbERGB2NhYBAYGwsvLC76+vkbrg4KCUK9evVL7V9Rkl2MzAAAtve2rOQkRERER1XUVLixmzZqFQYMGYcqUKdi5cycWLlyI0aNHAwBmz56Nxx57DPXr18fixYtNEqxTp06wt7fHpEmTcPPmTRw8eBBz587FvHnz8NxzzyErKwsLFixAVFQUFixYAK1WixEjRgAAJk+ejO+//x4//vgjwsPDMW3aNAwaNAj+/v4AgFdffRWLFy/G2bNn8ffff2Pp0qWYNm2aSXKbi05fiOtJRUVXCy/OX0FERERE1avCt0JZWFhg2bJlWLZsWYl1Y8eOxfDhw6UrBqZgY2OD3377DdOnT0ebNm2g0Wjw6quvYvr06ZDJZNi3bx+mTJmC5cuXo2XLlvjll1+g0WgAFA0fu3r1akyfPh23b9/GgAEDsH79eum5Z8+ejcTERAwYMAAA8NJLL2HWrFkmy24OVxOyUGgQcLKxgqe9qrrjEBEREVEdV+HCojyPPfaYKZ6mhObNm0uzZd+rc+fOOHfuXJn7Tpw4ERMnTix1nUKhwMqVK7Fy5UpTxKwWl+OKboNq4WXHifGIiIiIqNpV+FYoqlkibucAABq7m26mcyIiIiKih8XCopaKStUCAOo5Vd88GkRERERExVhY1FIsLIiIiIioJmFhUQsJIRCTlgsA8GVhQUREREQ1AAuLWihNq0d2XgEAwMex9NnCiYiIiIjMiYVFLRR952qFu50SKkuLak5DRERERMTColaKZv8KIiIiIqphWFjUQuxfQUREREQ1DQuLWqj4VihfRxYWRERERFQzsLCohYoLC94KRUREREQ1BQuLWkgqLJxZWBARERFRzcDCopYpNADxGToAvGJBRERERDUHC4taJi0fKDQIWCnkcNUoqzsOEREREREAQFHdAejBWFsA7z3dHFq9gFwuq+44REREREQAWFjUOjaWwOAOPrC0tKzuKEREREREEt4KRURERERElcbCgoiIiIiIKo2FBRERERERVRoLCyIiIiIiqjQWFkREREREVGksLIiIiIiIqNJYWBARERERUaWxsCAiIiIiokpjYUFERERERJXGwoKIiIiIiCqNhQUREREREVUaCwsiIiIiIqo0FhZERERERFRpLCyIiIiIiKjSWFgQEREREVGlsbAgIiIiIqJKY2FBRERERESVxsKCiIiIiIgqjYUFERERERFVGgsLIiIiIiKqNBYWRERERERUaSwsiIiIiIio0lhYEBERERFRpbGwICIiIiKiSmNhQURERERElcbCgoiIiIiIKo2FBRERERERVVqdLSxyc3MxceJE2Nrawt3dHcuWLavuSEREREREtZaiugNUl9mzZ+PixYs4c+YMYmNjMWLECPj7+2PUqFHVHY2IiIiIqNapk1cscnNzsWHDBnz88cdo1qwZHn/8cUyfPh1r1qyp7mhERERERLVSnSwszp8/D71ej65du0rLunXrhrNnz0IIUY3JiIiIiIhqpzpZWMTExMDFxQVWVlbSMi8vL+h0OqSkpFRjMiIiIiKi2qlO9rHQ6XRQKpVGy4of5+bmltg+Ly8PeXl50uOMjAwAQGpqKvR6fRUmNabX66HVapGSkgJLS0uztcsMzFAT22cGZqhJ7TMDM9Sk9pmBGUwpKysLACp0V0+dLCxUKhV0Op3RsuLCwdrausT277//PpYsWVJiub+/f9UEJCIiIiKqQbKysmBvb1/uNjJRBzsVnDx5Ej179kRubq5UOR48eBBDhw6FVquFTCYz2v7eKxYGgwGpqalwdnYusW1VyszMhK+vL6Kjo2FnZ2e2dpmBGWpi+8zADDWpfWZghprUPjMwgykJIZCVlQUvLy/I5eX3oqiTVyzatGkDhUKBkydPolevXgCAoKAgdOrUqdRCQalUlrh1ysHBwRxRS2VnZ1ftb0xmYIaa0j4zMENNap8ZmKEmtc8MzGAq97tSUaxOdt5Wq9V48cUXMXPmTISEhODw4cP4/PPPMW3atOqORkRERERUK9XJKxYA8Mknn2Dq1Kno3LkzNBoN3n77bYwcObK6YxERERER1Up1trDQaDTYvHkzNm/eXN1RKkypVGLRokUlbstiBmaoi+0zAzPUpPaZgRlqUvvMwAzVpU523iYiIiIiItOqk30siIiIiIjItFhYEBERERFRpbGwqCVyc3MxceJE2Nrawt3dHcuWLavyNiMiIjB06FDY29vD398f77//PgwGAwDgn3/+QYcOHaBSqdC2bVucPn26SrNMnjwZvXv3lh6Hh4ejT58+UKlUaNKkCfbt21dlbQshsGjRInh4eMDR0RGTJk2SJlg0R46EhAQMHz4cDg4OqFevHt5//31pXVW3n5+fj+bNm+PIkSMVbvPAgQNo1qwZrK2t0bNnT1y7ds3kGQ4fPoyOHTtCo9GgdevWJTJs3boV9evXh1qtxtChQ5GYmGjyDMX0ej1atWqFxYsXGy03x3FIS0vD6NGjYWtrCx8fH6xatcrsGU6fPo0uXbpAo9GgZcuW+OWXX0yeoTLnIlO9F8rLsHPnTrRs2RIajQadO3fGqVOnjPb9+OOP4enpCY1Gg3HjxiE7O9vkGYqlp6fD09MTmzZtMlpujuMQFRWFwYMHw8bGBo0aNcKOHTtMnqG89n/99Ve0bt0aGo0GnTp1QnBwcJUcg+vXr+OJJ56Ara0tGjdujG3btknrzHV+LC+Duc6P5WUoVtXnx/IymOv8WF4Gc5wfaxxBtcK0adNE+/btxZUrV8Qff/whHBwcxHfffVdl7eXl5YkWLVqIsWPHihs3bojffvtNuLq6itWrV4usrCzh7u4u3nnnHREZGSneeust4eLiIjIzM6skS1BQkJDJZKJXr15CCCEKCwtFq1atxIQJE0RkZKRYtWqVUKlUIjw8vEra//DDD4Wbm5s4fPiwOH/+vGjcuLGYO3eu2XIMHDhQ9OnTR4SGhopjx44JT09PsXHjxipvX6fTieeee04AEIcPHxZC3P/Y37p1S6jVarFmzRoREREhxo0bJ5o1ayYKCwtNluH69etCrVaLFStWiIiICLF69WphaWkpLl68KIQQIjg4WKhUKvHjjz+K69eviwEDBogBAwaY9Djc7b///a8AIBYtWiQtM8dxEEKIQYMGia5du4orV66IX375RahUKrF//36zZcjOzhYuLi5i+vTpIjIyUmzatElYW1uLsLAwk2WozLnIVO+F8jIcO3ZMqFQqsXXrVhERESEWLFgg7OzsRHx8vBBCiJ07dwpHR0dx+PBhcenSJdG2bVsxadIkk2a428svvywAiI0bN0rLzHEcCgoKRKtWrcSwYcPEjRs3xKZNm0z+/7K89iMjI4VKpRLLly8XkZGR4oMPPhBOTk4iOTnZpMegsLBQNGnSRIwbN06Eh4eL/fv3CwcHB3HkyBGznR/Ly2Cu82N5Ge5WlefH+2Uwx/mxvAzmOD/WRCwsagGtVitUKpXRB4olS5aIHj16VFmbx44dE0qlUmi1WmnZe++9J7p16ya++eYbUb9+fWEwGIQQQhgMBtGgQQOxYcMGk+fIy8sTzZs3Fz169JAKi0OHDgm1Wi2ys7Ol7fr06SMWLFhg8vYLCwuFq6ur2Lp1q7Rs69atol+/fmbLoVarxd69e6XHM2bMEMOGDavS9kNCQkSbNm1EmzZtjD5I3q/NhQsXSj8nIYTIyckRNjY24s8//zRZhqVLl4qePXsabdu/f3/xzjvvCCGEGDdunHjxxReldZGRkUImk4mbN2+aLEOxa9euCS8vL9GiRQujX5zmOA4XL14UFhYWIjIyUtp22rRpZv1ZnDlzRgAQWVlZ0rbt2rUTK1euNFmGypyLTPVeKC/DxIkTxbhx44y2b9y4sfj666+FEEL07NnT6L1x9OhRoVQqRU5Ojsky3P3cjRs3lv74UMwcx+Hnn38WDg4ORu+Fp556Sqxbt85kGcprf8eOHcLZ2dloeycnJ7F7926TtS+EEHFxcWL48OFGf0gbNmyYePPNN812fiwvg7nOj+VlKFbV58fyMpjr/FheBnOcH2si3gpVC5w/fx56vR5du3aVlnXr1g1nz56FqKJBvZo2bYq9e/fC2tpaWiaTyZCTk4Pg4GB0795dmqVcJpOha9euVXI71Pvvv4+2bduib9++0rLg4GC0a9cONjY20rJu3bpVSfshISFITk7GU089JS0bM2YMDh48aLYcbdu2xffff4+8vDwkJibi999/h729fZW2f+zYMfTp0wfHjx83Wn6/NoODg9GjRw9pnVqtfuhb5crK8Pzzz+OTTz4xWlb83iwtg5+fH7y9vU2aodjkyZOxZMkSuLi4GC03x3E4cuQI2rZtCz8/P2nZF198gaVLl5otQ8OGDWFra4tNmzZBCIGzZ88iLCxMmqHVFBkqcy4y1XuhvAyvv/465syZU2KfnJwcCCFw5swZowyBgYEoKCjAhQsXTJYBAPLy8jB58mR8+eWXsLKyMtrXHMfhyJEjePzxx6HRaKR1e/bswaRJk0yWobz2W7VqhczMTOzduxcAsG/fPqSlpZX5XnzYY+Dp6Yn//e9/sLW1BQCcOXMGR48erdA52VT/J8vLYK7zY3kZilX1+bG8DOY6P5aXwRznx5qIhUUtEBMTAxcXF6NfFl5eXtDpdEhJSamSNl1dXdG/f3/pcV5eHr755hv07dsXMTEx8Pb2Ntrey8sLsbGxJs0QFhaGtWvX4tNPPzVabq72AeDmzZtwdXVFUFAQ2rZtC39/f8ycORP5+flmy7F161YcP34cGo0Gnp6eKCwsxPz586u0/SlTpuDTTz81+gUJ3P/YmzJTWRkaN26MDh06SI9DQ0Nx+PBhqfg0RwYA2LhxI/Lz8/HSSy+VWGeODDdv3oS/vz8++ugjNGjQAC1btsT69evNmsHJyQk//PADZs+eDZVKhU6dOqFfv3544YUXTJahMuciUx2D8jK0adMGLVq0kNb9+eefuHbtGvr06YPk5GTodDqjDFZWVnB2djZpBgBYtmwZ2rVrh8cff7zEvuY4Djdv3oSvry/mzJkDX19ftG/fXvqQb6oM5bXftGlTrF69GsOGDYNSqcRTTz2Fl156Cb169TLpMbhbQEAAOnfujAYNGuD111836/mxrAzmPD+WlQEw3/mxrAzmPD+WlcEc58eaiIVFLaDT6UpMqlL8ODc3t8rbLywsxJgxY5CSkoJZs2aVmceUWYQQmDx5MhYvXgw3NzejdeZov1h2djays7Px9ttvY8WKFdi4cSP27NmDOXPmmC3HuHHj4O/vj5MnT2L37t1wdHTE7du3zXocit2vTXNnSkpKwrBhw9C+fXsMGTLEbBlu376NefPmYe3atdJfy+9mjgzZ2dk4ePAgTp48iR9//BFvvfUW/u///g+7d+82W4b4+HhMmDABL7/8Ms6cOYPPPvsMOTk5yMzMrJIMD3ouqopjcG+Gu129ehWjR4/G888/j9atW0uDPFR1htDQ0FL/CFPMHMchOzsbmzZtQkZGBn755Re8+OKLePbZZ/H3339XSYZ727906RLeeOMNLF26FGfPnsX8+fORmppape+FnTt34vfff4darcalS5eq5f14b4a7mev8eG+G6jg/3puhOs6P92Yw9/mxpqizM2/XJiqVSvoFVSwvLw8AjC4JVwWDwYCJEydi79692L9/Pzw9PcvMY8os69evh16vly6j302lUiE5OblK2y+mUCig1WqxcuVKaVSq5cuXY/To0Rg/fnyJ0V1MnePUqVM4ceIEYmJi4OnpCQDIysrCmDFjMGjQILMdh2L3O/ZlvTfs7OxMniU1NRX9+/eHTqfD//73P+kXmDnen2+++SbGjx+Pli1blrreHMdBoVAgPz8fW7duhUajQdu2bXH27FmsWbMGw4YNM0uGTZs2wdnZGZ9//jlkMhkee+wxXLp0CXPnzsXGjRtNmuFhzkWmfi+UlqFYREQEHn/8cXh5eeHrr7+W2gdQpRk8PDwwcuRILFmyBO7u7qXuY47joFAo4OjoiDVr1kAul6N169b4888/8dVXX2HdunUmzVBa+4sXL0bPnj3xzjvvAABat26N/v37Y/ny5Vi0aFGVnBfatGkDoKjInzBhAgYPHmz28+O9GcLDwyGTycx6frw3Q9euXc1+frw3w4ABA8x+frw3w6RJk8x2fqxJeMWiFvD29kZKSgr0er20LDY2FiqVCs7OzlXWbmFhIcaOHYsdO3bgxx9/RL9+/aQ88fHxRtvGxsaWuKRXGd999x3OnTsHR0dHODg44IMPPsDx48fh4OAAd3f3Km+/mIeHBwCgSZMm0rKmTZtCp9PBw8OjynNER0fDxcXF6ANM27ZtERERATc3N7Mdh2L3+9mb470BFP0lrlevXsjIyMChQ4eMnt9c788vv/wSDg4OcHBwwPHjx/HBBx+gdevWZsvg4eEBb29vo3vamzZtipiYGLNliI6ORqtWrYz+Ktm2bVucOXPGpBke9lxkymNQVgagaLjJHj16wMXFBX/88Yd0v7WzszOUSqVRhvz8fCQnJ5ssQ1RUFI4fP465c+dK78eoqChMnTpV+iu1OY6Dh4cHGjVqBLn8348VVfF+LKv96Oho6f9fsap4LyYmJkp/9S7WtGlTREZG3vecbI4MycnJZjk/lpfBXOfH8jIoFAqznB/LyxAVFWWW82NNw8KiFmjTpg0UCgVOnjwpLQsKCkKnTp1KvcxoKm+88QZ2796Nffv2Sb+ggKLOh8ePH5c6jgshcOLECQQGBpqs7e+//x6hoaE4f/48zp8/jylTpqBDhw44f/48AgMD8c8//0id0YCi42HK9ou1adMGlpaW+Oeff6RloaGhsLW1Rffu3as8R8OGDZGcnGw0znhYWBisrKzQpUsXsx2HYvc79oGBgQgKCpLW5eTk4Ny5cybNpNPpMHjwYOTm5uL48eNo2LBhiYx3Z4iIiEBsbKxJM0RERODSpUvS+7NDhw6YMmUK9u/fX2qGqjgOnTp1wq1bt5CamiotCw0NRYMGDcyWoWHDhrhy5YrRsrCwMKkgN1WGhz0XmfK9UFaG5ORk9O/fH15eXjhy5IhRR1W5XI5OnToZZThx4gSsrKykv25WNoO3tzciIiJw4cIF6f3o5eWFpUuXSveUm+M4dOrUCZcvXzb6A1h578eHzVBW+w0bNkRISIjRtuW9Fx+2/cjISAwfPhwJCQnSsr/++gtqtRo9evQwy/mxvAw2NjZmOT+Wl+HmzZtmOT+Wl+HJJ580y/mxvAyNGjUyy/mxxqm28ajogUyaNEm0b99eXL58WRw6dEg4ODiI7du3V1l7wcHBAoD44osvxO3bt6Wv1NRUkZGRIVxcXMQ777wjbt26Jd555x3h5uZmNKSaqS1atMhoHotmzZpJY4WvXr1aWFtbGw0rZ0qTJk0SjRo1EidPnhRnz54VjRs3FnPmzDFbju7du4v+/fuLkJAQcezYMeHv7y+mTJlitvZxzzwW5bUZHh4uVCqV+PLLL0VkZKSYMGGCaNmyZaXH5b47wwcffCCUSqU4deqU0Xuz+P13/PhxoVQqxf/+9z9x8+ZNMWjQIDFo0KBKtX9vhnv16tXLaDhFcxyH/Px80bJlSzFkyBARFhYmfvzxR6FWq6Vx2s2RISkpSTg4OIjZs2eLmzdviq1btwqVSiV++OEHk2WozLnIVO+F8jJMmTJFODs7i6tXrxqtKx5O9rvvvhMODg7izz//FFeuXBHt27cXr776qkkz3MvPz89ouFlzHIfU1FTh7u4uXnrpJXH9+nXx1VdfCUtLS3Hp0iWTZSiv/StXrggrKyvx6aefivDwcLFy5UphYWEhTp48adJjUFhYKDp37iyGDBkirl69Kn777Tfh4eEh5s2bZ7bzY3kZzHV+LC/Dvarq/FheBnOdH8vLYI7zY03EwqKWyMrKEv/5z3+EjY2NcHd3Fx999FGVtjdr1iwBoMSXn5+fEKLoBN+mTRthZWUl2rVrJ86ePVulee4uLIQQ4urVq6J79+5CqVSKJk2aiF9//bXK2s7LyxNvvvmmcHJyEra2tuKVV14ROp3ObDlu374tRo0aJZycnIS3t7eYOXOmNI67Odq/9wP1/drcu3evaNy4sVAqlaJXr17ixo0bJs3QoUOHUt+bd4/NvmHDBuHr6ytUKpV46qmnRFJSkkkz3OveX5xCVP1xEEKImJgY8fTTTwtra2vh6ekpPvvsM7NnOH/+vOjRo4fQaDSiSZMm0vwNpspQ2XORKd4L5WVwcXEpdd3d74f33ntPuLm5CRsbGzF+/PgHnsOiIsfhbvcWFkJU/XEQomi+k969ewsrKyvh7+8vfYAyVYb7tX/o0CHRvn17YWNjIx577DGxZ88ekx8DIYrmLnj22WeFvb298Pf3F++9957Q6/VCCPOdH8vKYM7zY3nH4W5VeX4sL4O5zo/lZajq82NNJBOiiiZCICIiIiKiOoN9LIiIiIiIqNJYWBARERERUaWxsCAiIiIiokpjYUFERERERJXGwoKIiIiIiCqNhQUREREREVUaCwsiIiIiIqo0FhZERHXY2bNnMW/ePBQWFj7QfmvXrsULL7xw3+2uXr1aYlnbtm0RGRlZ7n67du3CihUrHihTMb1ej23bthkty83NxYEDB8rdLzw8HJcuXTJatmrVKty6deuhcpTl8OHDOHXqFP766y906NABALB9+3ZkZGRgxYoVWL58uUnbIyIyFxYWRES11Nq1ayGTyaSv5557DsuWLYNGoynx1bNnz1Kfo3HjxggKCsLixYtNni8sLAy9evUyeu6oqCjcuHEDvr6+5e7bsWNHfPnll/jqq6+QkJBg9Drv/dq0aZPRvnq9Hl988QXmzZsnLdu4cSP++9//ltvm1q1bsXbtWulxfn4+Zs2aVWbRtWnTpnJz3fuzKWZhYYGnn34ap06dAgAsWrQIb7/9NoQQiIqKAuetJaLaSlHdAYiI6OGNHj0a3377Lb755hscOHAA+fn5eO655/Dxxx8bbWdpaYm//voLHTt2LPV5Tpw4UeKDt5+f332vLJSnadOmOH36NLp06QIPDw9MmTIFP/30E7Kzs6FQlPz1k5aWBgcHBwCAr68vvv/+e+zZswceHh7Q6/XSdtOmTYO7uzsWLlwIoOiD+t0sLCywZcsWtG/fHhMnToRMJsPixYvx008/QafTQalUQiaTSdsLIVBQUIAtW7Zg/fr1KCgogEKhwPHjx+Hi4oKkpCQkJSUBALy9vaWiaPTo0Rg2bJhR2xs3bsSePXuwe/duo+VWVlYAiq6caLVaTJgwAfHx8cjMzERGRgbefvttpKWl4erVq+jcufODH2wiohqAhQURUS0mk8mgUCggl5d/Adre3h4dOnSAXq/HP//8g/Pnz+PEiRPYsmULJk+ejCeffBIvv/wyhg0bBh8fH8yePbvUD/8Pys/PD1u2bMHNmzdhMBiwbt067NixA0OHDpW2+e233zB9+nSpqCjWsWNHqRC6O4tMJoNcLi8zn7OzM3JycgAUXZEp1r17dwDAuXPn0KZNG2n59u3bMWrUKABA7969AQARERHYuXMnNBoN5s+fDwC4efMmxo4di3fffRdAUbFQXDDczcbGpsRrKZaeno7FixcjKSkJ8fHxsLCwQHBwMIKDg2FhYYGTJ0/i119/lfIA4BUMIqo1eCsUEVEttm3bNshkMrzyyivSsu+//x6NGjVCo0aN0LBhQ7i6ukrrFAoFrl27hp9//hmbNm3CwoULsXbtWgwdOhQdO3bEp59+igULFkAIUeJKwL2K/9J/91dpH4L79euHSZMmYcOGDSgoKMDw4cOxf/9+3LhxAyqVCvv37zcqNBYvXizdQjRlypSHOi6RkZHQ6/X48ssvMXr0aOj1euj1evj5+ZW6fa9evSCEgBACSqUSaWlp2Lp1K3766SccPHgQBw8exMiRI2FpaVluu8nJydBoNGWu9/T0xM8//wyVSoX33nsPTZs2ha+vLyZOnAgfHx9kZGTg8uXL0Ov1aNCgQal9VIiIaioWFkREtdQrr7yC3Nxc9OjRAzt27MD3338PAJgwYQLS09ORnp5e5gfTwsJCPP7441i7di0WLVqEzZs34/Tp0/jiiy8q3P6OHTtgaWlp9LVu3boS2+Xm5iInJwcffvgh1q5dC7lcjh07duDMmTMAgC5duuC1116Ttl+4cCH0ej0WLFjwIIfDSP369WFpaYlp06bhu+++k/JVtCP2okWL0LNnTzRp0kRalp+fD6VSabSdRqMx6kuxbNky7Nixo0Qfi99++016jiFDhmDRokXo1q0bAGDDhg04ffo0li9fDkdHR1y9ehUFBQVISkpCo0aNHvoYEBGZG2+FIiKqpVauXInGjRtDLpdLH5wrQi6X448//pAeL1myRPr33LlzK9z+888/jx9++OG+2/3888/45ZdfEBISIn0wj4mJkf6yP2HChBL5ir8eVnR0NHx8fLB27VocP34cW7duBVBUcJTGYDBAp9NJj3NycvDf//4XTZs2xf79+xEQEIDs7GzY2toa7ZeRkSFdpdHr9fD19YW7uzv69u1rNKpV8dUfKysrBAcHQy6Xw9XVFWvXroWdnR0GDx6McePGYenSpbhw4QLq1auH5s2bV+oYEBGZG89YRES11KVLl6S/wE+YMAGPP/44gKKrETqdDjqdDnl5eUb7FBQUYNmyZfj777+lbSwsLBAXFyfdLqTX60vtO/Cwjhw5gtatW0tFRV5eHi5cuHDf4V8flkqlQqNGjaBSqfD666/j+++/h0qlgkqlQkJCQqkf1oOCgmBtbQ1ra2vk5eVhw4YN6NKlC8aOHYthw4ahoKAA6enpJQoLCwsLKBQKKBQK6arEJ598gl27dgGAtO7uzuJyuRw3btyAhYUFhg8fDgBo1qwZZs2ahYEDB+Knn37C77//Ll3RICKqLVhYEBE9AqZMmYJ33nkHALB+/XrpQ/Ldf6HPzs6GpaUlQkJC0KZNG+kqR2FhIby8vIxuaXr22WdNkis/Px87d+406kOxdu1aNGrUCL/++itu3LhhknbulpycLBVNq1atwqhRo6THOp0OrVu3LrHPvX0sis2fPx9yuRzffvstEhISjPqr3C09PR3Tp0/HvHnz0L9/fzg5OWHZsmUltouMjIRMJkNAQAASExOlW6VatGiBJUuWoEWLFtDpdFi3bh1Gjx5tuoNCRGQGLCyIiB4BnTt3Rp8+fQAAkydPlj4kF38BRf0BhBDw9vbGuXPnpHXFoxE98cQTSEtLK7WfxMP6+uuvERAQIPVVOH36NObPn48PP/wQs2bNwosvvljiqkppDAbDfbdp0aJFifk73nzzTWzfvr3UuT3Onj173+e0sLDAvn37MHr0aISHh8Pb27vENmlpaRg+fDiaNGmCN998ExYWFli3bh2WLVtWYtjZ+vXrIzExEXZ2djhx4gSSk5Nhb29vNKzv008/Da1Wi/bt2983HxFRTcLCgoioFgsNDUViYiKWLl2KLl26PNC+iYmJCA4OhhACX3/9NY4fP47XXnut1FuFCgsLjUZ/MhgMpY4KVbwOAOLj47FkyRIsWrQIQNFs2k888QTef/99DBgwAG+++SY8PDzQr1+/Mq9c6PV6fPjhh9KcFeUJCQlBdna20dfKlSvx/PPPl1ienZ1tNKfH0aNHpasHxYWOXq9Hfn4+/Pz8kJmZicTERAQEBBi1eejQIXTq1Ak2NjbYvXu31Jeia9eu2LBhA1544QV8/PHHKCgokPbJysrCyy+/jJEjR6Jdu3YYMmSINFrVzZs3sXnzZigUivtO6EdEVNOwsCAiqoW2bNmCH374AXv37sXjjz+Ojz/+GEFBQQCAdevWlTr7c0xMjNFzTJs2DSNHjsSTTz4JHx8ffPXVVwgJCUFqamqJ9uzt7Y1ulZo2bVqpo0JZWlpi4sSJAICDBw9i0KBB6Nu3L0aMGIE333wTP/zwgzQClEwmw44dO9CjRw+0adNGmi+iWGpqKjZu3Ihdu3bh2WefRWpqKlJTU5GTk2PUZ8EUevbsKfUvKb4VSgiBwMBAHD58GOvXr0e3bt2gUqmkfUaOHIkhQ4Zg9OjR+Omnn2BtbW30nGPGjMHevXuxdu1atG/fHtnZ2QAAR0dHBAQEQCaToU2bNvjll1/w3Xff4cCBA+jevTtee+01HDlyBCtWrMDChQs5jwUR1R6CiIhqnZSUFPHXX3+VWL5o0SIxadIkodfrS3wV8/b2FufOnSuxb0ZGhlAqlUImk4n333/fJDkNBoMQQog//vhD5OTklLldVFSUCA0NNVp2/vx5sXnzZuk5/vvf/woAwsbGRhw4cKBC7a9Zs0aMGTOm3G2+//570atXL+nx8uXLRXp6uhBCiHfffVc0adJEuLi4iOPHjxvtFxYWJqKjo++bQafTiWPHjgkhhAgNDRU2NjbimWeekX5+p0+fFuvXrxfOzs7i+++/l/YLCQkRHTp0ELGxsRV6rURE1U0mBP8UQkREZC5arRZqtbrE8pycHNjY2FRDIiIi02BhQURERERElcY+FkREREREVGksLIiIiIiIqNJYWBARERERUaWxsCAiIiIiokpjYUFERERERJXGwoKIiIiIiCqNhQUREREREVUaCwsiIiIiIqo0FhZERERERFRpLCyIiIiIiKjS/h9nmSW9ftHmLgAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 创建一个新的DataFrame，按'days_from_now_since_last_order_date'分组并计算每个级别的sku_id数量\n", "days_since_last_order_stats = all_sku_df.groupby('days_from_now_since_last_order_date')['ID'].nunique().reset_index()\n", "days_since_last_order_stats.columns = ['days_since_last_order', 'sku_count']\n", "\n", "# 计算累计百分比\n", "days_since_last_order_stats['cumulative_percentage'] = days_since_last_order_stats['sku_count'].cumsum() / days_since_last_order_stats['sku_count'].sum()\n", "\n", "# 计算累计出现次数\n", "days_since_last_order_stats['cumulative_count'] = days_since_last_order_stats['sku_count'].cumsum()\n", "\n", "# 显示结果\n", "# display(days_since_last_order_stats)\n", "\n", "# 可视化结果\n", "import matplotlib.pyplot as plt\n", "import matplotlib.font_manager as fm\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 创建两个子图\n", "fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(8, 6))\n", "\n", "# 绘制SKU累计百分比图\n", "ax1.plot(days_since_last_order_stats['days_since_last_order'], days_since_last_order_stats['cumulative_percentage'])\n", "ax1.set_xlabel('距离上次订单的天数')\n", "ax1.set_ylabel('SKU累计百分比')\n", "ax1.set_title('SKU最后订单日期分布（累计百分比）')\n", "ax1.grid(True)\n", "ax1.set_xticks(range(0, int(days_since_last_order_stats['days_since_last_order'].max()) + 1, 20))\n", "# 强制y轴从零开始\n", "ax1.set_ylim(bottom=0)\n", "\n", "# 绘制累计出现次数分布图\n", "ax2.plot(days_since_last_order_stats['days_since_last_order'], days_since_last_order_stats['cumulative_count'])\n", "ax2.set_xlabel('距离上次订单的天数')\n", "ax2.set_ylabel('SKU累计出现次数')\n", "ax2.set_title('SKU最后订单日期分布（累计出现次数）')\n", "ax2.grid(True)\n", "ax2.set_xticks(range(0, int(days_since_last_order_stats['days_since_last_order'].max()) + 1, 20))\n", "# 强制y轴从零开始\n", "ax2.set_ylim(bottom=0)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "\n", "# 计算每个ID的有销量天数\n", "sales_days = (\n", "    all_sku_df[\n", "        (all_sku_df[\"quantity\"] > 0)\n", "        & (all_sku_df[\"days_from_now_since_last_order_date\"] < 60)\n", "    ]\n", "    .groupby(\"ID\")[\"date\"]\n", "    .nunique()\n", "    .reset_index()\n", ")\n", "sales_days.columns = [\"ID\", \"sales_days\"]\n", "\n", "# 计算销量天数的统计信息\n", "sales_days_stats = sales_days[\"sales_days\"].value_counts().sort_index().reset_index()\n", "sales_days_stats.columns = [\"sales_days\", \"sku_count\"]\n", "\n", "# 计算累计百分比\n", "sales_days_stats[\"cumulative_percentage\"] = (\n", "    sales_days_stats[\"sku_count\"].cumsum() / sales_days_stats[\"sku_count\"].sum()\n", ")\n", "\n", "# 显示结果\n", "# display(sales_days_stats)\n", "\n", "# 可视化结果\n", "import matplotlib.pyplot as plt\n", "import matplotlib.font_manager as fm\n", "\n", "# 设置中文字体\n", "plt.rcParams[\"font.sans-serif\"] = [\"Arial Unicode MS\"]\n", "plt.rcParams[\"axes.unicode_minus\"] = False\n", "\n", "# 创建图形\n", "fig, ax = plt.subplots(figsize=(8, 4))\n", "\n", "# 绘制SKU累计百分比图\n", "ax.plot(sales_days_stats[\"sales_days\"], sales_days_stats[\"cumulative_percentage\"])\n", "ax.set_xlabel(\"有销量的天数\")\n", "ax.set_ylabel(\"SKU累计百分比\")\n", "ax.set_title(\"SKU有销量天数分布（累计百分比）\")\n", "ax.grid(True)\n", "\n", "# 设置x轴刻度\n", "max_sales_days = sales_days_stats[\"sales_days\"].max()\n", "ax.set_xticks(np.arange(0, max_sales_days + 1, max(1, max_sales_days // 10)))\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import numpy as np\n", "\n", "# 计算每个ID的平均日销量\n", "avg_daily_sales_all = (\n", "    all_sku_df[\n", "        (all_sku_df[\"quantity\"] > 0)\n", "        & (all_sku_df[\"days_from_now_since_last_order_date\"] < 60)\n", "    ]\n", "    .groupby(\"ID\")\n", "    .agg(total_quantity=(\"quantity\", \"sum\"), sales_days=(\"date\", \"nunique\"))\n", "    .reset_index()\n", ")\n", "avg_daily_sales_all[\"avg_daily_sales\"] = np.floor(\n", "    avg_daily_sales_all[\"total_quantity\"] / avg_daily_sales_all[\"sales_days\"]\n", ")\n", "\n", "avg_daily_sales = avg_daily_sales_all[avg_daily_sales_all[\"avg_daily_sales\"] <= 200]\n", "\n", "# 计算平均日销量的统计信息\n", "avg_sales_stats = (\n", "    avg_daily_sales[\"avg_daily_sales\"].value_counts().sort_index().reset_index()\n", ")\n", "avg_sales_stats.columns = [\"avg_daily_sales\", \"sku_count\"]\n", "\n", "# 计算累计百分比\n", "avg_sales_stats[\"cumulative_percentage\"] = (\n", "    avg_sales_stats[\"sku_count\"].cumsum() / avg_sales_stats[\"sku_count\"].sum()\n", ")\n", "\n", "# 显示结果\n", "# display(avg_sales_stats)\n", "\n", "# 可视化结果\n", "import matplotlib.pyplot as plt\n", "import matplotlib.font_manager as fm\n", "\n", "# 设置中文字体\n", "plt.rcParams[\"font.sans-serif\"] = [\"Arial Unicode MS\"]\n", "plt.rcParams[\"axes.unicode_minus\"] = False\n", "\n", "# 创建图形\n", "fig, ax = plt.subplots(figsize=(8, 4))\n", "\n", "# 绘制SKU累计百分比图\n", "ax.plot(avg_sales_stats[\"avg_daily_sales\"], avg_sales_stats[\"cumulative_percentage\"])\n", "ax.set_xlabel(\"平均日销量\")\n", "ax.set_ylabel(\"SKU累计百分比\")\n", "ax.set_title(\"SKU平均日销量分布（累计百分比）\")\n", "ax.grid(True)\n", "\n", "# 设置x轴刻度\n", "max_avg_sales = avg_sales_stats[\"avg_daily_sales\"].max()\n", "ax.set_xticks(np.arange(0, max_avg_sales + 1, max(1, max_avg_sales // 10)))\n", "\n", "# 强制y轴从零开始\n", "ax.set_ylim(bottom=0)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>销量档次</th>\n", "      <th>ID数量</th>\n", "      <th>百分比</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>小于1</td>\n", "      <td>0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>5600</td>\n", "      <td>51.319648</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>1943</td>\n", "      <td>17.806085</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>865</td>\n", "      <td>7.927053</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>454</td>\n", "      <td>4.160557</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>5</td>\n", "      <td>294</td>\n", "      <td>2.694282</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>大于5</td>\n", "      <td>1756</td>\n", "      <td>16.092375</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  销量档次  ID数量        百分比\n", "0  小于1     0   0.000000\n", "1    1  5600  51.319648\n", "2    2  1943  17.806085\n", "3    3   865   7.927053\n", "4    4   454   4.160557\n", "5    5   294   2.694282\n", "6  大于5  1756  16.092375"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 500x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 定义销量档次\n", "sales_ranges = [\n", "    ('小于1', lambda x: x < 1),\n", "    ('1', lambda x: x == 1),\n", "    ('2', lambda x: x == 2),\n", "    ('3', lambda x: x == 3),\n", "    ('4', lambda x: x == 4),\n", "    ('5', lambda x: x == 5),\n", "    ('大于5', lambda x: x > 5)\n", "]\n", "\n", "# 统计各档次的ID数量\n", "sales_stats = pd.DataFrame({\n", "    '销量档次': [r[0] for r in sales_ranges],\n", "    'ID数量': [avg_daily_sales_all[avg_daily_sales_all['avg_daily_sales'].apply(r[1])]['ID'].count() for r in sales_ranges]\n", "})\n", "\n", "# 计算百分比\n", "total_ids = avg_daily_sales_all['ID'].count()\n", "sales_stats['百分比'] = sales_stats['ID数量'] / total_ids * 100\n", "\n", "# 显示结果\n", "display(sales_stats)\n", "\n", "# 可视化结果\n", "plt.figure(figsize=(5, 3))\n", "plt.bar(sales_stats['销量档次'], sales_stats['百分比'])\n", "plt.title('各日均销量档次的ID分布')\n", "plt.xlabel('日均销量档次')\n", "plt.ylabel('百分比 (%)')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>在售天数档次</th>\n", "      <th>ID数量</th>\n", "      <th>百分比</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>7天以下</td>\n", "      <td>15253</td>\n", "      <td>81.479701</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>7-14天</td>\n", "      <td>23325</td>\n", "      <td>124.599359</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>14-30天</td>\n", "      <td>57431</td>\n", "      <td>306.789530</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>30天以上</td>\n", "      <td>1143639</td>\n", "      <td>6109.182692</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   在售天数档次     ID数量          百分比\n", "0    7天以下    15253    81.479701\n", "1   7-14天    23325   124.599359\n", "2  14-30天    57431   306.789530\n", "3   30天以上  1143639  6109.182692"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 定义在售天数档次\n", "onsale_ranges = [\n", "    ('7天以下', lambda x: x < 7),\n", "    ('7-14天', lambda x: 7 <= x < 14),\n", "    ('14-30天', lambda x: 14 <= x < 30),\n", "    ('30天以上', lambda x: x >= 30)\n", "]\n", "\n", "# 统计各档次的ID数量\n", "onsale_stats = pd.DataFrame({\n", "    '在售天数档次': [r[0] for r in onsale_ranges],\n", "    'ID数量': [all_sku_df[all_sku_df['onsale_days'].apply(r[1])]['ID'].count() for r in onsale_ranges]\n", "})\n", "\n", "# 计算百分比\n", "total_ids = len(all_sku_df['ID'].unique())\n", "onsale_stats['百分比'] = onsale_stats['ID数量'] / total_ids * 100\n", "\n", "# 显示结果\n", "display(onsale_stats)\n", "\n", "# 可视化结果\n", "plt.figure(figsize=(8, 4))\n", "plt.bar(onsale_stats['在售天数档次'], onsale_stats['百分比'])\n", "plt.title('各在售天数档次的ID分布')\n", "plt.xlabel('在售天数档次')\n", "plt.ylabel('百分比 (%)')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["datetime64[ns]\n", "销量数据稀疏但平均销量不低的SKU数量: 615\n", "                 ID  weighted_avg_sales  predicted_next_day_sales\n", "0  1001218743130_10                 0.0                       0.0\n", "1  1002116861466_10                 0.0                       0.0\n", "2  1003026205810_69                 0.0                       0.0\n", "3  1003043034124_24                 0.0                       0.0\n", "4  1003043034688_10                 0.0                       0.0\n"]}], "source": ["# 首先检查 date 列的数据类型\n", "print(all_sku_df[\"date\"].dtype)\n", "\n", "# 如果不是日期时间格式，我们需要进行转换\n", "if all_sku_df[\"date\"].dtype != \"datetime64[ns]\":\n", "    all_sku_df[\"date\"] = pd.to_datetime(all_sku_df[\"date\"])\n", "\n", "# 筛选平均销量小于2的SKU\n", "low_sales_skus = avg_daily_sales_all[avg_daily_sales_all[\"avg_daily_sales\"] < 2][\n", "    \"ID\"\n", "].unique()\n", "\n", "# 筛选销量数据很稀疏的SKU（如果有销量的天数/总时间跨度 < 1/7）\n", "low_onsale_days_ratio_skus = all_sku_df[\n", "    (all_sku_df[\"onsale_days_ratio\"] < 1.00 / 7)\n", "    & (all_sku_df[\"days_from_now_since_last_order_date\"] < 60)\n", "][\"ID\"].unique()\n", "\n", "# 提取low_onsale_days_ratio_skus和low_sales_skus的差集\n", "sparse_but_not_low_sales_skus = np.setdiff1d(low_onsale_days_ratio_skus, low_sales_skus)\n", "\n", "print(f\"销量数据稀疏但平均销量不低的SKU数量: {len(sparse_but_not_low_sales_skus)}\")\n", "\n", "# 创建完整的日期范围\n", "date_range = pd.date_range(end=pd.Timestamp.now(), periods=60)\n", "\n", "# 为每个SKU创建完整的日期范围，并填充缺失的日期\n", "complete_df = pd.DataFrame(\n", "    [(sku, date) for sku in low_sales_skus for date in date_range],\n", "    columns=[\"ID\", \"date\"],\n", ")\n", "\n", "# 将完整的日期范围与原始数据合并，填充缺失的销量为0\n", "low_sales_df = complete_df.merge(\n", "    all_sku_df[[\"ID\", \"date\", \"quantity\"]], on=[\"ID\", \"date\"], how=\"left\"\n", ")\n", "low_sales_df[\"quantity\"] = low_sales_df[\"quantity\"].fillna(0)\n", "\n", "# 计算每个日期的权重\n", "current_date = pd.Timestamp.now().date()\n", "low_sales_df[\"days_ago\"] = (current_date - low_sales_df[\"date\"].dt.date).apply(\n", "    lambda x: x.days\n", ")\n", "low_sales_df[\"weight\"] = 61 - low_sales_df[\"days_ago\"]\n", "\n", "# 计算加权平均销量\n", "weighted_sales = (\n", "    low_sales_df.groupby(\"ID\")[[\"quantity\", \"weight\"]]\n", "    .apply(\n", "        lambda x: pd.Series(\n", "            {\n", "                \"weighted_avg_sales\": np.sum(x[\"quantity\"] * x[\"weight\"])\n", "                / np.sum(x[\"weight\"])\n", "            }\n", "        )\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "# 预测下一个日期的销售额\n", "weighted_sales[\"predicted_next_day_sales\"] = np.round(\n", "    weighted_sales[\"weighted_avg_sales\"], 2\n", ")\n", "\n", "# 显示结果\n", "print(weighted_sales.head())"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sku_id</th>\n", "      <th>warehouse_no</th>\n", "      <th>date</th>\n", "      <th>quantity</th>\n", "      <th>ID</th>\n", "      <th>earliest_order_date</th>\n", "      <th>last_order_date</th>\n", "      <th>days_from_now_since_last_order_date</th>\n", "      <th>onsale_days</th>\n", "      <th>onsale_days_ratio</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>5146</th>\n", "      <td>106306</td>\n", "      <td>60</td>\n", "      <td>2023-10-09</td>\n", "      <td>1</td>\n", "      <td>106306_60</td>\n", "      <td>2023-09-24</td>\n", "      <td>2024-10-17</td>\n", "      <td>1</td>\n", "      <td>164</td>\n", "      <td>0.420513</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5149</th>\n", "      <td>106306</td>\n", "      <td>60</td>\n", "      <td>2023-10-15</td>\n", "      <td>1</td>\n", "      <td>106306_60</td>\n", "      <td>2023-09-24</td>\n", "      <td>2024-10-17</td>\n", "      <td>1</td>\n", "      <td>164</td>\n", "      <td>0.420513</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5153</th>\n", "      <td>106306</td>\n", "      <td>60</td>\n", "      <td>2023-10-28</td>\n", "      <td>3</td>\n", "      <td>106306_60</td>\n", "      <td>2023-09-24</td>\n", "      <td>2024-10-17</td>\n", "      <td>1</td>\n", "      <td>164</td>\n", "      <td>0.420513</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5157</th>\n", "      <td>106306</td>\n", "      <td>60</td>\n", "      <td>2023-11-12</td>\n", "      <td>1</td>\n", "      <td>106306_60</td>\n", "      <td>2023-09-24</td>\n", "      <td>2024-10-17</td>\n", "      <td>1</td>\n", "      <td>164</td>\n", "      <td>0.420513</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5171</th>\n", "      <td>106306</td>\n", "      <td>60</td>\n", "      <td>2023-12-10</td>\n", "      <td>1</td>\n", "      <td>106306_60</td>\n", "      <td>2023-09-24</td>\n", "      <td>2024-10-17</td>\n", "      <td>1</td>\n", "      <td>164</td>\n", "      <td>0.420513</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1090335</th>\n", "      <td>106306</td>\n", "      <td>60</td>\n", "      <td>2024-05-26</td>\n", "      <td>1</td>\n", "      <td>106306_60</td>\n", "      <td>2023-09-24</td>\n", "      <td>2024-10-17</td>\n", "      <td>1</td>\n", "      <td>164</td>\n", "      <td>0.420513</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1090357</th>\n", "      <td>106306</td>\n", "      <td>60</td>\n", "      <td>2024-06-27</td>\n", "      <td>2</td>\n", "      <td>106306_60</td>\n", "      <td>2023-09-24</td>\n", "      <td>2024-10-17</td>\n", "      <td>1</td>\n", "      <td>164</td>\n", "      <td>0.420513</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1090367</th>\n", "      <td>106306</td>\n", "      <td>60</td>\n", "      <td>2024-07-15</td>\n", "      <td>1</td>\n", "      <td>106306_60</td>\n", "      <td>2023-09-24</td>\n", "      <td>2024-10-17</td>\n", "      <td>1</td>\n", "      <td>164</td>\n", "      <td>0.420513</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1090370</th>\n", "      <td>106306</td>\n", "      <td>60</td>\n", "      <td>2024-07-17</td>\n", "      <td>2</td>\n", "      <td>106306_60</td>\n", "      <td>2023-09-24</td>\n", "      <td>2024-10-17</td>\n", "      <td>1</td>\n", "      <td>164</td>\n", "      <td>0.420513</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1090449</th>\n", "      <td>106306</td>\n", "      <td>60</td>\n", "      <td>2024-10-16</td>\n", "      <td>1</td>\n", "      <td>106306_60</td>\n", "      <td>2023-09-24</td>\n", "      <td>2024-10-17</td>\n", "      <td>1</td>\n", "      <td>164</td>\n", "      <td>0.420513</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>164 rows × 10 columns</p>\n", "</div>"], "text/plain": ["         sku_id  warehouse_no       date  quantity         ID  \\\n", "5146     106306            60 2023-10-09         1  106306_60   \n", "5149     106306            60 2023-10-15         1  106306_60   \n", "5153     106306            60 2023-10-28         3  106306_60   \n", "5157     106306            60 2023-11-12         1  106306_60   \n", "5171     106306            60 2023-12-10         1  106306_60   \n", "...         ...           ...        ...       ...        ...   \n", "1090335  106306            60 2024-05-26         1  106306_60   \n", "1090357  106306            60 2024-06-27         2  106306_60   \n", "1090367  106306            60 2024-07-15         1  106306_60   \n", "1090370  106306            60 2024-07-17         2  106306_60   \n", "1090449  106306            60 2024-10-16         1  106306_60   \n", "\n", "        earliest_order_date last_order_date  \\\n", "5146             2023-09-24      2024-10-17   \n", "5149             2023-09-24      2024-10-17   \n", "5153             2023-09-24      2024-10-17   \n", "5157             2023-09-24      2024-10-17   \n", "5171             2023-09-24      2024-10-17   \n", "...                     ...             ...   \n", "1090335          2023-09-24      2024-10-17   \n", "1090357          2023-09-24      2024-10-17   \n", "1090367          2023-09-24      2024-10-17   \n", "1090370          2023-09-24      2024-10-17   \n", "1090449          2023-09-24      2024-10-17   \n", "\n", "         days_from_now_since_last_order_date  onsale_days  onsale_days_ratio  \n", "5146                                       1          164           0.420513  \n", "5149                                       1          164           0.420513  \n", "5153                                       1          164           0.420513  \n", "5157                                       1          164           0.420513  \n", "5171                                       1          164           0.420513  \n", "...                                      ...          ...                ...  \n", "1090335                                    1          164           0.420513  \n", "1090357                                    1          164           0.420513  \n", "1090367                                    1          164           0.420513  \n", "1090370                                    1          164           0.420513  \n", "1090449                                    1          164           0.420513  \n", "\n", "[164 rows x 10 columns]"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["df=all_sku_df[all_sku_df[\"ID\"]=='106306_60'].sort_values(by=['date'], ascending=False)\n", "df.head(20)"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th>total_quantity</th>\n", "      <th>sales_days</th>\n", "      <th>avg_daily_sales</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>379</th>\n", "      <td>106306_60</td>\n", "      <td>239</td>\n", "      <td>164</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            ID  total_quantity  sales_days  avg_daily_sales\n", "379  106306_60             239         164              1.0"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["avg_daily_sales_all[avg_daily_sales_all['ID']=='106306_60']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 首先检查 date 列的数据类型\n", "print(all_sku_df[\"date\"].dtype)\n", "\n", "# 如果不是日期时间格式，我们需要进行转换\n", "if all_sku_df[\"date\"].dtype != \"datetime64[ns]\":\n", "    all_sku_df[\"date\"] = pd.to_datetime(all_sku_df[\"date\"])\n", "\n", "# 筛选平均销量小于2的SKU\n", "low_sales_skus = avg_daily_sales_all[avg_daily_sales_all[\"avg_daily_sales\"] < 2][\n", "    \"ID\"\n", "].unique()\n", "\n", "# 筛选销量数据很稀疏的SKU（如果有销量的天数/总时间跨度 < 1/7）\n", "low_onsale_days_ratio_skus = all_sku_df[\n", "    (all_sku_df[\"onsale_days_ratio\"] < 1.00 / 7)\n", "    & (all_sku_df[\"days_from_now_since_last_order_date\"] < 60)\n", "][\"ID\"].unique()\n", "\n", "# 提取low_onsale_days_ratio_skus和low_sales_skus的差集\n", "sparse_but_not_low_sales_skus = np.setdiff1d(low_onsale_days_ratio_skus, low_sales_skus)\n", "\n", "print(f\"销量数据稀疏但平均销量不低的SKU数量: {len(sparse_but_not_low_sales_skus)}\")\n", "\n", "# 创建完整的日期范围（最近9周）\n", "end_date = pd.Timestamp.now().date()\n", "start_date = end_date - pd.<PERSON><PERSON><PERSON>(days=63)  # 9周 * 7天\n", "date_range = pd.date_range(start=start_date, end=end_date)\n", "\n", "# 为每个SKU创建完整的日期范围，并填充缺失的日期\n", "complete_df = pd.DataFrame(\n", "    [(sku, date) for sku in low_sales_skus for date in date_range],\n", "    columns=[\"ID\", \"date\"],\n", ")\n", "\n", "# 将完整的日期范围与原始数据合并，填充缺失的销量为0\n", "low_sales_df = complete_df.merge(\n", "    all_sku_df[[\"ID\", \"date\", \"quantity\"]], on=[\"ID\", \"date\"], how=\"left\"\n", ")\n", "low_sales_df[\"quantity\"] = low_sales_df[\"quantity\"].fillna(0)\n", "\n", "# 计算每周的总销量\n", "low_sales_df[\"week\"] = (end_date - low_sales_df[\"date\"].dt.date).apply(\n", "    lambda x: x.days\n", ") // 7\n", "weekly_sales = low_sales_df.groupby([\"ID\", \"week\"])[\"quantity\"].sum().reset_index()\n", "\n", "# 计算每周的权重\n", "weekly_sales[\"weight\"] = 10 - weekly_sales[\"week\"]  # 最近的周权重为9，最远的周权重为1\n", "\n", "# 计算加权平均周销量\n", "weighted_sales = (\n", "    weekly_sales.groupby(\"ID\")\n", "    .apply(\n", "        lambda x: pd.Series(\n", "            {\n", "                \"weighted_avg_weekly_sales\": np.sum(x[\"quantity\"] * x[\"weight\"])\n", "                / np.sum(x[\"weight\"])\n", "            }\n", "        )\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "# 预测未来7天的销售额\n", "weighted_sales[\"predicted_next_week_sales\"] = np.round(\n", "    weighted_sales[\"weighted_avg_weekly_sales\"], 2\n", ")\n", "\n", "# 新增一列：predicted_next_week_sales_cnt，使用predicted_next_week_sales的向上取整整数值\n", "weighted_sales[\"predicted_next_week_sales_cnt\"] = np.ceil(\n", "    weighted_sales[\"predicted_next_week_sales\"]\n", ").astype(int)\n", "\n", "# 显示结果\n", "print(weighted_sales.head())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "np", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 2}