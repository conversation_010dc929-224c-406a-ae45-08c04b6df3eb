from multiprocessing import Value, Lock

from sympy import false
from feishu_client import send_markdown_to_feishu
from neuralprophet import NeuralProphet, set_log_level
from odps_client import (
    get_odps_sql_result_as_df,
    write_pandas_df_into_odps,
    logging,
    create_directory_if_not_exists,
)
from typing import Union
import multiprocessing
import pandas as pd
from datetime import date, timedelta, datetime
import numpy as np
import traceback
import warnings
import os
import time
import psutil
import torch
import json
import sys

import argparse

parser = argparse.ArgumentParser()
parser.add_argument(
    "--period", default="week", choices=["week", "day"], help="预测周期, week/day"
)
parser.add_argument(
    "--num_of_days", type=int, default=14, help="预测周期为‘day’时的预测天数，默认14天"
)
parser.add_argument(
    "--num_of_weeks",
    type=int,
    default=10,
    help="预测周期为‘week’时的预测周数，默认10周",
)
parser.add_argument(
    "--total_batch_no",
    type=int,
    default=10,
    help="分批次训练和预测，释放子进程所占有的内存资源",
)
parser.add_argument(
    "--feishu_token",
    type=str,
    default="null",
    help="发送飞书通知所用的token，默认不需要发送",
)
parser.add_argument("--epochs", type=int, default=300, help="最大训练轮数")
parser.add_argument(
    "--batch_timeout_minutes",
    type=int,
    default=10,
    help="每个批次的超时时间(单位:分钟)，默认10分钟",
)
parser.add_argument(
    "--max_wait_hour", type=int, default=5, help="等待数据ready的最大小时数"
)
parser.add_argument(
    "--last_order_date_threshold",
    type=int,
    default=60,
    help="最后订单日期阈值,超过该阈值的SKU将不需要预测，直接返回0",
)
parser.add_argument(
    "--use_mps",
    choices=["true", "false"],
    default="true",
    help="是否使用mps(macOS) true/false",
)
parser.add_argument(
    "--early_stopping",
    choices=["true", "false"],
    default="true",
    help="是否提前结束",
)

args = parser.parse_args()
logging.info(f"parsed args:{args}")
period = args.period
epochs_number = args.epochs
use_mps = "true" == args.use_mps
last_order_date_threshold = args.last_order_date_threshold
num_of_days = args.num_of_days
num_of_weeks = args.num_of_weeks
early_stopping = "true" == args.early_stopping
total_batch_no = args.total_batch_no
max_wait_hour = args.max_wait_hour
batch_timeout_minutes = args.batch_timeout_minutes
feishu_token = args.feishu_token


if period not in ["week", "day"]:
    raise ValueError("period 必须是 'week' 或 'day'")

date_of_now = datetime.now().strftime("%Y%m%d")

create_directory_if_not_exists("lightning_logs")
failed_csv_path = create_directory_if_not_exists(f"./np_output/训练失败_{date_of_now}")
prediction_csv_path = create_directory_if_not_exists(
    f"./np_output/prediction_{period}_{date_of_now}"
)
metrics_csv_path = create_directory_if_not_exists(
    f"./np_output/metrics_{period}_{date_of_now}"
)
low_sales_prediction_csv_path = create_directory_if_not_exists(
    f"./np_output/low_sales_prediction_{period}_{date_of_now}"
)


warnings.filterwarnings("ignore")
set_log_level("ERROR")

multiprocessing_level = int(os.getenv("multiprocessing_level", "1"))

max_workers = multiprocessing.cpu_count() * multiprocessing_level

is_mps_available = torch.backends.mps.is_available() and use_mps
logging.info(f"is mps available:{is_mps_available}, use_mps:{use_mps}")

start_time = datetime.now()

logging.info(f"任务开始了:{start_time}")

ds_one_year_ago = (datetime.now() - timedelta(days=13 * 30)).strftime("%Y%m%d")
ds_yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y%m%d")


def is_data_ready(ds: str):
    q = """SELECT  ds,COUNT(*) as cnt
FROM    summerfarm_tech.app_estimated_sku_warehouse_record_df
WHERE   ds = MAX_PT('summerfarm_tech.app_estimated_sku_warehouse_record_df')
GROUP BY ds
;"""
    df = get_odps_sql_result_as_df(q)
    if df is None or len(df) <= 0:
        return False
    elif df.iloc[0]["ds"] == ds:
        return True
    else:
        logging.error(
            f"summerfarm_tech.app_estimated_sku_warehouse_record_df的数据还未就绪:{ds}, {df.to_string()}"
        )
        return False


def log_cpu_usage():
    # CPU使用率百分比
    cpu_usage = psutil.cpu_percent(interval=1)

    # CPU负载平均值（过去1、5和15分钟，类似Linux的'uptime'命令）
    cpu_load = psutil.getloadavg()

    # 运行中的进程总数
    process_count = len(psutil.pids())

    # 内存使用情况
    memory = psutil.virtual_memory()
    memory_usage = memory.percent
    memory_available = memory.available / (1024 * 1024 * 1024)  # 转换为GB

    # 交换内存使用情况
    swap = psutil.swap_memory()
    swap_usage = swap.percent
    swap_total = swap.total / (1024 * 1024 * 1024)  # 转换为GB

    # 输出CPU使用率、负载、内存和交换内存信息
    logging.info(
        f"性能监控: CPU利用率: {cpu_usage}%, CPU负载(1, 5, 15分钟): {cpu_load}, 活跃进程数: {process_count}, "
        f"可用内存: {memory_available:.2f} GB, 内存使用率: {memory_usage}%, "
        f"总交换内存: {swap_total:.2f} GB, 交换内存使用率: {swap_usage}%, "
    )


def load_data(batch_index=0, total_batch_no=1, total_groups=max_workers):
    testing_query = ""
    batching_query = ""
    if "true" == os.getenv("LOCAL_TESTING", "false"):
        # 测试时使用：
        testing_query = """AND     sku_id in ('N001S01R005','N001S01R002','N001H01Y003','L001S01R001','3816076886','464633265','1233884862',
        '607164503701','15103217314','60553221161','D009H20T012','605874603137','T001S01H001','605353800833','17320871038',
        '605455167145','N001S01R004','15174258876','106306','15300532428','16816572100','1001218743130','1002116861466','1003043034688')"""

    if total_batch_no > 1:
        batching_query = (
            f"AND abs(hash(sku_id, 'sku_batching')) % {total_batch_no} = {batch_index}"
        )

    query = f"""
    SELECT  a.sku_id, a.warehouse_no, a.date, a.quantity
    FROM    summerfarm_tech.app_estimated_sku_warehouse_record_df a
    INNER JOIN summerfarm_tech.ods_inventory_df b on b.ds=max_pt('summerfarm_tech.ods_inventory_df') and a.sku_id = b.sku
    WHERE   a.ds = MAX_PT('summerfarm_tech.app_estimated_sku_warehouse_record_df')
    AND     b.sub_type NOT IN (4,5) -- 4:代仓, 5:鲜果POP品 这两种商品均不需要预测服务
    AND     b.outdated != 1 -- 1:代表已删除
    AND     a.date >= '{ds_one_year_ago}'
    {batching_query}
    {testing_query}
    """

    sales_records_df = get_odps_sql_result_as_df(sql=query)

    sales_records_df.rename(columns={"date": "ds", "quantity": "y"}, inplace=True)
    sales_records_df["sku_id"] = sales_records_df["sku_id"].astype(str)
    sales_records_df["warehouse_no"] = sales_records_df["warehouse_no"].astype(str)
    sales_records_df["ID"] = (
        sales_records_df["sku_id"] + "_" + sales_records_df["warehouse_no"]
    )

    sales_records_df["ds"] = pd.to_datetime(sales_records_df["ds"], format="%Y%m%d")
    logging.info(
        f"从ODPS获取数据成功，total_groups:{total_groups}, 数据条数:{len(sales_records_df)}"
    )

    logging.info(
        f"将那些最后一次下单日距今已经超过:{last_order_date_threshold}天的SKU单独返回"
    )

    # 为每个ID创建earliest_order_date和last_order_date列
    sales_records_df["ds"] = pd.to_datetime(sales_records_df["ds"])
    sales_records_df["last_order_date"] = sales_records_df.groupby("ID")[
        "ds"
    ].transform("max")
    sales_records_df["earliest_order_date"] = sales_records_df.groupby("ID")[
        "ds"
    ].transform("min")

    # 计算最近7天的销量,单独存储在一个新的DataFrame中
    yesterday = datetime.now().date() - timedelta(days=1)
    last_7_days = yesterday - timedelta(days=7)
    last_7d_sales_df = (
        sales_records_df[sales_records_df["ds"].dt.date > last_7_days]
        .groupby(["sku_id", "warehouse_no"])["y"]
        .sum()
        .reset_index()
    )
    last_7d_sales_df.columns = ["sku_id", "warehouse_no", "last_7d_sales_quantity"]

    # 创建days_from_now_since_last_order_date列
    current_date = datetime.now().date()

    # 确保 last_order_date 列是日期类型
    sales_records_df["last_order_date"] = pd.to_datetime(
        sales_records_df["last_order_date"]
    ).dt.date

    # 创建 days_from_now_since_last_order_date 列
    sales_records_df["days_from_now_since_last_order_date"] = (
        current_date - sales_records_df["last_order_date"]
    ).apply(lambda x: x.days)

    # 构建onsale_days字段
    sales_records_df["onsale_days"] = sales_records_df.groupby("ID")["ds"].transform(
        "count"
    )

    # 构建onsale_days_ratio字段
    sales_records_df["onsale_days_ratio"] = sales_records_df["onsale_days"] / (
        (
            pd.to_datetime(sales_records_df["last_order_date"])
            - pd.to_datetime(sales_records_df["earliest_order_date"])
        ).dt.days
        + 1
    )

    # 删除 last_order_date 列
    sales_records_df = sales_records_df.drop(columns=["last_order_date"])

    logging.info(
        f"days_from_now_since_last_order_date quantile:\n{sales_records_df['days_from_now_since_last_order_date'].quantile([0.2, 0.5, 0.75, 0.8, 0.9, 0.95])}"
    )

    logging.info(f"warehouses list: {sales_records_df['warehouse_no'].unique()}")
    sku_list_without_order_for_long_time = sales_records_df[
        sales_records_df["days_from_now_since_last_order_date"]
        >= last_order_date_threshold
    ]["ID"].unique()
    logging.info(
        f">>>>以下SKU很久时间(超过{last_order_date_threshold}天)以来都没有订单了:\n{','.join(sku_list_without_order_for_long_time)}"
    )
    return (
        sales_records_df,
        sku_list_without_order_for_long_time,
        last_7d_sales_df,
    )


def generate_weighted_average_forecasting_for_low_sales_sku(
    all_sku_df: pd.DataFrame, end_date: datetime
) -> pd.DataFrame:
    # 计算每个ID的平均日销量
    avg_daily_sales_all = (
        all_sku_df[
            (all_sku_df["y"] > 0)
            & (all_sku_df["days_from_now_since_last_order_date"] < 60)
        ]
        .groupby("ID")
        .agg(total_quantity=("y", "sum"), sales_days=("ds", "nunique"))
        .reset_index()
    )
    avg_daily_sales_all["avg_daily_sales"] = np.floor(
        avg_daily_sales_all["total_quantity"] / avg_daily_sales_all["sales_days"]
    )

    # 筛选平均销量小于2的SKU
    low_sales_skus = avg_daily_sales_all[avg_daily_sales_all["avg_daily_sales"] < 2][
        "ID"
    ].unique()
    logging.info(f"为以下平均销量小于2的SKU使用线形方法预测:{low_sales_skus}")

    # 筛选销量数据很稀疏的SKU（如果有销量的天数/总时间跨度 < 1/7）
    low_onsale_days_ratio_skus = all_sku_df[
        (all_sku_df["onsale_days_ratio"] < 1.00 / 7)
        & (all_sku_df["days_from_now_since_last_order_date"] < 60)
    ]["ID"].unique()

    logging.info(
        f"为以下销量数据稀疏的SKU使用线形方法预测:{low_onsale_days_ratio_skus}"
    )

    # 合并并去重 low_sales_skus 和 low_onsale_days_ratio_skus
    all_low_sales_skus = np.unique(
        np.concatenate([low_sales_skus, low_onsale_days_ratio_skus])
    )

    weighted_sales = pd.DataFrame()

    if period == "week":
        weighted_sales = generate_weekly_weighted_result(
            all_sku_df=all_sku_df, all_low_sales_skus=all_low_sales_skus
        )
    elif period == "day":
        weighted_sales = generate_daily_weighted_result(
            all_sku_df=all_sku_df, all_low_sales_skus=all_low_sales_skus
        )
    else:
        logging.error(f"不合法的period参数:{period}")
        exit(-1)

    if len(weighted_sales) > 0:
        weighted_sales.to_csv(
            f"{low_sales_prediction_csv_path}/low_sales_sku_weighted_sales.csv",
            index=False,
        )

    # 创建未来N天或N段时间的预测数据DataFrame
    future_dates = pd.date_range(
        start=end_date + pd.Timedelta(days=1),
        periods=num_of_weeks if period == "week" else num_of_days,
        freq="W" if period == "week" else "D",
    )

    future_predictions = pd.DataFrame(
        [(id, date) for id in weighted_sales["ID"] for date in future_dates],
        columns=["ID", "view_date"],
    )

    future_predictions = future_predictions.merge(
        weighted_sales[["ID", "predicted_next_period_sales_cnt"]], on="ID", how="left"
    )

    future_predictions = future_predictions.rename(
        columns={"predicted_next_period_sales_cnt": "estimated_sales_cnt"}
    )

    future_predictions["estimated_sales"] = future_predictions[
        "estimated_sales_cnt"
    ].astype(float)
    future_predictions["estimated_sales_lower"] = future_predictions["estimated_sales"]
    future_predictions["estimated_sales_upper"] = future_predictions["estimated_sales"]
    future_predictions["estimated_sales_lower_cnt"] = future_predictions[
        "estimated_sales_cnt"
    ]
    future_predictions["estimated_sales_upper_cnt"] = future_predictions[
        "estimated_sales_cnt"
    ]
    future_predictions = future_predictions.merge(
        avg_daily_sales_all[["ID", "sales_days"]], on="ID", how="left"
    )
    future_predictions["on_sale_days"] = future_predictions["sales_days"]
    future_predictions.drop("sales_days", axis=1, inplace=True)
    future_predictions["type"] = period

    future_predictions["sku_id"] = future_predictions["ID"].str.split("_").str[0]
    future_predictions["warehouse_no"] = future_predictions["ID"].str.split("_").str[1]

    future_predictions = future_predictions[
        [
            "view_date",
            "sku_id",
            "warehouse_no",
            "estimated_sales",
            "estimated_sales_lower",
            "estimated_sales_upper",
            "estimated_sales_cnt",
            "estimated_sales_lower_cnt",
            "estimated_sales_upper_cnt",
            "on_sale_days",
            "type",
        ]
    ]

    return future_predictions, all_low_sales_skus


def generate_daily_weighted_result(
    all_sku_df: pd.DataFrame, all_low_sales_skus: np.ndarray
) -> pd.DataFrame:
    # 创建完整的日期范围
    date_range = pd.date_range(end=pd.Timestamp.now(), periods=60)

    # 为每个SKU创建完整的日期范围，并填充缺失的日期
    complete_df = pd.DataFrame(
        [(sku, date) for sku in all_low_sales_skus for date in date_range],
        columns=["ID", "ds"],
    )

    # 将完整的日期范围与原始数据合并，填充缺失的销量为0
    low_sales_df = complete_df.merge(
        all_sku_df[["ID", "ds", "y"]], on=["ID", "ds"], how="left"
    )
    low_sales_df["y"] = low_sales_df["y"].fillna(0)

    # 计算每个日期的权重
    current_date = pd.Timestamp.now().date()
    low_sales_df["days_ago"] = (current_date - low_sales_df["ds"].dt.date).apply(
        lambda x: x.days
    )
    low_sales_df["weight"] = 61 - low_sales_df["days_ago"]

    # 计算加权平均销量
    weighted_sales = (
        low_sales_df.groupby("ID")[["y", "weight"]]
        .apply(
            lambda x: pd.Series(
                {
                    "weighted_avg_sales": np.sum(x["y"] * x["weight"])
                    / np.sum(x["weight"])
                }
            )
        )
        .reset_index()
    )

    # 预测下一个日期的销售额
    weighted_sales["predicted_next_period_sales"] = np.round(
        weighted_sales["weighted_avg_sales"], 2
    )

    # 新增一列：predicted_next_day_sales_cnt，使用predicted_next_day_sales的向上取整整数值
    weighted_sales["predicted_next_period_sales_cnt"] = np.ceil(
        weighted_sales["weighted_avg_sales"]
    ).astype(int)

    return weighted_sales


def generate_weekly_weighted_result(
    all_sku_df: pd.DataFrame, all_low_sales_skus: np.ndarray
) -> pd.DataFrame:
    # 创建完整的日期范围（最近9周）
    end_date = pd.Timestamp.now().date()
    start_date = end_date - pd.Timedelta(days=63)  # 9周 * 7天
    date_range = pd.date_range(start=start_date, end=end_date)

    # 为每个SKU创建完整的日期范围，并填充缺失的日期
    complete_df = pd.DataFrame(
        [(sku, date) for sku in all_low_sales_skus for date in date_range],
        columns=["ID", "ds"],
    )

    # 将完整的日期范围与原始数据合并，填充缺失的销量为0
    low_sales_df = complete_df.merge(
        all_sku_df[["ID", "ds", "y"]], on=["ID", "ds"], how="left"
    )
    low_sales_df["y"] = low_sales_df["y"].fillna(0)

    # 计算每周的总销量
    low_sales_df["week"] = (end_date - low_sales_df["ds"].dt.date).apply(
        lambda x: x.days
    ) // 7
    weekly_sales = low_sales_df.groupby(["ID", "week"])["y"].sum().reset_index()

    # 计算每周的权重
    weekly_sales["weight"] = (
        10 - weekly_sales["week"]
    )  # 最近的周权重为9，最远的周权重为1

    # 计算加权平均周销量
    weighted_sales = (
        weekly_sales.groupby("ID")
        .apply(
            lambda x: pd.Series(
                {
                    "weighted_avg_weekly_sales": np.sum(x["y"] * x["weight"])
                    / np.sum(x["weight"])
                }
            )
        )
        .reset_index()
    )

    # 预测未来7天的销售额
    weighted_sales["predicted_next_period_sales"] = np.round(
        weighted_sales["weighted_avg_weekly_sales"], 2
    )
    # 新增一列：predicted_next_week_sales_cnt，使用predicted_next_week_sales的向上取整整数值
    weighted_sales["predicted_next_period_sales_cnt"] = np.ceil(
        weighted_sales["weighted_avg_weekly_sales"]
    ).astype(int)

    return weighted_sales


def predict_period(
    dataframe: pd.DataFrame,
    num_periods,
    quantiles,
    train_end_date,
) -> Union[pd.DataFrame, pd.DataFrame]:
    df = dataframe.copy()

    sku_id = df.iloc[0]['ID'].split('_')[0]

    # 按ID分组并填充每个时间序列的缺失日期
    df_list = []
    for id, group in df.groupby("ID"):
        start_date = group["ds"].min()
        date_range = pd.date_range(start=start_date, end=datetime.today())
        filled_group = (
            group.set_index("ds").reindex(date_range, fill_value=0).reset_index()
        )
        filled_group["ID"] = id
        df_list.append(filled_group)

    df = pd.concat(df_list, ignore_index=True)

    # 确保'ds'列存在，如果必要的话将'index'重命名为'ds'
    if "ds" not in df.columns and "index" in df.columns:
        df.rename(columns={"index": "ds"}, inplace=True)

    # 如果需要，处理每周聚合
    if period == "week":
        week_end_day = train_end_date.weekday()
        offset = pd.offsets.Week(weekday=week_end_day)
        df = df.groupby(["ID", pd.Grouper(key="ds", freq=offset)]).sum().reset_index()

    df_train = df[df["ds"] <= train_end_date]

    m = NeuralProphet(
        epochs=epochs_number,
        quantiles=quantiles,
        loss_func="MSE",
        trainer_config={
            "accelerator": "mps" if is_mps_available else "cpu",
            "min_epochs": int(epochs_number / 2),  # 强制至少训练epochs_number/2轮
        },
    )
    m = m.add_country_holidays("CN", lower_window=1, upper_window=3)
    metrics_df = m.fit(
        df=df_train[["ds", "y", "ID"]],
        early_stopping=early_stopping,
        batch_size=128,
    )

    # 记录提前停止的训练
    if len(metrics_df) < epochs_number:
        logging.warning(
            f"SKU:{sku_id}训练在第{len(metrics_df)}轮提前停止,总轮数:{epochs_number}, "
            f"最终loss:{metrics_df.iloc[-1]['Loss']:.4f}, "
            f"最佳loss:{metrics_df['Loss'].min():.4f}"
        )

    # Convert the last 5 rows to a JSON string
    json_string = metrics_df.tail(5).to_json(orient='records', lines=False)
    logging.info(f"SKU:{sku_id} fit finished:{json_string}")

    sku_list = df["ID"].unique()

    # 创建新的sku_metrics_df
    last_epoch_metrics = metrics_df.iloc[-1].to_dict()
    sku_metrics_df = pd.DataFrame(
        {
            "ID": sku_list,
            **{k: [v] * len(sku_list) for k, v in last_epoch_metrics.items()},
        }
    )

    # 计算每个ID的y的统计量并添加到sku_metrics_df
    y_stats = df_train.groupby("ID")["y"].agg(
        ["mean", "median", lambda x: x.quantile(0.25), lambda x: x.quantile(0.75)]
    )
    y_stats.columns = ["y_avg", "y_median", "y_25_quantile", "y_75_quantile"]
    sku_metrics_df = sku_metrics_df.merge(
        y_stats, left_on="ID", right_index=True, how="left"
    )

    # 添加新列 sku_id 和 warehouse_no
    sku_metrics_df["sku_id"] = sku_metrics_df["ID"].apply(lambda x: x.split("_")[0])
    sku_metrics_df["warehouse_no"] = sku_metrics_df["ID"].apply(
        lambda x: x.split("_")[1]
    )
    sku_metrics_df["training_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    sku_metrics_df["period"] = period

    logging.info(f"{sku_id} 的训练metrics:\n{sku_metrics_df}")

    df_future = m.make_future_dataframe(
        df_train[["ds", "y", "ID"]], n_historic_predictions=True, periods=num_periods
    )
    forecast = m.predict(df_future)

    forecasted = forecast[forecast["ds"] > train_end_date].copy()
    forecasted.reset_index(drop=True, inplace=True)
    forecasted.drop(columns=["y"], inplace=True)

    # 负数转成0
    forecasted.loc[forecasted["yhat1"] < 0, "yhat1"] = 0
    forecasted.loc[forecasted["yhat1 5.0%"] < 0, "yhat1 5.0%"] = 0
    forecasted.loc[forecasted["yhat1 95.0%"] < 0, "yhat1 95.0%"] = 0

    # 四舍五入
    forecasted["estimated_sales_cnt"] = forecasted["yhat1"].apply(lambda x: round(x))
    forecasted["estimated_sales_lower_cnt"] = forecasted["yhat1 5.0%"].apply(
        lambda x: round(x)
    )
    forecasted["estimated_sales_upper_cnt"] = forecasted["yhat1 95.0%"].apply(
        lambda x: round(x)
    )

    forecasted["type"] = period

    return forecasted, sku_metrics_df


def read_all_csv_into_single_df(csv_path: str) -> pd.DataFrame:
    import glob

    # Get a list of all CSV files in the directory
    csv_files = glob.glob(os.path.join(csv_path, "*.csv"))

    # Read each CSV file into a DataFrame and concatenate them into a single DataFrame
    df = pd.concat([pd.read_csv(file) for file in csv_files], ignore_index=True)

    logging.info(f"read {len(df)} rows of data from CSV:{csv_path}")
    return df


def generate_zero_forecast_result(
    has_no_order_df: pd.DataFrame, ds_values: list
) -> list:
    zero_forecast_list = []
    logging.info(f"处理长时间无订单的序列...{has_no_order_df['ID'].unique()}")
    for sku_id_and_warehouse_no, group_df in has_no_order_df.groupby("ID"):
        if ds_values is None or len(ds_values) <= 0:
            logging.warning(f"predictions_list为空，使用一个默认的日期范围")
            ds_values = pd.date_range(
                start=yesterday,
                periods=num_of_weeks if period == "week" else num_of_days,
                freq="W" if period == "week" else "D",
            )
        zero_forecast = pd.DataFrame(
            {
                "view_date": ds_values,
                "sku_id": sku_id_and_warehouse_no.split("_")[0],
                "warehouse_no": sku_id_and_warehouse_no.split("_")[1],
                "estimated_sales": 0.0,
                "estimated_sales_lower": 0.0,
                "estimated_sales_upper": 0.0,
                "estimated_sales_cnt": 0,
                "estimated_sales_lower_cnt": 0,
                "estimated_sales_upper_cnt": 0,
                "on_sale_days": group_df.shape[0],
                "type": period,
            }
        )
        zero_forecast_list.append(zero_forecast)
    return zero_forecast_list


def train_and_predict_skus(
    group_index=0,
    batch_index=0,
    share_df_dict: dict = {},
    sku_id_list: list = [],
    quantiles: any = None,
    yesterday: str = None,
    lock: Lock = None,
    global_counter: Value = None,
    total_steps: int = 1,
) -> Union[list, list]:

    logging.info(f"第{batch_index}批次, total_steps:{total_steps}")

    predictions_list = []
    sku_metrics_df_list = []
    failed_sku_list = []

    sku_ids_that_this_process_train = []

    while len(sku_id_list) > 0 and len(sku_ids_that_this_process_train) < total_steps:
        sku_id_to_train = None
        record_df = None
        start_time = time.time()
        while time.time() - start_time < 60:  # 尝试获取锁最多1分钟
            if lock.acquire(blocking=False):
                try:
                    if len(sku_id_list) > 0:
                        sku_id_to_train = sku_id_list.pop(0)
                        sku_ids_that_this_process_train.append(sku_id_to_train)
                        global_counter.value += 1  # 正确的更新方式
                        record_df = share_df_dict.get(sku_id_to_train)
                    break
                finally:
                    lock.release()
            time.sleep(0.1)  # 短暂休眠以避免过度消耗CPU

        if sku_id_to_train is None:
            logging.warning(
                f"group_index/batch_index:{group_index}/{batch_index}. 无法在1分钟内获取锁或sku_id_list为空，跳过此次循环. sku_ids_that_this_process_train:{sku_ids_that_this_process_train}"
            )
            break

        sku_id = sku_id_to_train

        if record_df is None:
            logging.error(f"SKU ID没有数据:{sku_id_to_train}")
            continue

        warehouse_no_list = list(record_df["warehouse_no"].unique())
        try:
            logging.info(f"开始训练SKU:{sku_id}, warehouse_no_list:{warehouse_no_list}")
            start_time = time.time()

            df = record_df[["ds", "y", "ID"]]

            prediction, sku_metrics_df = predict_period(
                dataframe=df,
                num_periods=num_of_weeks if period == "week" else num_of_days,
                quantiles=quantiles,
                train_end_date=yesterday,
            )
            log_cpu_usage()

            end_time = time.time()
            training_time = end_time - start_time

            prediction["sku_id"] = sku_id
            prediction["warehouse_no"] = prediction["ID"].str.split("_").str[1]

            # 按ID分组并计算每组的on_sale_days
            on_sale_days = (
                record_df.groupby("ID").size().reset_index(name="on_sale_days")
            )
            prediction = prediction.merge(on_sale_days, on="ID", how="left")

            predictions_list.append(prediction)
            sku_metrics_df_list.append(sku_metrics_df)
            logging.info(
                f"group_index/batch_index:{group_index}/{batch_index}, SKU训练完成:{sku_id}, 训练耗时: {training_time:.2f}秒, 进度: {global_counter.value}/{total_steps}, {global_counter.value/total_steps:.1%}"
            )

        except Exception as e:
            logging.error(
                f"{sku_id}训练异常:{e}\nStack trace: {traceback.format_exc()}"
            )
            # 保存数据到CSV，便于排查
            failed_sku_list.append(sku_id)
            record_df.to_csv(
                f"./{failed_csv_path}/训练异常_{sku_id}_{date_of_now}_{period}.csv",
                index=False,
            )
            continue

    # 打印训练失败的SKU_ID
    if len(failed_sku_list) > 0:
        logging.error(f">>>>以下SKU训练失败:{','.join(failed_sku_list)}")
    return predictions_list, sku_metrics_df_list


def save_all_result_into_odps(
    zero_forecast_df: pd.DataFrame,
    low_sales_sku_prediction: list,
    partition_spec: str,
    all_sku_id_list: list,
    last_7d_sales_df: pd.DataFrame = None,
) -> int:
    table_name = "summerfarm_ds.np_estimated_sku_warehouse_sales_v2_df"
    metrics_table_name = (
        "summerfarm_ds.np_estimated_sku_warehouse_sales_training_metrics_df"
    )
    logging.info("即将保存到ODPS")

    predictions_df = read_all_csv_into_single_df(prediction_csv_path)

    predictions_df[["sku_id", "warehouse_no"]] = predictions_df[
        ["sku_id", "warehouse_no"]
    ].astype(str)
    predictions_df[
        [
            "estimated_sales_cnt",
            "estimated_sales_lower_cnt",
            "estimated_sales_upper_cnt",
            "on_sale_days",
        ]
    ] = predictions_df[
        [
            "estimated_sales_cnt",
            "estimated_sales_lower_cnt",
            "estimated_sales_upper_cnt",
            "on_sale_days",
        ]
    ].astype(
        int
    )
    predictions_df["view_date"] = pd.to_datetime(predictions_df["view_date"])

    if len(zero_forecast_df) > 0:
        logging.info(f"把最终的zero forecast result和正常result拼接在一起")
        predictions_df = pd.concat([predictions_df, zero_forecast_df], axis=0)

    if len(low_sales_sku_prediction) > 0:
        logging.info("把最终的weighted forecast result和正常result拼接在一起")
        predictions_df = pd.concat([predictions_df, low_sales_sku_prediction], axis=0)

    processed_sku_list = list(predictions_df["sku_id"].unique())
    len_of_predictions_df = len(predictions_df)
    logging.info(f"即将保存到ODPS, SKU列表:{','.join(processed_sku_list)}")
    if len(processed_sku_list) != len(all_sku_id_list):
        logging.error(
            f"======>>>>>请注意，获取到的SKU数据，和即将写入ODPS的预测数据不同, processed_sku_list:{len(processed_sku_list)}, all_sku_id_list:{len(all_sku_id_list)}"
        )

    predictions_df["warehouse_no"] = predictions_df["warehouse_no"].astype(int)
    predictions_df.drop(columns=["yhat1", "id"], errors="ignore", inplace=True)

    # Join with last_7d_sales_df to get last 7 days sales quantity
    if last_7d_sales_df is not None and len(last_7d_sales_df) > 0:
        last_7d_sales_df["warehouse_no"] = last_7d_sales_df["warehouse_no"].astype(int)
        predictions_df = predictions_df.merge(
            last_7d_sales_df[["sku_id", "warehouse_no", "last_7d_sales_quantity"]],
            on=["sku_id", "warehouse_no"],
            how="left",
        )
        predictions_df["last_7d_sales_quantity"] = predictions_df[
            "last_7d_sales_quantity"
        ].fillna(0)
        predictions_df["last_7d_sales_quantity"] = predictions_df[
            "last_7d_sales_quantity"
        ].astype(int)
    else:
        predictions_df["last_7d_sales_quantity"] = 0
    write_pandas_df_into_odps(
        df=predictions_df,
        table_name=table_name,
        partition_spec=partition_spec,
        overwrite=True,
    )
    del predictions_df

    metrics_df = read_all_csv_into_single_df(metrics_csv_path)
    metrics_df[["sku_id", "warehouse_no"]] = metrics_df[
        ["sku_id", "warehouse_no"]
    ].astype(str)

    write_pandas_df_into_odps(
        df=metrics_df,
        table_name=metrics_table_name,
        partition_spec=partition_spec,
        overwrite=False,
    )

    return len_of_predictions_df


if __name__ == "__main__":

    retry_interval = 300  # 5分钟，单位为秒
    max_retries = int(
        max_wait_hour * 3600 / retry_interval
    )  # 小时*3600单位秒/300重试间隔秒

    for attempt in range(max_retries):
        if is_data_ready(ds_yesterday):
            logging.info(f"ds:{ds_yesterday} 数据已就绪，继续执行")
            break
        else:
            if attempt < max_retries - 1:
                logging.warning(
                    f"ds:{ds_yesterday} 数据未就绪，{retry_interval}秒后重试 (尝试 {attempt + 1}/{max_retries})"
                )
                time.sleep(retry_interval)
            else:
                error_msg = f"app_estimated_sku_warehouse_record_df, ds={ds_yesterday} 数据在{max_wait_hour}小时内未就绪，退出程序"
                logging.error(error_msg)
                send_markdown_to_feishu(
                    title=f"数据未就绪:ds={ds_yesterday}",
                    markdown_content=error_msg,
                    feishu_token=feishu_token,
                )
                sys.exit(-1)

    from multiprocessing import Manager

    confidence_level = 0.9
    boundaries = round((1 - confidence_level) / 2, 2)
    quantiles = [boundaries, confidence_level + boundaries]

    yesterday = datetime.now() - timedelta(days=1)

    # 加载数据
    sales_records_df, no_order_sku_list, last_7d_sales_df = load_data()

    sku_desc = f"""
- SKU总数(去重):{len(sales_records_df['sku_id'].unique())}
- SKU-warehouse组合总数:{len(sales_records_df['ID'].unique())}
- 无销量的SKU-warehouse组合总数:{len(no_order_sku_list)}
"""

    send_markdown_to_feishu(
        title=f"nueral_prophet开始训练了:ds={ds_yesterday},period={period}",
        markdown_content=sku_desc,
        feishu_token=feishu_token,
    )

    valid_sales_records_df = sales_records_df[
        sales_records_df["days_from_now_since_last_order_date"]
        < last_order_date_threshold
    ]

    unique_warehouse_list = list(valid_sales_records_df["warehouse_no"].unique())

    low_sales_sku_prediction, all_low_sales_skus = (
        generate_weighted_average_forecasting_for_low_sales_sku(
            all_sku_df=valid_sales_records_df, end_date=yesterday
        )
    )
    # 那些低销量的SKU使用了线形预测方法（加权平均），这里需要过滤掉
    if len(low_sales_sku_prediction) > 0:
        valid_sales_records_df = valid_sales_records_df[
            ~valid_sales_records_df["ID"].isin(all_low_sales_skus)
        ]

    has_no_order_sales_records_df = sales_records_df[
        sales_records_df["days_from_now_since_last_order_date"]
        >= last_order_date_threshold
    ]

    total_valid_sku_list = list(valid_sales_records_df["sku_id"].unique())
    all_sku_id_list = list(sales_records_df["sku_id"].unique())

    # 将total_valid_sku_list切分为total_batch_no批次
    batch_size = len(total_valid_sku_list) // total_batch_no
    sku_id_batches = [
        total_valid_sku_list[i : i + batch_size]
        for i in range(0, len(total_valid_sku_list), batch_size)
    ]

    # 如果最后一批的大小小于batch_size，将剩余的SKU ID添加到最后一批
    if len(sku_id_batches[-1]) < batch_size:
        sku_id_batches[-2].extend(sku_id_batches[-1])
        sku_id_batches.pop()

    logging.info(
        f"将{len(total_valid_sku_list)}个SKU ID切分为{len(sku_id_batches)}批，每批约{batch_size}个"
    )

    unique_ds_values = None

    for batch_index, sku_id_list in enumerate(sku_id_batches):
        predictions_list = []
        all_metrics_list = []
        total_steps = len(sku_id_list)

        # 创建一个Manager对象
        manager = Manager()
        # 使用Manager创建一个锁
        lock = manager.Lock()
        # 使用Manager创建一个共享列表
        shared_sku_id_list = manager.list()
        global_counter = manager.Value(int, 0)

        # 创建一个共享字典，用于存储sku_id到其对应sales_record_df的映射
        shared_sales_records = manager.dict()

        # 遍历每个唯一的sku_id，创建对应的DataFrame并存储在共享字典中
        for sku_id in sku_id_list:
            sku_df = sales_records_df[sales_records_df["sku_id"] == sku_id]
            shared_sales_records[sku_id] = sku_df

        # 将sku_id列表添加到共享列表中
        shared_sku_id_list.extend(sku_id_list)

        logging.info(
            f"total_batch_no:{total_batch_no}, batch_index:{batch_index}, SKU总个数:{len(sku_id_list)}"
        )

        with multiprocessing.Pool(processes=max_workers) as pool:
            # 获取进程数
            logging.info(f"总计进程数: {max_workers}")
            results = []
            for group_index in range(0, max_workers):
                result = pool.apply_async(
                    train_and_predict_skus,
                    args=(
                        group_index,
                        batch_index,
                        shared_sales_records,
                        shared_sku_id_list,
                        quantiles,
                        yesterday,
                        lock,
                        global_counter,
                        total_steps,
                    ),
                )
                results.append(result)

            for result in results:
                try:
                    prediction, metrics = result.get(
                        timeout=60 * batch_timeout_minutes
                    )  # 设置1小时超时
                    if prediction is not None:
                        predictions_list.extend(prediction)
                    if metrics is not None:
                        all_metrics_list.extend(metrics)
                except multiprocessing.TimeoutError as e:
                    error_traceback = traceback.format_exc()
                    logging.error(
                        f"batch:{batch_index}子进程执行超时:{e}\n堆栈跟踪:\n{error_traceback}"
                    )
                    send_markdown_to_feishu(
                        f"SCP训练任务超时了:period={period},ds={ds_yesterday},batch={batch_index}",
                        f"错误信息: {e}\n\n堆栈跟踪:\n{error_traceback}",
                        feishu_token=feishu_token,
                    )
            pool.terminate()  # 立即终止进程池

        if len(predictions_list) > 0 and unique_ds_values is None:
            unique_ds_values = predictions_list[0]["ds"].unique()

        if len(predictions_list) <= 0:
            logging.error(f"批次失败了:{batch_index}")
            continue

        predictions_df = pd.concat(predictions_list, axis=0)

        predictions_df.rename(
            columns={
                "ds": "view_date",
                "yhat1": "estimated_sales",
                "yhat1 5.0%": "estimated_sales_lower",
                "yhat1 95.0%": "estimated_sales_upper",
            },
            inplace=True,
            errors="ignore",
        )
        predictions_df = predictions_df[
            [
                "view_date",
                "sku_id",
                "warehouse_no",
                "estimated_sales",
                "estimated_sales_lower",
                "estimated_sales_upper",
                "estimated_sales_cnt",
                "estimated_sales_lower_cnt",
                "estimated_sales_upper_cnt",
                "on_sale_days",
                "type",
            ]
        ]

        num_of_models = len(predictions_df.groupby(by=["sku_id", "warehouse_no"]))
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds() / 60
        logging.info(
            f"batch_index:{batch_index} 训练完成了，总计训练:{num_of_models}, 开始时间:{start_time}, 结束时间:{end_time}, 总耗时:{total_time:.2f} 分钟"
        )

        predictions_df.to_csv(
            f"{prediction_csv_path}/batch_{batch_index}.csv", index=False
        )
        del predictions_df

        if len(all_metrics_list) > 0:
            all_metrics_list_df = pd.concat(all_metrics_list, axis=0)
            all_metrics_list_df.to_csv(
                f"{metrics_csv_path}/batch_{batch_index}.csv", index=False
            )
            del all_metrics_list_df
        else:
            logging.error(f"该批次训练全部失败:{batch_index}")

    zero_forecast_list = generate_zero_forecast_result(
        has_no_order_df=has_no_order_sales_records_df,
        ds_values=unique_ds_values,
    )
    zero_forecast_df = pd.DataFrame()
    if len(zero_forecast_list) > 0:
        logging.warning(f"concat zero forecast result...{len(zero_forecast_list)}")
        zero_forecast_df = pd.concat(zero_forecast_list, axis=0)

    # 写入odps
    today = datetime.now().strftime("%Y%m%d")
    partition_spec = f"ds={today},period={period}"

    save_to_odps = "true" == os.getenv("save_to_odps", "true")
    saved_rows_cnt = 0
    if save_to_odps:
        saved_rows_cnt = save_all_result_into_odps(
            zero_forecast_df=zero_forecast_df,
            low_sales_sku_prediction=low_sales_sku_prediction,
            partition_spec=partition_spec,
            all_sku_id_list=all_sku_id_list,
            last_7d_sales_df=last_7d_sales_df,
        )
    else:
        logging.warning(f"无须写入ODPS")

    end_time = datetime.now()
    total_time = end_time - start_time
    total_hours = int(total_time.total_seconds() // 3600)
    total_minutes = int((total_time.total_seconds() % 3600) // 60)
    total_seconds = int(total_time.total_seconds() % 60)
    msg = f"""任务完成了
    
- 开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}
- 结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}
- 总耗时: {total_hours}时{total_minutes}分{total_seconds}秒
- 写入数据: {saved_rows_cnt}条
- SKU总数: {len(all_sku_id_list)}个
- 库存仓号: {','.join(unique_warehouse_list)}"""

    logging.info(msg)
    send_markdown_to_feishu(
        title=f"SCP训练任务完成:{partition_spec}",
        markdown_content=msg,
        feishu_token=feishu_token,
    )
