{"cells": [{"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2023-12-10 20231210\n"]}], "source": ["import os\n", "import json\n", "import pandas as pd\n", "import pandasql\n", "from odps import ODPS\n", "from odps.df import DataFrame\n", "from datetime import datetime,timedelta\n", "pd.set_option('display.max_rows', None)  # Set to None to display all rows\n", "pd.set_option('display.max_columns', None)  # Set to None to display all columns\n", "pd.set_option('display.width', None)  # Set width to None for automatic wrapping\n", "\n", "ALIBABA_CLOUD_ACCESS_KEY_ID='LTAI5tJjdqu75w9fAqsfHTcY'\n", "ALIBABA_CLOUD_ACCESS_KEY_SECRET='******************************'\n", "odps = ODPS(\n", "    ALIBABA_CLOUD_ACCESS_KEY_ID,\n", "    ALIBABA_CLOUD_ACCESS_KEY_SECRET,\n", "    project='summerfarm_ds_dev',\n", "    endpoint='http://service.cn-hangzhou.maxcompute.aliyun.com/api',\n", ")\n", "\n", "a_month_ago=datetime.now()-<PERSON><PERSON><PERSON>(30)\n", "a_month_ago_yyyymmdd=a_month_ago.strftime('%Y%m%d')\n", "a_month_ago_yyyy_mm_dd=a_month_ago.strftime('%Y-%m-%d')\n", "\n", "print(a_month_ago_yyyy_mm_dd, a_month_ago_yyyymmdd)\n", "\n", "def create_directory_if_not_exists(path):\n", "    if not os.path.exists(path):\n", "        os.makedirs(path)\n", "\n", "def get_odps_sql_result_as_df(sql):\n", "    instance=odps.execute_sql(sql, hints={'odps.sql.hive.compatible':True})\n", "    instance.wait_for_success()\n", "    pd_df=None\n", "    with instance.open_reader(tunnel=True) as reader:\n", "        # type of pd_df is pandas DataFrame\n", "        pd_df = reader.to_pandas()\n", "\n", "    if pd_df is not None:\n", "        print(f\"sql:\\n{sql}\\ncolumns:{pd_df.columns}\")\n", "        return pd_df\n", "    return None\n", "\n", "root_path=\"./SCP-SKU-线上对比\"\n", "create_directory_if_not_exists(root_path)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 按日预测"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sql:\n", "\n", "SELECT  np.view_date\n", "        ,np.sku_id\n", "        ,real.category1\n", "        ,np.warehouse_no\n", "        ,real.warehouse_name\n", "        ,np.estimated_sales_cnt\n", "        ,real.sum_sku_cnt_real\n", "        ,real.deliver_date\n", "        ,real.spu_name\n", "        ,real.sku_disc\n", "FROM    summerfarm_ds.np_estimated_sku_warehouse_sales_df np\n", "INNER JOIN  (\n", "                SELECT  ds\n", "                        ,deliver_date\n", "                        ,sku_id\n", "                        ,warehouse_no\n", "                        ,category1\n", "                        ,min(warehouse_name) warehouse_name\n", "                        ,min(spu_name) spu_name\n", "                        ,min(sku_disc) sku_disc\n", "                        ,SUM(sku_cnt) sum_sku_cnt_real\n", "                FROM    summerfarm_tech.dwd_dlv_delivery_cost_di dc\n", "                WHERE   ds >= '20231221'\n", "                GROUP BY ds\n", "                         ,category1\n", "                         ,deliver_date\n", "                         ,sku_id\n", "                         ,warehouse_no\n", "            ) real\n", "ON      real.ds = np.ds\n", "AND     real.deliver_date = np.view_date\n", "AND     real.sku_id = np.sku_id\n", "AND     real.warehouse_no = np.warehouse_no\n", "WHERE   np.ds >= '20231221'\n", "AND     np.type = 'day'\n", "ORDER BY np.view_date DESC,np.warehouse_no,np.sku_id\n", "\n", "columns:Index(['view_date', 'sku_id', 'category1', 'warehouse_no', 'warehouse_name',\n", "       'estimated_sales_cnt', 'sum_sku_cnt_real', 'deliver_date', 'spu_name',\n", "       'sku_disc'],\n", "      dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>view_date</th>\n", "      <th>sku_id</th>\n", "      <th>category1</th>\n", "      <th>warehouse_no</th>\n", "      <th>warehouse_name</th>\n", "      <th>estimated_sales_cnt</th>\n", "      <th>sum_sku_cnt_real</th>\n", "      <th>deliver_date</th>\n", "      <th>spu_name</th>\n", "      <th>sku_disc</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-01-08</td>\n", "      <td>1003074364015</td>\n", "      <td>乳制品</td>\n", "      <td>2</td>\n", "      <td>上海总仓</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>2024-01-08</td>\n", "      <td>ProtagxEva乳酸黄油</td>\n", "      <td>10KG*1箱</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-01-08</td>\n", "      <td>1233884862</td>\n", "      <td>乳制品</td>\n", "      <td>2</td>\n", "      <td>上海总仓</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2024-01-08</td>\n", "      <td>安佳碎条状马苏里拉干酪</td>\n", "      <td>12KG*1箱/红标(产品适用于堂食，胶质感较强，拉丝效果较好)</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   view_date         sku_id category1  warehouse_no warehouse_name  \\\n", "0 2024-01-08  1003074364015       乳制品             2           上海总仓   \n", "1 2024-01-08     1233884862       乳制品             2           上海总仓   \n", "\n", "   estimated_sales_cnt  sum_sku_cnt_real deliver_date        spu_name  \\\n", "0                    1                 3   2024-01-08  ProtagxEva乳酸黄油   \n", "1                    0                 1   2024-01-08     安佳碎条状马苏里拉干酪   \n", "\n", "                           sku_disc  \n", "0                           10KG*1箱  \n", "1  12KG*1箱/红标(产品适用于堂食，胶质感较强，拉丝效果较好)  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["sql=f\"\"\"\n", "SELECT  np.view_date\n", "        ,np.sku_id\n", "        ,real.category1\n", "        ,np.warehouse_no\n", "        ,real.warehouse_name\n", "        ,np.estimated_sales_cnt\n", "        ,real.sum_sku_cnt_real\n", "        ,real.deliver_date\n", "        ,real.spu_name\n", "        ,real.sku_disc\n", "FROM    summerfarm_ds.np_estimated_sku_warehouse_sales_df np\n", "INNER JOIN  (\n", "                SELECT  ds\n", "                        ,deliver_date\n", "                        ,sku_id\n", "                        ,warehouse_no\n", "                        ,category1\n", "                        ,min(warehouse_name) warehouse_name\n", "                        ,min(spu_name) spu_name\n", "                        ,min(sku_disc) sku_disc\n", "                        ,SUM(sku_cnt) sum_sku_cnt_real\n", "                FROM    summerfarm_tech.dwd_dlv_delivery_cost_di dc\n", "                WHERE   ds >= '20231221'\n", "                GROUP BY ds\n", "                         ,category1\n", "                         ,deliver_date\n", "                         ,sku_id\n", "                         ,warehouse_no\n", "            ) real\n", "ON      real.ds = np.ds\n", "AND     real.deliver_date = np.view_date\n", "AND     real.sku_id = np.sku_id\n", "AND     real.warehouse_no = np.warehouse_no\n", "WHERE   np.ds >= '20231221'\n", "AND     np.type = 'day'\n", "ORDER BY np.view_date DESC,np.warehouse_no,np.sku_id\n", "\"\"\"\n", "\n", "all_np_result_and_real_df=get_odps_sql_result_as_df(sql=sql)\n", "all_np_result_and_real_df.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 使用SCP标注数据（S/A/B/C）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "all_labels=pd.read_csv(\"./scp_sku_lablels.csv\")\n", "all_labels['是否SCP关注']='关注'\n", "all_labels.head(2)\n", "merged_result = all_np_result_and_real_df.merge(all_labels, left_on='spu_name', right_on='品名', how='left')\n", "merged_result.head(5)\n", "merged_result.loc[merged_result['是否SCP关注'] != '关注', '是否SCP关注'] = '不关注'\n", "merged_result.loc[merged_result['是否SCP关注'] != '关注', 'GMV占比'] = '不关注'\n", "merged_result.loc[merged_result['是否SCP关注'] != '关注', '分层'] = '不关注'\n", "\n", "merged_result.groupby(['warehouse_name','是否SCP关注']).count()\n", "\n", "merged_result = merged_result.assign(abs_error=np.abs(merged_result['estimated_sales_cnt'] - merged_result['sum_sku_cnt_real']))\n", "merged_result = merged_result.assign(abs_error_percent=np.round(merged_result['abs_error'] / merged_result['sum_sku_cnt_real'],4))\n", "merged_result['sku_cnt_total'] = merged_result.groupby(['warehouse_no', 'warehouse_name','sku_id'])['sum_sku_cnt_real'].transform('sum')\n", "merged_result['day_has_deliver_data'] = merged_result.groupby(['warehouse_no', 'warehouse_name','sku_id'])['deliver_date'].transform('count')\n", "merged_result.to_csv(f\"{root_path}/all_np_result_and_real_daily_df.csv\", index=False)\n", "merged_result.head(4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["stats=pandasql.sqldf(\"\"\"select spu_name,warehouse_name,sku_id\n", "                     ,round(1.00*sum(abs_error)/sum(sum_sku_cnt_real),4) as avg_MAE\n", "                     ,count(distinct view_date) as days_has_data \n", "                     ,sum(sum_sku_cnt_real) as total_sku_cnt_real\n", "                     ,sum(estimated_sales_cnt) total_estimated_sales_cnt\n", "               from merged_result group by spu_name,warehouse_name,sku_id order by total_sku_cnt_real desc limit 200\"\"\")\n", "stats"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_result['sku_cnt_total'].quantile([0.05,0.1,0.15,0.2,0.3,0.4,0.5,0.8])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["merged_result['day_has_deliver_data'].quantile([0.05,0.1,0.2,0.5,0.8,0.9,0.95])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import matplotlib.dates as mdates\n", "\n", "sample_sku=merged_result[(merged_result['sku_id']=='N001G01R002')]\n", "pd.options.mode.chained_assignment = None  # default='warn'\n", "sns.lineplot(data=sample_sku, x=sample_sku['view_date'], y='abs_error_percent', hue=sample_sku['warehouse_no'], palette='tab10')\n", "plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%m'))\n", "plt.gca().xaxis.set_major_locator(mdates.MonthLocator())\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 按周预测数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sql=f\"\"\"\n", "SELECT  np.view_date\n", "        ,np.sku_id\n", "        ,real.category1\n", "        ,np.warehouse_no\n", "        ,real.warehouse_name\n", "        ,np.estimated_sales_cnt\n", "        ,real.sum_sku_cnt_real\n", "        ,real.deliver_date\n", "        ,real.spu_name\n", "        ,real.sku_disc\n", "FROM    summerfarm_ds.np_estimated_sku_warehouse_sales_df np\n", "INNER JOIN  (\n", "                SELECT  ds\n", "                        ,deliver_date\n", "                        ,sku_id\n", "                        ,warehouse_no\n", "                        ,category1\n", "                        ,min(warehouse_name) warehouse_name\n", "                        ,min(spu_name) spu_name\n", "                        ,min(sku_disc) sku_disc\n", "                        ,SUM(sku_cnt) sum_sku_cnt_real\n", "                FROM    summerfarm_tech.dwd_dlv_delivery_cost_di dc\n", "                WHERE   ds >= '20231221'\n", "                GROUP BY ds\n", "                         ,category1\n", "                         ,deliver_date\n", "                         ,sku_id\n", "                         ,warehouse_no\n", "            ) real\n", "ON      real.ds = np.ds\n", "AND     real.deliver_date = np.view_date\n", "AND     real.sku_id = np.sku_id\n", "AND     real.warehouse_no = np.warehouse_no\n", "WHERE   np.ds >= '20231221'\n", "AND     np.type = 'week'\n", "ORDER BY np.view_date DESC,np.warehouse_no,np.sku_id\n", "\"\"\"\n", "\n", "all_np_result_and_real_df=get_odps_sql_result_as_df(sql=sql)\n", "all_np_result_and_real_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "merged_result = all_np_result_and_real_df.merge(all_labels, left_on='spu_name', right_on='品名', how='left')\n", "merged_result.head(5)\n", "merged_result.loc[merged_result['是否SCP关注'] != '关注', '是否SCP关注'] = '不关注'\n", "merged_result.loc[merged_result['是否SCP关注'] != '关注', 'GMV占比'] = '不关注'\n", "merged_result.loc[merged_result['是否SCP关注'] != '关注', '分层'] = '不关注'\n", "\n", "merged_result.groupby(['warehouse_name','是否SCP关注']).count()\n", "\n", "merged_result = merged_result.assign(abs_error=np.abs(merged_result['estimated_sales_cnt'] - merged_result['sum_sku_cnt_real']))\n", "merged_result = merged_result.assign(abs_error_percent=np.round(merged_result['abs_error'] / merged_result['sum_sku_cnt_real'],4))\n", "merged_result['sku_cnt_total'] = merged_result.groupby(['warehouse_no', 'warehouse_name','sku_id'])['sum_sku_cnt_real'].transform('sum')\n", "merged_result['day_has_deliver_data'] = merged_result.groupby(['warehouse_no', 'warehouse_name','sku_id'])['deliver_date'].transform('count')\n", "merged_result.to_csv(f\"{root_path}/all_np_result_and_real_weekly_df.csv\", index=False)\n", "merged_result.head(4)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 2}