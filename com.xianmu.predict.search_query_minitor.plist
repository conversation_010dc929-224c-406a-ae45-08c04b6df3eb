<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.xianmu.search_query_monitor</string>
    <key>ProgramArguments</key>
    <array>
        <string>/bin/bash</string>
        <string>-l</string>
        <string>-c</string>
        <string>cd /Users/<USER>/Documents/xianmu-ai && source np/bin/activate && rm -rf ./lightning_logs/* ; truncate -s 0 logs/search_query_monitor.error.log logs/search_query_monitor.log ; python -m 搜索系统.monitor_online_keyword_performence</string>
    </array>
    <key>StartCalendarInterval</key>
    <array>
        <dict>
            <key>Hour</key>
            <integer>10</integer>
            <key>Minute</key>
            <integer>10</integer>
        </dict>
    </array>
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/Users/<USER>/Documents/xianmu-ai/np/bin:/opt/homebrew/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin</string>
        <key>LANG</key>
        <string>en_US.UTF-8</string>
        <key>LC_ALL</key>
        <string>en_US.UTF-8</string>
        <key>HOME</key>
        <string>/Users/<USER>/string>
        <key>ALIBABA_CLOUD_ACCESS_KEY_ID</key>
        <string>LTAI5tQzmpz2nQEWdiqvQGsc</string>
        <key>ALIBABA_CLOUD_ACCESS_KEY_SECRET</key>
        <string>******************************</string>
        <key>XM_FAST_GPT_API_KEY</key>
        <string>sk-Is03LB8g58Qz41bdQKip5g</string>
        <key>OPENAI_API_KEY</key>
        <string>sk-Is03LB8g58Qz41bdQKip5g</string>
    </dict>
    <key>WorkingDirectory</key>
    <string>/Users/<USER>/Documents/xianmu-ai</string>
    <key>StandardOutPath</key>
    <string>/Users/<USER>/Documents/xianmu-ai/logs/search_query_monitor.log</string>
    <key>StandardErrorPath</key>
    <string>/Users/<USER>/Documents/xianmu-ai/logs/search_query_monitor.error.log</string>
    <key>RunAtLoad</key>
    <true/>
    <key>UserName</key>
    <string>jishubu</string>
</dict>
</plist>