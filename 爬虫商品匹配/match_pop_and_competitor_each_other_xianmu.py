#!/usr/bin/env python
# coding: utf-8

# # 鲜果POP商品和标果之间匹配和比较
#
# ## 先登录admin

import traceback
import sys
from venv import logger

sys.path.append("../")

# 获取token
import requests
from datetime import datetime, timedelta
from odps_client import logging
import os
from feishu_client import send_markdown_to_feishu

login_url = "https://admin.summerfarm.net/authentication/auth/username/login"
login_data = {
    "username": "<EMAIL>",
    "password": os.getenv("XIANMU_ADMIN_PASSWORD_POP"),
}

token = requests.post(login_url, data=login_data).json()

logging.info(f"token:{token}")

started_at = datetime.now()

headers = {
    "token": token.get("data").get("token"),
    "xm-rqid": "pop_buyer_test",
    "xm-uid": "1090646",
    "Content-Type": "application/json;charset=UTF-8",
}

logging.info(f"headers:{headers}")

import argparse

parser = argparse.ArgumentParser(description="Process some integers.")
parser.add_argument(
    "--top_products_to_save",
    type=int,
    default=500,
    help="the number of top products to save",
)
parser.add_argument(
    "--xianmu_sku_cnt_to_save",
    type=int,
    default=500,
    help="the number of xianmu skus to save",
)
parser.add_argument(
    "--ds_to_run",
    type=str,
    default=(datetime.now() - timedelta(days=1)).strftime("%Y%m%d"),
    help="the date to run",
)
parser.add_argument(
    "--llm_top_n",
    type=int,
    default=5,
    help="the number of top LLM results to consider",
)
parser.add_argument(
    "--feishu_token",
    type=str,
    default="aabbccdd",
    help="feishu token",
)
args = parser.parse_args()

top_products_to_save = args.top_products_to_save
xianmu_sku_cnt_to_save = args.xianmu_sku_cnt_to_save
ds_to_run = args.ds_to_run
llm_top_n = args.llm_top_n
feishu_token = args.feishu_token

logging.info(f"top_products_to_save:{top_products_to_save}")
logging.info(f"ds_to_run:{ds_to_run}")

DATA_PATH = f"./data/pop_{ds_to_run}"

from odps_client import create_directory_if_not_exists

create_directory_if_not_exists(DATA_PATH)


def get_product_info(pd_id=11087):
    url = "https://admin.summerfarm.net/sf-mall-manage/product/query/info"
    data = {"pdId": pd_id}
    return requests.post(url=url, headers=headers, json=data).json().get("data")


# Define the 'CASE WHEN' logic
def open_sale_case_when(open_sale=-1):
    if open_sale == 0:
        return "上架"
    elif open_sale == 1:
        return "有库存时上架"
    elif open_sale == 2:
        return "定时上架"
    elif open_sale == 3:
        return "有库存时上架(永久生效)"
    else:
        return "未定义"


pd_list = []
page_index = 1
page_size = 500
while True:
    response = (
        requests.get(
            f"https://admin.summerfarm.net/sf-mall-manage/product/query/selectPage?outdated=0&subType=3&type=0&grandCategoryId=400&pageIndex={page_index}&pageSize={page_size}",
            headers=headers,
        )
        .json()
        .get("data")
    )

    current_page_list = response.get("list", [])
    pd_list.extend(current_page_list)

    # 如果返回的列表数量小于500，说明已经获取完所有数据
    if len(current_page_list) < page_size:
        break

    page_index += 1

logging.info(f"获取到的鲜果POP商品个数{len(pd_list)}")


# ## 构建鲜沐的类目树


url = "https://admin.summerfarm.net/category"
all_xianmu_category = requests.get(url, headers=headers).json().get("data")
all_xianmu_category_pop = []
for cate in all_xianmu_category:
    # 取非POP类目
    if "POP" not in cate["category"]:
        all_xianmu_category_pop.extend(cate.get("categoryList"))

all_xianmu_category_pop_map = {}
for second_level in all_xianmu_category_pop:
    second_category_name = second_level.get("category", "")
    for leaf_level in second_level.get("categoryList", []):
        logging.info(f"鲜果鲜沐的类目:{leaf_level}")
        all_xianmu_category_pop_map[f"{leaf_level.get('id','haha')}"] = (
            f"{second_category_name}/{leaf_level['category']}"
        )


# ## 从鲜沐admin后台获取所有的POP商品


import json
import pandas as pd


def extract_product_fields(json_data):
    # Initialize a list to hold the extracted data
    extracted_data = []
    keyValueList = ",".join(
        [
            f"{kv.get('name')}:{kv.get('productsPropertyValue')}"
            for kv in json_data.get("keyValueList", [])
        ]
    )

    # Loop through each item in the inventory detail list
    for item in json_data.get("inventoryDetailVOS", []):
        # Concatenate saleValueList
        sale_value_list = item.get("saleValueList", [])
        buyer_name = item.get("buyerName", "")
        sale_value_string = (
            ",".join(
                f"{sv.get('name','')}:{sv.get('productsPropertyValue', '')}"
                for sv in sale_value_list
            )
            if len(sale_value_list) > 0
            else ""
        )
        # Loop through each area SKU in the item
        for area_sku in item.get("areaSkuVOS", []):
            # Extract the necessary fields and create a dictionary
            if not area_sku.get("onSale", False):
                logging.info(f'SKU未上架:{json_data.get("pdName", "")}, {area_sku}')
                continue
            picturePath = json_data.get("picturePath", "404.jpg")
            picture = (
                picturePath
                if picturePath.startswith("http")
                else "https://azure.summerfarm.net/" + picturePath
            )
            extracted_dict = {
                "sku": area_sku.get("sku", ""),
                "categoryName": all_xianmu_category_pop_map.get(
                    f'{json_data.get("categoryId")}',
                    f'{json_data.get("categoryId")}找不到类目嘞!',
                ),
                "openSale": open_sale_case_when(area_sku.get("openSale", -1)),
                "onSale": "上架中" if area_sku.get("onSale", False) else "下架(没库存)",
                "pdId": item.get("pdId", ""),
                "sku_name": json_data.get("productName", ""),
                "pd_name": json_data.get("pdName", ""),
                "realName": item.get("realName", ""),
                "picture": picture,
                "price": area_sku.get("price", 0),
                "supplyPrice": json.loads(item.get("createRemark", "{}")).get(
                    "supplyPrice", 0
                ),
                "areaNo": area_sku.get("areaNo", ""),
                "areaName": area_sku.get("areaName", ""),
                "netWeightNum": item.get("netWeightNum", 0),
                "weightNum": item.get("weightNum", 0),
                "weight": item.get("weight", ""),
                "sku_spec": sale_value_string,
                "spu_spec": keyValueList,
                "buyer_name": buyer_name,
            }
            # Add the dictionary to the list
            extracted_data.append(extracted_dict)

    return extracted_data


from datetime import datetime, timedelta

product_list = []

for product in pd_list:
    product = get_product_info(product["pdId"])
    product_list.append(product)

sku_list = []

for product in product_list:
    extracted_data = extract_product_fields(product)
    sku_list.extend(extracted_data)

sku_list_df = pd.DataFrame(sku_list)
sku_list_df.to_csv(
    f"{DATA_PATH}/鲜果POP全部SKU_{datetime.now().strftime('%Y%m%d')}.csv", index=False
)
sku_list_df.drop_duplicates(subset=["sku"], inplace=True)
sku_list_df.sort_values(by=["categoryName", "pd_name"], inplace=True)


# ### 获取标果的爬虫数据（当天）


from odps_client import get_odps_sql_result_as_df

biaoguo_x_xianmu_df = get_odps_sql_result_as_df(
    f"""
SELECT  categoryname,backcategoryname,id,competitor,skucode,spider_fetch_time,goodspropdetaillist,createtime,goodssiphoncommissionrate
        ,standardprice,finalstandardprice,lasttimestandardprice,finalunitpricecatty,monthsale,attachurlr AS url,sellersiphoncommissionrate
        ,unitpricecatty,unit,sellername,grossweight,netweight,specification,babyname,goodsname,goodstype,sevendayaftersale
FROM    (
            SELECT  *
                    ,RANK() OVER (PARTITION BY categoryname,goodsname ORDER BY monthsale DESC,finalstandardprice ASC,createtime DESC,spider_fetch_time DESC) AS rnk
            FROM    summerfarm_ds.spider_biaoguo_with_prop_product_result_df
            WHERE   ds = MAX_PT('summerfarm_ds.spider_biaoguo_with_prop_product_result_df')
            AND     competitor = '标果-杭州' 
            AND     categoryname like '水果_%'
        )
WHERE   rnk = 1
LIMIT   100000;
"""
)

logging.info(f"标果的商品数:{len(biaoguo_x_xianmu_df)}")
logging.info(
    f"标果的所有类目: {','.join(biaoguo_x_xianmu_df['categoryname'].unique())}"
)
send_markdown_to_feishu(
    f"鲜果POP商品匹配任务开始了:ds={ds_to_run}",
    f"- 标果的商品个数:{len(biaoguo_x_xianmu_df)}",
    feishu_token=feishu_token,
)

# ## Azure客户端定义


import os
from openai import AzureOpenAI, OpenAI
import httpx

client = AzureOpenAI(
    # https://esat-us.openai.azure.com/openai/deployments/text-embedding-3-small/embeddings?api-version=2023-05-15
    api_version="2023-07-01-preview",
    azure_endpoint="https://esat-us.openai.azure.com",
    api_key=os.getenv("AZURE_API_KEY_XM", "please set:AZURE_API_KEY_XM"),
    http_client=httpx.Client(proxies={"http://": None, "https://": None}),
)

embedding_model = os.getenv("AZURE_EMBEDDING_MODEL", "text-embedding-3-small")


# ## 使用embedding模型做标题的匹配


import sqlite3
import os

# Ensure the directory exists
os.makedirs(os.path.expanduser("~/sqlite"), exist_ok=True)
# Path to the database
db_path = os.path.expanduser("~/sqlite/embeddings.db")
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# Create table if it doesn't exist
cursor.execute(
    """
CREATE TABLE IF NOT EXISTS embeddings (
    input_text TEXT PRIMARY KEY,
    embedding TEXT
)
"""
)


def get_embedding_directly_from_azure(input: str) -> list:
    embbed = client.embeddings.create(model=embedding_model, input=input)
    return embbed.to_dict().get("data", [{}])[0].get("embedding")


def get_embedding(input_text) -> str:

    # Check if input text already exists in the database
    cursor.execute(
        "SELECT embedding FROM embeddings WHERE input_text = ?", (input_text,)
    )
    result = cursor.fetchone()

    if result:
        embedding = result[0]
        logging.info(
            f"Found, return the embedding of input_text:{input_text}, {embedding[:50]}"
        )
    else:
        logging.info(
            f"Not found, call the OpenAI API to get the embedding:{input_text}"
        )
        embedding = str(get_embedding_directly_from_azure(input_text))

        # Insert the new input text and embedding into the database
        cursor.execute(
            "INSERT INTO embeddings (input_text, embedding) VALUES (?, ?)",
            (input_text, embedding),
        )
        conn.commit()
    return embedding


def get_xianmu_embedding(row: pd.Series):
    sku_name = row["sku_name"]
    if sku_name is None or len(f"{sku_name}") <= 2:
        sku_name = row["pd_name"]
    input_text = f"{sku_name}"
    return get_embedding(input_text=input_text)


def get_competitor_like_biaoguo_embedding(row: pd.Series):
    goodsname = row["goods_name"]
    return get_embedding(input_text=f"{goodsname}")


def get_category_embedding(category: str):
    return get_embedding(input_text=category)


biaoguo_x_xianmu_df.rename(
    columns={"goodsname": "goods_name"}, inplace=True, errors="ignore"
)

biaoguo_x_xianmu_df["sku_embeddings"] = biaoguo_x_xianmu_df.apply(
    get_competitor_like_biaoguo_embedding, axis=1
)
biaoguo_x_xianmu_df["category_embeddings"] = (
    biaoguo_x_xianmu_df.apply(lambda row: f"{row['categoryname']}", axis=1)
).apply(get_category_embedding)

sku_list_df["sku_embeddings"] = sku_list_df.apply(get_xianmu_embedding, axis=1)
sku_list_df["category_embeddings"] = (
    sku_list_df.apply(lambda row: f"{row['categoryName']}", axis=1)
).apply(get_category_embedding)


import numpy as np
from scipy.spatial.distance import cosine


top_n = 20


def calculate_cosine_similarity(embedding1, embedding2) -> float:
    try:
        if "class 'str'" in f"{type(embedding1)}":
            embedding1 = json.loads(embedding1)
        if "class 'str'" in f"{type(embedding2)}":
            embedding2 = json.loads(embedding2)

        # Ensure embeddings are 1-D arrays
        embedding1 = np.array(embedding1).flatten()
        embedding2 = np.array(embedding2).flatten()

        if len(embedding1) != len(embedding2):
            return 0.0

        return 1 - cosine(embedding1, embedding2)
    except:
        return 0.0


sku_list_with_matched = []

category_similarity_score_theshold = 0.6


def find_top_matches_for_xianmu(sku_row: pd.Series):
    global sku_list_with_matched
    sku_name = sku_row["sku_name"]
    similarities = []
    for index, competitor_sku_row in biaoguo_x_xianmu_df.iterrows():
        category_similarity_score = calculate_cosine_similarity(
            sku_row["category_embeddings"], competitor_sku_row["category_embeddings"]
        )
        competitor_sku_name = competitor_sku_row["goods_name"]
        if category_similarity_score <= category_similarity_score_theshold:
            logging.warning(
                f"category_similarity_score太低了:{category_similarity_score},{sku_name}, {competitor_sku_name}"
            )
            continue
        similarity_score = calculate_cosine_similarity(
            sku_row["sku_embeddings"], competitor_sku_row["sku_embeddings"]
        )

        competitor_sku_row_dict = competitor_sku_row.to_dict()
        keys_to_remove = ["sku_embeddings", "category_embeddings"]
        for key in keys_to_remove:
            if key in competitor_sku_row_dict:
                del competitor_sku_row_dict[key]

        # 类目embedding + sku embedding分数
        similarities.append(
            (competitor_sku_row_dict, similarity_score + category_similarity_score)
        )

    # Sort the results based on similarity score in descending order
    sorted_similarities = sorted(similarities, key=lambda x: x[1], reverse=True)

    # Get the top N matches
    top_n_matches = [item[0] for item in sorted_similarities[:top_n]]
    logging.info(
        f"Matched rows for:{sku_row['sku_name']}, {[biaoguo.get('goods_name') for biaoguo in top_n_matches]}"
    )
    sku_row["top_matches"] = top_n_matches
    sku_list_with_matched.append(sku_row)
    return top_n_matches


two_month_ago = datetime.now() - timedelta(days=60)
two_month_ago = two_month_ago.strftime("%Y-%m-%d")

pop_sku_order_query = f"""
SELECT  a.sku_id
        ,a.spu_name AS pd_name
        ,CONCAT(a.category3,'>',a.category4) AS category
        ,SUM(CASE WHEN b.status IN (2,3,6) THEN b.amount * b.price ELSE 0.0 END) AS gmv
        ,COUNT(DISTINCT CASE WHEN b.status IN (2,3,6) THEN b.order_no END) AS order_cnt
        ,COALESCE(SUM(CASE WHEN b.status IN (2,3,6) THEN b.amount END),0) AS order_quantity
        ,MAX(b.create_time) as last_order_time
FROM    summerfarm_tech.dim_sku_df a
LEFT JOIN summerfarm_tech.ods_order_item_df b
ON      b.ds = MAX_PT('summerfarm_tech.ods_order_item_df')
AND     a.sku_id = b.sku
WHERE   a.ds = MAX_PT('summerfarm_tech.dim_sku_df')
AND     a.sub_type = 3
AND     a.category1 = '鲜果'
AND     a.spu_name not like  '%专用%'
AND     a.spu_name not like  '%测试%'
AND     a.spu_name not like  '%礼盒%'
GROUP BY a.sku_id
         ,a.spu_name
         ,category
HAVING  last_order_time >= '{two_month_ago} 00:00:00'
ORDER BY gmv DESC;
"""

pop_sku_order_df = get_odps_sql_result_as_df(pop_sku_order_query)
logging.info(f"鲜沐的sku order df shape:{pop_sku_order_df.shape}")

sku_list_df_top10 = pd.merge(
    left=sku_list_df,
    right=pop_sku_order_df,
    left_on="sku",
    right_on="sku_id",
    suffixes=["", "_b"],
)

sku_list_df_top10.sort_values(by=["gmv"], ascending=False, inplace=True)

sku_list_df_top10 = sku_list_df_top10.head(xianmu_sku_cnt_to_save)

sku_list_df_top10 = sku_list_df_top10.drop(
    columns=["pd_name_b", "sku_id"], errors="ignore"
)

sku_list_df_top10["top_matches"] = sku_list_df_top10.apply(
    find_top_matches_for_xianmu, axis=1
)

date_of_now = datetime.now().strftime("%Y-%m-%d")

# ## 使用语言模型（GPT3）做匹配

model = "gpt-4o-mini"

system_prompt = f"""
你是一个专业的商品匹配助手。你的任务是为我司的商品从对手品牌中找出最相似的{llm_top_n}个商品。

输入格式：
```
我司商品：
__XIANMU_PRODUCT__

对手商品列表：
__BIAOGUO_PRODUCTS__
```

在进行匹配时，请重点考虑以下因素：
1. 商品描述：考虑商品描述中的关键信息（如品种、产地、品牌）是否相近
2. 规格一致性：优先匹配相同规格（如同为"中果"）
3. 重量匹配度：比较商品的毛重是否接近
4. 价格相似度：比较商品的价格差异，在满足了商品描述一致、规格一致、重量一致的情况下，价格越接近越好，否则应该以以上标准为准.

输出要求：
1. 列出匹配到的前{llm_top_n}个对手的商品，按相似度从高到低排序
2. 对每个匹配项，说明选择理由，包括：
   - 相似度得分（0-100分）
   - 价格对比分析
   - 重量对比分析
   - 规格匹配说明
3. 输出格式举例：
```json
{{'匹配结果':[{{'序号':1,'对手商品ID':'123abc','相似度得分':80,'匹配理由':'匹配理由1','对手商品名称':'对手商品名称1','对手商品价格':'对手商品价格1':'对手商品价格1'}},{{第二匹配项...}},{{第三匹配项...}}]}}
```

注意事项：
1. 商品价格的细微差异可以接受，但差距不应超过30%。
2. 品种必须精确匹配，如‘库尔勒香梨’必须完全匹配‘库尔勒香梨’
3. 重量尽可能精确匹配，两个平台的包装规格可能不太一样。
4. 如果找不到完全匹配的规格，可以推荐相近规格的商品，但需要在理由中说明，且此时推荐分数不得超过75分。
5. 所有分析和推理过程必须基于输入信息中明确提供的数据
6. 仅需返回JSON内容即可，无须返回其他信息
"""

one_api_client = OpenAI(
    base_url="https://litellm-test.summerfarm.net/v1",
    api_key=os.getenv("XM_FAST_GPT_API_KEY"),
)


def get_top_matched_biaoguo_sku(xianmu_sku_specification: str, biaoguo_sku_to_match=[]):
    sku_to_match = "\n".join(biaoguo_sku_to_match)
    message_content = system_prompt.replace(
        "__XIANMU_PRODUCT__", xianmu_sku_specification
    )
    message_content = message_content.replace("__BIAOGUO_PRODUCTS__", sku_to_match)
    messages = [
        {
            "role": "user",
            "content": message_content,
        },
    ]
    completion = one_api_client.chat.completions.create(
        model="deepseek-v3",
        temperature=0.7,
        max_tokens=16384,
        messages=messages,
        response_format={"type": "json_object"},
    )

    response = completion.choices[0].message.content
    return response


import csv
import json
from io import StringIO


def matched_csv_to_json(json_str: str, competitor: str = ""):
    # 移除markdown格式的前缀和后缀
    json_str = json_str.replace("```json", "").replace("```", "").strip()

    try:
        # Convert the CSV string into a list of dictionaries
        json_object_arr = json.loads(json_str).get("匹配结果", [])
        json_data = []
        for row in json_object_arr:
            data = {}
            logging.info(f"row:{row}")
            data["rank"] = row.get("序号")
            data["competitorSkuCode"] = row.get("对手商品ID")
            similarity_score = row.get("相似度得分", 0)
            similarity_score = (
                similarity_score
                if isinstance(similarity_score, int)
                else int(similarity_score.replace("分", ""))
            )
            data["similarityScore"] = similarity_score
            data["matchingReason"] = row.get("匹配理由")
            data["competitorProductName"] = row.get("对手商品名称")
            data["competitorSkuPrice"] = row.get("对手商品价格")
            data["competitor"] = competitor
            json_data.append(data)

        # Convert list of dictionaries to JSON format
        return json.dumps(json_data, ensure_ascii=False, indent=2)
    except Exception as e:
        logging.info(f"转换到标准JSON时出错: {e}, json_str:{json_str}")
        return "[]"


import concurrent.futures
import time

def get_top_matched_by_llm(xianmu_sku):
    specification = f"skucode:{xianmu_sku['sku']}, {xianmu_sku['categoryName']}-{xianmu_sku['sku_name']}, 规格:{xianmu_sku['weight']}"
    specification = f"{specification}, 毛重:{xianmu_sku.get('weightNum', 0)*2}斤, 售价:¥{xianmu_sku['price']}"  # 净重:{xianmu_sku.get('netWeightNum', 0)*2}斤,
    top_matches = xianmu_sku["top_matches"]
    biaoguo_sku_to_match = []
    if len(top_matches) <= 0:
        logging.info(f"鲜沐SKU embedding算法匹配不到任何标果商品:{specification}")
        return "[]"
    for biaoguo_matched in top_matches:
        sku = f"skucode:{biaoguo_matched['skucode']}, {biaoguo_matched['categoryname']}-{biaoguo_matched['goods_name']}"
        sku = f"{sku}, 规格:{biaoguo_matched['specification']}, 毛重:{biaoguo_matched['grossweight']}斤, 售价:¥{biaoguo_matched['finalstandardprice']}"  # 净重:{biaoguo_matched['netweight']}斤,
        logging.info(f"{xianmu_sku['sku_name']}匹配到的标果SKU是：{sku}")
        biaoguo_sku_to_match.append(sku)
    start_time = time.time()
    matched_json = get_top_matched_biaoguo_sku(
        xianmu_sku_specification=specification,
        biaoguo_sku_to_match=biaoguo_sku_to_match,
    )
    end_time = time.time()
    logging.info(f"调用get_top_matched_biaoguo_sku耗时: {round(end_time - start_time, 1)}秒")
    logging.info(f"鲜沐SKU{specification}, top{top_n}:{matched_json}")
    matched_csv_json = matched_csv_to_json(
        matched_json, competitor=top_matches[0]["competitor"]
    )
    return json.dumps(matched_csv_json, ensure_ascii=False)

# 使用多线程处理匹配，线程数为5
def process_with_threads(df, func, max_workers=5):
    """
    使用多线程方式对DataFrame的每一行应用函数
    """
    results = [None] * len(df)
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_idx = {
            executor.submit(func, row): idx for idx, (_, row) in enumerate(df.iterrows())
        }
        for future in concurrent.futures.as_completed(future_to_idx):
            idx = future_to_idx[future]
            try:
                result = future.result()
            except Exception as exc:
                # 发生异常时，打印日志，并将结果置为空
                logging.info(f"线程处理第{idx}行时发生异常: {exc}")
                result = "[]"
            results[idx] = result
    return results

# 调用多线程函数，替代apply
sku_list_df_top10["top_matched_competitor_sku_list"] = process_with_threads(
    sku_list_df_top10, get_top_matched_by_llm, max_workers=5
)


from odps_client import write_pandas_df_into_odps

sku_list_to_save_df = sku_list_df_top10[
    [
        "sku",
        "weight",
        "sku_name",
        "pd_name",
        "top_matched_competitor_sku_list",
        "category",
        "gmv",
        "order_cnt",
        "order_quantity",
        "last_order_time",
    ]
]

partition_spec = f"ds={ds_to_run}"

write_pandas_df_into_odps(
    df=sku_list_to_save_df,
    table_name="summerfarm_ds.app_xianmu_top_matched_competitor_sku_list_df",
    partition_spec=partition_spec,
    overwrite=True,
)

from odps_client import get_odps_sql_result_as_df

ds_now = datetime.now().strftime("%Y%m%d")
ds_3days_ago = (datetime.now() - timedelta(days=3)).strftime("%Y%m%d")


def get_standard_df_by_sql_template(sql_template: str) -> pd.DataFrame:
    competitor_df = get_odps_sql_result_as_df(sql=sql_template.format(ds=ds_now))
    competitor_3d_df = get_odps_sql_result_as_df(
        sql=sql_template.format(ds=ds_3days_ago)
    )
    return merge_now_and_3d_data(
        competitor_df=competitor_df, competitor_3d_df=competitor_3d_df
    )


def merge_now_and_3d_data(
    competitor_df: pd.DataFrame, competitor_3d_df: pd.DataFrame
) -> pd.DataFrame:
    competitor_3d_df.rename(columns={"month_sale": "sales_volume_3d"}, inplace=True)
    competitor_df = pd.merge(
        competitor_df,
        competitor_3d_df[["sku_code", "sales_volume_3d"]],
        on="sku_code",
        how="left",
    )

    # Convert to numeric and handle errors
    competitor_df["month_sale"] = pd.to_numeric(
        competitor_df["month_sale"], errors="coerce"
    )
    competitor_df["sales_volume_3d"] = pd.to_numeric(
        competitor_df["sales_volume_3d"], errors="coerce"
    )

    # Fill NaNs with a default value (e.g., 0) or handle them as needed
    competitor_df["month_sale"].fillna(0, inplace=True)
    competitor_df["sales_volume_3d"].fillna(0, inplace=True)

    # Convert to integers
    competitor_df["month_sale"] = competitor_df["month_sale"].astype(int)
    competitor_df["sales_volume_3d"] = competitor_df["sales_volume_3d"].astype(int)

    # Perform the subtraction
    competitor_df["sales_volume_3d"] = (
        competitor_df["month_sale"] - competitor_df["sales_volume_3d"]
    )

    competitor_df.sort_values(by="sales_volume_3d", ascending=False, inplace=True)
    return competitor_df


# ### 标果-杭州


biaoguo_sql = """
SELECT  categoryname AS category_name,
        competitor,
        skucode AS sku_code,
        spider_fetch_time,
        goodssiphoncommissionrate AS goods_siphon_commission_rate,
        standardprice AS standard_price,
        finalstandardprice AS final_standard_price,
        monthsale AS month_sale,
        attachurlr AS url,
        sellersiphoncommissionrate AS seller_siphon_commission_rate,
        unitpricecatty AS unit_price_catty,
        unit,
        sellername AS seller_name,
        grossweight AS gross_weight,
        netweight AS net_weight,
        specification,
        goodsname AS goods_name,
        sevendayaftersale AS seven_day_after_sale
FROM(
            SELECT  *
                    ,RANK() OVER (PARTITION BY categoryname,goodsname ORDER BY monthsale DESC,finalstandardprice ASC,createtime DESC,spider_fetch_time DESC) AS rnk
            FROM    summerfarm_ds.spider_biaoguo_with_prop_product_result_df
            WHERE   ds = max_pt('summerfarm_ds.spider_biaoguo_with_prop_product_result_df')
            AND     competitor = '标果-杭州' 
            AND     categoryname like '水果_%'
    )
WHERE   rnk = 1
LIMIT   100000;
"""

biaoguo_df = get_standard_df_by_sql_template(biaoguo_sql)


# Concatenating DataFrames
# all_competitor_df = pd.concat([biaoguo_df, guosusong_df, fengguogong_df, yishengxianguo_df], ignore_index=True)
all_competitor_df = biaoguo_df

categories = all_competitor_df["category_name"].unique()

logging.info(f"所有的类目:{categories}")

# Filter rows where category_name is in fruit_categories
fruit_filtered_df = all_competitor_df

fruit_filtered_df["sku_embeddings"] = fruit_filtered_df.apply(
    get_competitor_like_biaoguo_embedding, axis=1
)
sku_list_df["sku_embeddings"] = sku_list_df.apply(get_xianmu_embedding, axis=1)

fruit_filtered_df["category_embeddings"] = fruit_filtered_df.apply(
    lambda row: get_category_embedding(f"{row['category_name']}"),
    axis=1,
)
sku_list_df["category_embeddings"] = sku_list_df.apply(
    lambda row: get_category_embedding(f"{row['categoryName']}"),
    axis=1,
)


# 将数据类型转换为数值类型
fruit_filtered_df["final_standard_price"] = pd.to_numeric(
    fruit_filtered_df["final_standard_price"], errors="coerce"
)
fruit_filtered_df["month_sale"] = pd.to_numeric(
    fruit_filtered_df["month_sale"], errors="coerce", downcast="integer"
)

# 计算月销售额和3天销售额
fruit_filtered_df["monthsale_gmv"] = fruit_filtered_df.apply(
    lambda row: row["month_sale"] * row["final_standard_price"], axis=1
)
fruit_filtered_df["monthsale_gmv_3d_ago"] = fruit_filtered_df.apply(
    lambda row: row["sales_volume_3d"] * row["final_standard_price"], axis=1
)

# 按照竞争对手、3天销售额和月销售额进行排序
fruit_filtered_df.sort_values(
    by=["competitor", "sales_volume_3d", "monthsale_gmv"],
    ascending=False,
    inplace=True,
)

if top_products_to_save <= 50:
    logging.warning(
        f"top_products_to_save is less than 50, which may cause the result to be not accurate.{top_products_to_save}"
    )

fruit_filtered_df = fruit_filtered_df.head(top_products_to_save)

all_competitor_to_xianmu_top_n = 10


## 把表格的商品映射到鲜沐上，标果的商品作为主键，和 def find_top_matches_for_xianmu() 的顺序是反过来的
def find_top_xianmu_matches_for_competitor(competitor_sku_row: pd.Series):
    similarities = []
    for index, xianmu_row in sku_list_df.iterrows():
        category_similarity_score = calculate_cosine_similarity(
            competitor_sku_row["category_embeddings"], xianmu_row["category_embeddings"]
        )
        if category_similarity_score <= category_similarity_score_theshold:
            continue
        similarity_score = calculate_cosine_similarity(
            competitor_sku_row["sku_embeddings"], xianmu_row["sku_embeddings"]
        )
        # 类目embedding + sku embedding分数
        xianmu_sku_row_dict = xianmu_row.to_dict()
        keys_to_remove = ["sku_embeddings", "category_embeddings"]
        for key in keys_to_remove:
            if key in xianmu_sku_row_dict:
                del xianmu_sku_row_dict[key]
        similarities.append(
            (xianmu_sku_row_dict, similarity_score + category_similarity_score)
        )
    if len(similarities) <= 0:
        logging.info(f"SKU:{competitor_sku_row.to_dict()} 未匹配到")
        return []
    elif len(similarities) <= 1:
        logging.info(f"匹配出来的结果很少，立即返回:{similarities[0][0]}")
        return [similarities[0][0]]

    # Sort the results based on similarity score in descending order
    sorted_similarities = sorted(similarities, key=lambda x: x[1], reverse=True)

    # Get the top N matches
    top_n_matches = [
        item[0] for item in sorted_similarities[:all_competitor_to_xianmu_top_n]
    ]
    logging.info(
        f"Matched rows for:{competitor_sku_row['goods_name']}, {[xianmu.get('sku_name') for xianmu in top_n_matches]}"
    )
    competitor_sku_row["top_matches"] = top_n_matches
    return top_n_matches


def safe_find_top_matches(row: pd.Series):
    try:
        top_xianmu_matches = find_top_xianmu_matches_for_competitor(row)
        if isinstance(top_xianmu_matches, list):
            return top_xianmu_matches
        else:
            logging.error(
                f"result of row:{row['goods_name']} is not an array:{top_xianmu_matches}"
            )
    except Exception as e:
        logging.error(
            f"Error processing row:{row['goods_name']}: {e}, {traceback.format_exc()}"
        )
    return []


fruit_filtered_df["top_matches"] = fruit_filtered_df.apply(
    safe_find_top_matches, axis=1
)

import pandasql

fruit_filtered_df["category_level2"] = (
    fruit_filtered_df["category_name"].str.split("/").str[0]
)

fruit_filtered_clean_df = fruit_filtered_df[
    [
        "sku_code",
        "goods_name",
        "category_name",
        "month_sale",
        "monthsale_gmv",
        "monthsale_gmv_3d_ago",
        "category_level2",
        "competitor",
    ]
]

static_df = pandasql.sqldf(
    """select category_name,competitor
    ,sum(monthsale_gmv) as category_monthsale_gmv 
    ,sum(monthsale_gmv_3d_ago) as category_3d_gmv 
    from fruit_filtered_clean_df 
    group by competitor,category_name"""
)

# Step 2: Merge total_gmv into fruit_filtered_df
fruit_filtered_with_category_gmv_df = fruit_filtered_df.merge(
    static_df[
        ["category_name", "competitor", "category_monthsale_gmv", "category_3d_gmv"]
    ],
    on=["category_name", "competitor"],
    how="left",
)

fruit_filtered_with_category_gmv_df["monthsale_gmv_percentile_of_category"] = round(
    100.00
    * fruit_filtered_with_category_gmv_df["monthsale_gmv"].astype(float)
    / fruit_filtered_with_category_gmv_df["category_monthsale_gmv"].astype(float),
    2,
)

fruit_filtered_with_category_gmv_df["monthsale_gmv_3d_ago_percentile_of_category"] = (
    round(
        100.00
        * fruit_filtered_with_category_gmv_df["monthsale_gmv_3d_ago"].astype(float)
        / fruit_filtered_with_category_gmv_df["category_3d_gmv"].astype(float),
        2,
    )
)


# Step 4: Rank each item within its category based on monthsale_gmv
fruit_filtered_with_category_gmv_df["category_rank"] = (
    fruit_filtered_with_category_gmv_df.groupby(["category_name", "competitor"])[
        "monthsale_gmv_3d_ago"
    ].rank(ascending=False)
)

fruit_filtered_top10_of_each_category_df = fruit_filtered_with_category_gmv_df[
    fruit_filtered_with_category_gmv_df["category_rank"] <= 10.0
]
fruit_filtered_top10_of_each_category_df = fruit_filtered_top10_of_each_category_df[
    fruit_filtered_top10_of_each_category_df["category_monthsale_gmv"] > 1000.00
]


from datetime import datetime, timedelta

# ### 使用LLM进行匹配


import csv
import json
from io import StringIO


def matched_csv_to_xianmu_json(json_str: str, competitor: str = ""):
    # 移除markdown格式的前缀和后缀
    json_str = json_str.replace("```json", "").replace("```", "").strip()

    try:
        # Convert the CSV string into a list of dictionaries
        json_obj_arr = json.loads(json_str).get("匹配结果", [])
        json_data = []
        for row in json_obj_arr:
            data = {}
            logging.info(f"row:{row}")
            data["rank"] = row.get("序号")
            data["xianmuSkuCode"] = row.get("对手商品ID")
            similarity_score = row.get("相似度得分", 0)
            similarity_score = (
                similarity_score
                if isinstance(similarity_score, int)
                else int(similarity_score.replace("分", ""))
            )
            data["similarityScore"] = similarity_score
            data["matchingReason"] = row.get("匹配理由")
            data["xianmuProductName"] = row.get("对手商品名称")
            data["xianmuSkuPrice"] = row.get("对手商品价格")
            json_data.append(data)

        # Convert list of dictionaries to JSON format
        return json.dumps(json_data, ensure_ascii=False, indent=2)
    except Exception as e:
        logging.error(f"转换到标准JSON时出错: {e}, json_str:{json_str}")
        return "[]"


import time
import concurrent.futures

def get_top_matched_xianmu_sku_by_llm(competitor_sku: pd.Series):
    """
    使用LLM匹配鲜沐SKU
    """
    specification = f"skucode:{competitor_sku['sku_code']}, {competitor_sku['category_name']}-{competitor_sku['goods_name']}, 规格:{competitor_sku['specification']}"
    specification = f"{specification}, 毛重:{competitor_sku.get('grossweight', 0)}斤, 售价:¥{competitor_sku['final_standard_price']}"  # 净重:{competitor_sku.get('netweight', 0)}斤,
    top_matches = competitor_sku["top_matches"]
    logging.info(f"标果SKU:{specification}")
    biaoguo_sku_to_match = []
    if not isinstance(top_matches, list) or len(top_matches) <= 0:
        logging.info(f"对手的SKU embedding算法匹配不到任何鲜沐商品:{specification}")
        return "[]"
    for xianmu_matched in top_matches:
        sku = f"skucode:{xianmu_matched['sku']}, {xianmu_matched['categoryName']}-{xianmu_matched['sku_name']}"
        sku = f"{sku}, 规格:{xianmu_matched['weight']}, 毛重:{xianmu_matched['weightNum']*2}斤, 售价:¥{xianmu_matched['price']}"  # 净重:{xianmu_matched['netWeightNum']*2}斤,
        logging.info(f"匹配到的鲜沐SKU:{sku}")
        biaoguo_sku_to_match.append(sku)

    start_time = time.time()
    matched_json = get_top_matched_biaoguo_sku(
        xianmu_sku_specification=specification,
        biaoguo_sku_to_match=biaoguo_sku_to_match,
    )
    end_time = time.time()
    logging.info(f"调用get_top_matched_biaoguo_sku耗时: {round(end_time - start_time, 1)}秒")
    logging.info(f"标果SKU:{specification}, top{top_n}:{matched_json}")
    matched_csv_json = matched_csv_to_xianmu_json(matched_json)
    return json.dumps(matched_csv_json, ensure_ascii=False)

def process_with_threads(df, func, max_workers=5):
    """
    使用多线程方式对DataFrame的每一行应用函数
    """
    results = [None] * len(df)
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_idx = {
            executor.submit(func, row): idx for idx, (_, row) in enumerate(df.iterrows())
        }
        for future in concurrent.futures.as_completed(future_to_idx):
            idx = future_to_idx[future]
            try:
                result = future.result()
            except Exception as exc:
                logging.info(f"线程处理第{idx}行时发生异常: {exc}")
                result = "[]"
            results[idx] = result
    return results

fruit_filtered_df.sort_values(
    by=["monthsale_gmv_3d_ago", "monthsale_gmv"], ascending=False, inplace=True
)
fruit_filtered_df_top_sales = fruit_filtered_df

fruit_filtered_df_top_sales["top_matched_xianmu_sku_list"] = process_with_threads(
    fruit_filtered_df_top_sales, get_top_matched_xianmu_sku_by_llm, max_workers=5
)


# Split category_name into category1,category2,category3
fruit_filtered_df_top_sales[["category1", "category2", "category3"]] = (
    fruit_filtered_df_top_sales["category_name"].str.split("_", expand=True)
)
fruit_filtered_df_top_sales["category3"] = fruit_filtered_df_top_sales[
    "category3"
].fillna("")

fruit_filtered_df_to_save = fruit_filtered_df_top_sales[
    [
        "category_name",
        "category1",
        "category2",
        "category3",
        "competitor",
        "sku_code",
        "spider_fetch_time",
        "final_standard_price",
        "month_sale",
        "url",
        "gross_weight",
        "net_weight",
        "specification",
        "goods_name",
        "sales_volume_3d",
        "monthsale_gmv",
        "category_level2",
        "monthsale_gmv_3d_ago",
        "top_matched_xianmu_sku_list",
    ]
]


write_pandas_df_into_odps(
    df=fruit_filtered_df_to_save,
    table_name="summerfarm_ds.app_xianmu_biaoguo_top_sale_sku_df",
    partition_spec=partition_spec,
    overwrite=True,
)

category_name_list = list(fruit_filtered_df_to_save["category_name"].unique())
logging.info(f"解析的标果类目:{','.join(category_name_list)}")

# 获取top5类目，根据每个类目加总 sales_volume_3d 进行倒序排序
top5_category_name_list = (
    fruit_filtered_df_to_save.groupby("category_name")["sales_volume_3d"]
    .sum()
    .sort_values(ascending=False)
    .index[:5]
    .tolist()
)
logging.info(f"top5类目:{','.join(top5_category_name_list)}")

time_cost = datetime.now() - started_at
hours, remainder = divmod(time_cost.total_seconds(), 3600)
minutes, seconds = divmod(remainder, 60)
msg = f"""- 开始时间:{started_at.strftime('%Y%m%d %H:%M:%S')}
- 结束时间:{datetime.now().strftime('%Y%m%d %H:%M:%S')}
- 总耗时:{int(hours)}时{int(minutes)}分{int(seconds)}秒"""
msg = f"- 共解析了{len(fruit_filtered_df_to_save)}个标果top sale商品\n{msg}"
msg = f"- 共匹配了{len(sku_list_to_save_df)}个鲜沐商品到标果商品\n{msg}"
msg = f"- top5标果类目（按sales_volume_3d倒序）：{','.join(top5_category_name_list)}\n{msg}"

send_markdown_to_feishu(
    f"匹配鲜沐和标果商品任务完成:ds={ds_to_run}",
    msg,
    feishu_token=feishu_token,
)
