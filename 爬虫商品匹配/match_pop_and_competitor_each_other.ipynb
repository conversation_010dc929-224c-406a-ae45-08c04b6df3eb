{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 鲜果POP商品和标果之间匹配和比较\n", "\n", "## 先登录admin"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "\n", "sys.path.append(\"../\")\n", "\n", "# 获取token\n", "import requests\n", "from datetime import datetime, timedelta\n", "import os\n", "\n", "login_url = \"https://admin.summerfarm.net/authentication/auth/username/login\"\n", "login_data = {\n", "    \"username\": \"<EMAIL>\",\n", "    \"password\": os.getenv(\"XIANMU_ADMIN_PASSWORD\"),\n", "}\n", "\n", "token = requests.post(login_url, data=login_data).json()\n", "\n", "print(token)\n", "\n", "\n", "headers = {\n", "    \"token\": token.get(\"data\").get(\"token\"),\n", "    \"xm-rqid\": \"create_fake_merchant_tp\",\n", "    \"xm-uid\": \"2047\",\n", "    \"Content-Type\": \"application/json;charset=UTF-8\",\n", "}\n", "\n", "print(headers)\n", "\n", "ds_to_run = datetime.now().strftime(\"%Y%m%d\")\n", "DATA_PATH = f\"./data/pop_{ds_to_run}\"\n", "\n", "from odps_client import create_directory_if_not_exists\n", "\n", "create_directory_if_not_exists(DATA_PATH)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_product_info(pd_id=11087):\n", "    url = \"https://admin.summerfarm.net/sf-mall-manage/product/query/info\"\n", "    data = {\"pdId\": pd_id}\n", "    return requests.post(url=url, headers=headers, json=data).json().get(\"data\")\n", "\n", "\n", "# Define the 'CASE WHEN' logic\n", "def open_sale_case_when(open_sale=-1):\n", "    if open_sale == 0:\n", "        return \"上架\"\n", "    elif open_sale == 1:\n", "        return \"有库存时上架\"\n", "    elif open_sale == 2:\n", "        return \"定时上架\"\n", "    elif open_sale == 3:\n", "        return \"有库存时上架(永久生效)\"\n", "    else:\n", "        return \"未定义\"\n", "\n", "\n", "pd_list = []\n", "page_index = 1\n", "page_size = 500\n", "while True:\n", "    response = (\n", "        requests.get(\n", "            f\"https://admin.summerfarm.net/sf-mall-manage/product/query/selectPage?outdated=0&subType=5&type=2&pageIndex={page_index}&pageSize={page_size}\",\n", "            headers=headers,\n", "        )\n", "        .json()\n", "        .get(\"data\")\n", "    )\n", "\n", "    current_page_list = response.get(\"list\", [])\n", "    pd_list.extend(current_page_list)\n", "\n", "    # 如果返回的列表数量小于500，说明已经获取完所有数据\n", "    if len(current_page_list) < page_size:\n", "        break\n", "\n", "    page_index += 1\n", "\n", "print(f\"获取到的商品个数{len(pd_list)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 构建鲜沐的类目树"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["url = \"https://admin.summerfarm.net/category\"\n", "all_xianmu_category = requests.get(url, headers=headers).json().get(\"data\")\n", "all_xianmu_category_pop = []\n", "for cate in all_xianmu_category:\n", "    if \"POP\" in cate[\"category\"]:\n", "        all_xianmu_category_pop.extend(cate.get(\"categoryList\"))\n", "\n", "print(\n", "    f\"all_xianmu_category:{all_xianmu_category}\",\n", "    f\"all_xianmu_category_pop:{all_xianmu_category_pop}\",\n", ")\n", "\n", "all_xianmu_category_pop_map = {}\n", "for second_level in all_xianmu_category_pop:\n", "    second_category_name = second_level.get(\"category\", \"\")\n", "    for leaf_level in second_level.get(\"categoryList\", []):\n", "        all_xianmu_category_pop_map[f\"{leaf_level.get('id','haha')}\"] = (\n", "            f\"{second_category_name}/{leaf_level['category']}\"\n", "        )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 从鲜沐admin后台获取所有的POP商品"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import json\n", "import pandas as pd\n", "\n", "\n", "def extract_product_fields(json_data):\n", "    # Initialize a list to hold the extracted data\n", "    extracted_data = []\n", "    keyValueList = \",\".join(\n", "        [\n", "            f\"{kv['name']}:{kv['productsPropertyValue']}\"\n", "            for kv in json_data.get(\"keyValueList\", [])\n", "        ]\n", "    )\n", "\n", "    # Loop through each item in the inventory detail list\n", "    for item in json_data.get(\"inventoryDetailVOS\", []):\n", "        # Concatenate saleValueList\n", "        sale_value_list = item.get(\"saleValueList\", [])\n", "        buyer_name = item.get(\"buyerName\", \"\")\n", "        sale_value_string = \",\".join(\n", "            f\"{sv['name']}:{sv['productsPropertyValue']}\" for sv in sale_value_list\n", "        )\n", "        # Loop through each area SKU in the item\n", "        for area_sku in item.get(\"areaSkuVOS\", []):\n", "            # Extract the necessary fields and create a dictionary\n", "            if not area_sku.get(\"onSale\", False):\n", "                print(f'SKU未上架:{json_data.get(\"pdName\", \"\")}, {area_sku}')\n", "                continue\n", "            picturePath = json_data.get(\"picturePath\", \"404.jpg\")\n", "            picture = (\n", "                picturePath\n", "                if picturePath.startswith(\"http\")\n", "                else \"https://azure.summerfarm.net/\" + picturePath\n", "            )\n", "            extracted_dict = {\n", "                \"sku\": area_sku.get(\"sku\", \"\"),\n", "                \"categoryName\": all_xianmu_category_pop_map.get(\n", "                    f'{json_data.get(\"categoryId\")}',\n", "                    f'{json_data.get(\"categoryId\")}找不到类目嘞!',\n", "                ),\n", "                \"openSale\": open_sale_case_when(area_sku.get(\"openSale\", -1)),\n", "                \"onSale\": \"上架中\" if area_sku.get(\"onSale\", False) else \"下架(没库存)\",\n", "                \"pdId\": item.get(\"pdId\", \"\"),\n", "                \"sku_name\": json_data.get(\"productName\", \"\"),\n", "                \"pd_name\": json_data.get(\"pdName\", \"\"),\n", "                \"realName\": item.get(\"realName\", \"\"),\n", "                \"picture\": picture,\n", "                \"price\": area_sku.get(\"price\", 0),\n", "                \"supplyPrice\": json.loads(item.get(\"createRemark\", \"{}\")).get(\n", "                    \"supplyPrice\", 0\n", "                ),\n", "                \"areaNo\": area_sku.get(\"areaNo\", \"\"),\n", "                \"areaName\": area_sku.get(\"areaName\", \"\"),\n", "                \"netWeightNum\": item.get(\"netWeightNum\", 0),\n", "                \"weightNum\": item.get(\"weightNum\", 0),\n", "                \"weight\": item.get(\"weight\", \"\"),\n", "                \"sku_spec\": sale_value_string,\n", "                \"spu_spec\": keyValueList,\n", "                \"buyer_name\": buyer_name,\n", "            }\n", "            # Add the dictionary to the list\n", "            extracted_data.append(extracted_dict)\n", "\n", "    return extracted_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "product_list = []\n", "\n", "for product in pd_list:\n", "    product = get_product_info(product[\"pdId\"])\n", "    product_list.append(product)\n", "\n", "sku_list = []\n", "\n", "for product in product_list:\n", "    extracted_data = extract_product_fields(product)\n", "    sku_list.extend(extracted_data)\n", "\n", "sku_list_df = pd.DataFrame(sku_list)\n", "sku_list_df.to_csv(\n", "    f\"{DATA_PATH}/鲜果POP全部SKU_{datetime.now().strftime('%Y%m%d')}.csv\", index=False\n", ")\n", "sku_list_df.drop_duplicates(subset=[\"sku\"], inplace=True)\n", "sku_list_df.sort_values(by=[\"categoryName\", \"pd_name\"], inplace=True)\n", "sku_list_df.head(2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 获取标果的爬虫数据（当天）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from odps_client import get_odps_sql_result_as_df\n", "\n", "all_fruit_category=['千禧樱桃小番茄','红富士苹果','水蜜桃','25号小蜜','冬枣','更多桃','麒麟西瓜','更多梨','更多苹果','更多李']\n", "all_fruit_category.extend(['柠檬','果篮','国产油桃','山竹','巨峰葡萄','阳光玫瑰','进口橙','凯特芒','国产红心火龙果','更多凤梨','更多提子'])\n", "all_fruit_category.extend(['进口红心火龙果','更多蜜瓜','蜂糖李','青脆李','更多柚子','水仙芒','玉菇甜瓜','百香果','更多榴莲','木瓜/杨桃','椰青'])\n", "all_fruit_category.extend(['金枕榴莲','人参果/释迦果','更多龙眼','黄桃','红肉菠萝蜜','三红蜜柚','台芒','吊干杏','无籽红提','更多葡萄','网纹蜜瓜','蜜桔'])\n", "all_fruit_category.extend(['赣南脐橙','黑布林','夏黑葡萄','普通红提','更多柑','蓝莓','西州蜜瓜','更多芒果','水果黄瓜','火腿肠','牛油果','硒砂瓜'])\n", "all_fruit_category.extend(['莲雾/芭乐','其他桔','圆红/血橙','更多橙','白心火龙果','皇冠梨','红布林','进口车厘子','更多荔枝','更多莓','桂味','特小凤','蛇果'])\n", "all_fruit_category.extend(['贡梨','贵妃芒','更多西瓜','白心蜜柚','秋月梨','绿心猕猴桃','羊角蜜','雪莲果/马蹄果','黄元帅苹果','冷冻畜禽食品','更多枣','水产品'])\n", "all_fruit_category.extend(['火参果','红心蜜柚','红毛丹','脆柿','软籽石榴','青芒','鲜山楂','鹰嘴芒'])\n", "\n", "biaoguo_df=get_odps_sql_result_as_df(f\"\"\"\n", "SELECT  categoryname,backcategoryname,id,competitor,skucode,spider_fetch_time,goodspropdetaillist,createtime,goodssiphoncommissionrate\n", "        ,standardprice,finalstandardprice,lasttimestandardprice,finalunitpricecatty,monthsale,attachurlr AS url,sellersiphoncommissionrate\n", "        ,unitpricecatty,unit,sellername,grossweight,netweight,specification,babyname,goodsname,goodstype,sevendayaftersale\n", "FROM    (\n", "            SELECT  *\n", "                    ,RANK() OVER (PARTITION BY id,skucode ORDER BY spider_fetch_time DESC ) AS rnk\n", "            FROM    summerfarm_ds.spider_biaoguo_with_prop_product_result_df\n", "            WHERE   ds = MAX_PT('summerfarm_ds.spider_biaoguo_with_prop_product_result_df')\n", "            AND     competitor = '标果-杭州' \n", "            AND     categoryname like '%/%'\n", "            -- AND     split_part(categoryname,'/',2) in ('{\"','\".join(all_fruit_category)}')\n", "        ) \n", "WHERE   rnk = 1\n", "LIMIT   100000;\n", "\"\"\")\n", "\n", "print(f\"标果的商品数:{len(biaoguo_df)}\")\n", "biaoguo_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(f\"标果的所有类目: \\n{biaoguo_df['categoryname'].unique()}\")\n", "\n", "category_list_that_xianmu_wont_sale = set(\n", "    [\n", "        \"即食食品/方便罐头\",\n", "        \"即食食品/海苔食品\",\n", "        \"饼干糕点/饼干\",\n", "        \"方便食品/方便速食\" \"糖果/巧克力/果冻布丁/果冻/布丁\",\n", "        \"刀具工具/刀具工具\",\n", "        \"包装配饰/包装配饰\",\n", "        \"水果盒/带盖盒\",\n", "        \"陈列道具/陈列道具\" \"水果盒/托盒\",\n", "        \"保鲜膜/保鲜膜\",\n", "        \"标签贴纸/标签贴纸\",\n", "        \"糖果/巧克力/果冻布丁/龟苓膏/果膏\" \"即食食品/肉干肉铺\",\n", "        \"坚果炒货/坚果\",\n", "        \"其他标品/营养保健\",\n", "        \"糖果/巧克力/果冻布丁/其他糖果\",\n", "        \"饼干糕点/散装食品\" \"酒水/饮料/冲调\",\n", "        \"即食食品/其他即食\",\n", "        \"酒水/饮料/酒类\",\n", "        \"饼干糕点/糕点\",\n", "        \"标果奶品/标果奶品\" \"即食食品/蔬菜干/豆干\",\n", "        \"糖果蜜饯/果干蜜饯\",\n", "        \"即食食品/火腿肠\",\n", "        \"水果袋/水果袋\" \"氛围牌/氛围牌\",\n", "        \"设计定制/设计定制\",\n", "        \"水果盒/瓶罐\",\n", "        \"粽子/米类/面类/粽子/米类/面类\",\n", "        \"洗护用品/洗护用品\",\n", "        \"鲜花/鲜花\",\n", "        \"水产/水产品\",\n", "        \"即食食品/卤味即食\",\n", "    ]\n", ")\n", "\n", "print(f\"这些类目被过滤掉了:{category_list_that_xianmu_wont_sale}\")\n", "\n", "# Fix the filtering by using 'isin' method\n", "\n", "# biaoguo_x_xianmu_df = biaoguo_df[\n", "#     ~biaoguo_df[\"categoryname\"].isin(category_list_that_xianmu_wont_sale)\n", "# ].copy()\n", "\n", "biaoguo_x_xianmu_df = biaoguo_df\n", "\n", "# Get the length of the filtered DataFrame\n", "print(f\"过滤后的标果商品个数:{len(biaoguo_x_xianmu_df)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Azure客户端定义"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import os\n", "from openai import AzureOpenAI\n", "import httpx\n", "\n", "client = AzureOpenAI(\n", "    # https://esat-us.openai.azure.com/openai/deployments/text-embedding-3-small/embeddings?api-version=2023-05-15\n", "    api_version=\"2023-07-01-preview\",\n", "    azure_endpoint=\"https://esat-us.openai.azure.com\",\n", "    api_key=os.getenv(\"AZURE_API_KEY_XM\", \"please set:AZURE_API_KEY_XM\"),\n", "    http_client=httpx.Client(proxies={\"http://\": None, \"https://\": None}),\n", ")\n", "\n", "embedding_model = os.getenv(\"AZURE_EMBEDDING_MODEL\", \"text-embedding-3-small\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 使用embedding模型做标题的匹配"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["import sqlite3\n", "import os\n", "\n", "# Ensure the directory exists\n", "os.makedirs(os.path.expanduser(\"~/sqlite\"), exist_ok=True)\n", "# Path to the database\n", "db_path = os.path.expanduser(\"~/sqlite/embeddings.db\")\n", "conn = sqlite3.connect(db_path)\n", "cursor = conn.cursor()\n", "\n", "# Create table if it doesn't exist\n", "cursor.execute(\n", "    \"\"\"\n", "CREATE TABLE IF NOT EXISTS embeddings (\n", "    input_text TEXT PRIMARY KEY,\n", "    embedding TEXT\n", ")\n", "\"\"\"\n", ")\n", "\n", "\n", "def get_embedding_directly_from_azure(input: str):\n", "    embbed = client.embeddings.create(model=embedding_model, input=input)\n", "    return embbed.to_dict().get(\"data\", [{}])[0].get(\"embedding\")\n", "\n", "\n", "def get_embedding(input_text):\n", "\n", "    # Check if input text already exists in the database\n", "    cursor.execute(\n", "        \"SELECT embedding FROM embeddings WHERE input_text = ?\", (input_text,)\n", "    )\n", "    result = cursor.fetchone()\n", "\n", "    if result:\n", "        embedding = result[0]\n", "        print(\n", "            f\"Found, return the embedding of input_text:{input_text}, {embedding[:50]}\"\n", "        )\n", "    else:\n", "        print(f\"Not found, call the OpenAI API to get the embedding:{input_text}\")\n", "        embedding = str(get_embedding_directly_from_azure(input_text))\n", "\n", "        # Insert the new input text and embedding into the database\n", "        cursor.execute(\n", "            \"INSERT INTO embeddings (input_text, embedding) VALUES (?, ?)\",\n", "            (input_text, embedding),\n", "        )\n", "        conn.commit()\n", "    return embedding\n", "\n", "\n", "# Example usage\n", "# input_text = \"你好\"\n", "# embedding = get_embedding(input_text)\n", "# print(json.loads(embedding))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def get_xianmu_embedding(row: pd.Series):\n", "    sku_name = row[\"sku_name\"]\n", "    if sku_name is None or len(f\"{sku_name}\") <= 2:\n", "        sku_name = row[\"pd_name\"]\n", "    input_text = f\"{sku_name}\"\n", "    return get_embedding(input_text=input_text)\n", "\n", "\n", "def get_competitor_like_biaoguo_embedding(row: pd.Series):\n", "    goodsname = row[\"goods_name\"]\n", "    return get_embedding(input_text=f\"{goodsname}\")\n", "\n", "\n", "def get_category_embedding(category: str):\n", "    return get_embedding(input_text=category)\n", "\n", "\n", "biaoguo_x_xianmu_df.rename(columns={\"goodsname\": \"goods_name\"}, inplace=True, errors=\"ignore\")\n", "\n", "biaoguo_x_xianmu_df[\"sku_embeddings\"] = biaoguo_x_xianmu_df.apply(\n", "    get_competitor_like_biaoguo_embedding, axis=1\n", ")\n", "biaoguo_x_xianmu_df[\"category_embeddings\"] = (\n", "    biaoguo_x_xianmu_df.apply(lambda row: f\"{row['categoryname']}\", axis=1)\n", ").apply(get_category_embedding)\n", "\n", "sku_list_df[\"sku_embeddings\"] = sku_list_df.apply(get_xianmu_embedding, axis=1)\n", "sku_list_df[\"category_embeddings\"] = (\n", "    sku_list_df.apply(lambda row: f\"{row['categoryName']}\", axis=1)\n", ").apply(get_category_embedding)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import concurrent\n", "from concurrent.futures import ThreadPoolExecutor\n", "import numpy as np\n", "import traceback\n", "from scipy.spatial.distance import cosine\n", "\n", "\n", "top_n = 20\n", "\n", "\n", "def calculate_cosine_similarity(embedding1, embedding2):\n", "    if \"class 'str'\" in f\"{type(embedding1)}\":\n", "        embedding1 = json.loads(embedding1)\n", "    if \"class 'str'\" in f\"{type(embedding2)}\":\n", "        embedding2 = json.loads(embedding2)\n", "    # print(len(embedding1), len(embedding2), type(embedding2))\n", "    return 1 - cosine(embedding1, embedding2)\n", "\n", "\n", "sku_list_with_matched = []\n", "\n", "category_similarity_score_theshold = 0.6\n", "\n", "\n", "def find_top_matches_for_xian<PERSON>(sku_row: pd.Series):\n", "    global sku_list_with_matched\n", "    # print(f\"finding top match rows for:{sku_row['sku_name']}\")\n", "    sku_name = sku_row[\"sku_name\"]\n", "    similarities = []\n", "    for index, competitor_sku_row in biaoguo_x_xianmu_df.iterrows():\n", "        category_similarity_score = calculate_cosine_similarity(\n", "            sku_row[\"category_embeddings\"], competitor_sku_row[\"category_embeddings\"]\n", "        )\n", "        competitor_sku_name = competitor_sku_row[\"goods_name\"]\n", "        if category_similarity_score <= category_similarity_score_theshold:\n", "            # 如果类目的相似度小于0.88，则不需要再考虑名字的embedding\n", "            # print(\n", "            #     f\"类目的相似度小于{category_similarity_score_theshold}:{category_similarity_score}\",\n", "            #     f'{competitor_sku_row[\"competitor\"]}类目:{competitor_sku_row[\"categoryname\"]}, 鲜沐类目:{sku_row[\"categoryName\"]}',\n", "            #     f\"鲜沐SKU:{sku_name}, 标果SKU:{competitor_sku_name}\",\n", "            # )\n", "            continue\n", "        # print(f\"matching: {competitor_sku_row['goods_name']}\")\n", "        similarity_score = calculate_cosine_similarity(\n", "            sku_row[\"sku_embeddings\"], competitor_sku_row[\"sku_embeddings\"]\n", "        )\n", "\n", "        competitor_sku_row_dict = competitor_sku_row.to_dict()\n", "        keys_to_remove = [\"sku_embeddings\", \"category_embeddings\"]\n", "        for key in keys_to_remove:\n", "            if key in competitor_sku_row_dict:\n", "                del competitor_sku_row_dict[key]\n", "\n", "        # 类目embedding + sku embedding分数\n", "        similarities.append(\n", "            (competitor_sku_row_dict, similarity_score + category_similarity_score)\n", "        )\n", "\n", "    # Sort the results based on similarity score in descending order\n", "    sorted_similarities = sorted(similarities, key=lambda x: x[1], reverse=True)\n", "\n", "    # Get the top N matches\n", "    top_n_matches = [item[0] for item in sorted_similarities[:top_n]]\n", "    print(\n", "        f\"Matched rows for:{sku_row['sku_name']}, {[biaoguo.get('goods_name') for biaoguo in top_n_matches]}\"\n", "    )\n", "    sku_row[\"top_matches\"] = top_n_matches\n", "    sku_list_with_matched.append(sku_row)\n", "    return top_n_matches"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sku_list_df_top10 = sku_list_df.head(5000)\n", "sku_list_df_top10[\"top_matches\"] = sku_list_df_top10.apply(\n", "    find_top_matches_for_xianmu, axis=1\n", ")\n", "\n", "# sku_list_df_top10[[\"top_matches\", \"sku_name\"]]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 写入到HTML文件"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.core.display import HTML\n", "from datetime import datetime\n", "\n", "css = \"\"\"\n", "<link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/css/bootstrap.min.css\" integrity=\"sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm\" crossorigin=\"anonymous\">\n", "<style type=\\\"text/css\\\">\n", "table {\n", "    color: #333;\n", "    font-family: unset;\n", "    font-size: 12px;\n", "    line-height: 1.5;\n", "    width: 1024px;\n", "    border-collapse:\n", "    collapse; \n", "    border-spacing: 0;\n", "    font-family: \"SF Pro SC\", \"SF Pro Text\", \"SF Pro Icons\", \"PingFang SC\", \"Helvetica Neue\", \"Helvetica\", \"Arial\", sans-serif;\n", "}\n", "\n", "tr{\n", "    border-bottom: 1px solid #C1C3D1;\n", "}\n", "\n", "tr:nth-child(even) {\n", "    background-color: #F8F8F8;\n", "}\n", "\n", "td, th {\n", "    /* border: 1px solid transparent; No more visible border */\n", "    height: 30px;\n", "}\n", "\n", "th {\n", "    background-color: #DFDFDF; /* Darken header a bit */\n", "    font-weight: bolder;\n", "    min-width: 100px;\n", "    text-align: center;\n", "}\n", "\n", "td {\n", "    /* background-color: #FAFAFA;\n", "    text-align: center; */\n", "}\n", "\n", "ol li{\n", "    text-align: left;\n", "}\n", ".biaoguo-container {\n", "    display:flex;\n", "}\n", ".biaoguo-goods-item{\n", "    padding-right: 0.3vw;\n", "}\n", ".no-list-style ul{\n", "    padding-left: 0;\n", "}\n", ".no-list-style li{\n", "    list-style: none;\n", "    font-size: smaller;\n", "}\n", ".no-list-style li.sku-title{\n", "    font-weight:bolder;\n", "}\n", ".xianmu-container{\n", "    /* border-right: 1px solid lightblue; */\n", "}\n", "\n", "img {\n", "    cursor: pointer;\n", "}\n", "\n", "img.enlarged {\n", "    position: fixed;\n", "    top: 50%;\n", "    left: 50%;\n", "    transform: translate(-50%, -50%);\n", "    z-index: 1000;\n", "    max-width: 80%;\n", "    max-height: 80%;\n", "    border: 1px solid black;\n", "    background-color: white;\n", "    padding: 10px;\n", "    display: none; /* Initially hidden */\n", "}\n", "\n", "img:hover + .enlarged {\n", "    display: block; /* Show on hover */\n", "}\n", "\n", "</style>\n", "\"\"\"\n", "\n", "\n", "def display_xianmu_html(row):\n", "    img = row[\"picture\"]\n", "    unit_price = round(row[\"price\"] / row.get(\"weightNum\", 0) / 2, 2)\n", "    content = f\"\"\"<div class=\"xianmu-container no-list-style\">\n", "    <img width=\"80\" src=\"{img}\">\n", "    <img class=\"enlarged\" src=\"{img}\" alt=\"Enlarged Image\">\n", "    <ul>\n", "    <li class=\"sku-title\">{row['sku']}, {row[\"sku_name\"]}</li>\n", "    <li>鲜沐售价: ¥{row[\"price\"]}</li>\n", "    <li>单价(斤,毛重): ¥{unit_price}</li>\n", "    <li>提报价: ¥{row['supplyPrice']}</li>\n", "    <li>类目:{row.get('categoryName','')}</li>\n", "    <li>规格:{row[\"weight\"]}</li>\n", "    <li>毛重:{row.get('weightNum', 0)*2}斤, 净重:{row.get('netWeightNum', 0)*2}斤</li>\n", "    <li>买手:{row[\"buyer_name\"]}</li>\n", "    <li>是否上架:{row[\"onSale\"]}</li>\n", "    </ul></div>\"\"\"\n", "    return content.replace(\"\\n\", \"\")\n", "\n", "\n", "display_count = 10\n", "\n", "\n", "def display_matched_biaoguo_html(row):\n", "    top_matches = row[\"top_matches\"]\n", "    contents_of_single_item = []\n", "    for index, item in enumerate(top_matches):\n", "        if index >= display_count:\n", "            # print(f\"超过了配置数量:{display_count},跳过\")\n", "            break\n", "        img = item[\"url\"]\n", "        finalunitpricecatty = round(\n", "            float(item[\"finalstandardprice\"]) / float(item[\"grossweight\"]), 2\n", "        )\n", "\n", "        single_item = f\"\"\"<div class=\"biaoguo-goods-item no-list-style\">\n", "        <img width=\"80\" src=\"{img}\">\n", "        <img class=\"enlarged\" src=\"{img}\" alt=\"Enlarged Image\">\n", "        <ul>\n", "        <li class=\"sku-title\">{item['skucode']}, {item['goods_name']}, {item['specification']}</li>\n", "        <li>标果价格: ¥{item['finalstandardprice']}</li>\n", "        <li>单价(斤,毛重): ¥{finalunitpricecatty}</li>\n", "        <li>类目:{item['categoryname']}</li>\n", "        <li>毛重:{item['grossweight']}斤, 净重:{item['netweight']}斤</li>\n", "        <li>卖家:{item.get('sellername','--')}</li>\n", "        <li>商品抽佣:{item.get('goodssiphoncommissionrate',0)}%</li>\n", "        <li>向卖家抽佣:{item.get('sellersiphoncommissionrate',0)}%</li>\n", "        <li>月销量:{item.get('monthsale',0)}, 7日售后:{item.get('sevendayaftersale',0)}</li>\n", "        </ul></div>\"\"\"\n", "        contents_of_single_item.append(single_item)\n", "\n", "    return f\"\"\"<div class=\"biaoguo-container\">{''.join(contents_of_single_item)}</div>\"\"\".replace(\n", "        \"\\n\", \"\"\n", "    )\n", "\n", "date_of_now = datetime.now().strftime(\"%Y-%m-%d\")\n", "\n", "sku_list_df_top10[\"鲜沐POP商品\"] = sku_list_df_top10.apply(display_xianmu_html, axis=1)\n", "sku_list_df_top10[\"标果商品(Top10)\"] = sku_list_df_top10.apply(display_matched_biaoguo_html, axis=1)\n", "\n", "html_content = css + sku_list_df_top10[[\"鲜沐POP商品\", \"标果商品(Top10)\"]].to_html(\n", "    escape=False, index=False, classes=\"table dataframe\"\n", ")\n", "html_content = f'<html><head><meta charset=\"UTF-8\"><meta name=\"title\" content=\"POP商品和标果商品的比较-{date_of_now}\"></head><body>{html_content}</body></html>'\n", "\n", "file_path = f\"{DATA_PATH}/鲜沐POP和标果-杭州的比较-{date_of_now}.html\"\n", "\n", "# 保存HTML到本地文件：\n", "with open(file_path, \"w\", encoding=\"utf-8\") as f:\n", "    f.write(html_content)\n", "\n", "print(f\"写入HTML成功！{file_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 使用语言模型（GPT3）做匹配"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["model = \"gpt-4o-mini\"\n", "\n", "llm_top_n = 3\n", "\n", "system_prompt = f\"\"\"\n", "你是一个专业的商品匹配助手。你的任务是为我司的商品从对手品牌中找出最相似的{llm_top_n}个商品。\n", "\n", "输入格式：\n", "```\n", "我司商品：\n", "__XIANMU_PRODUCT__\n", "\n", "对手商品列表：\n", "__BIAOGUO_PRODUCTS__\n", "```\n", "\n", "在进行匹配时，请重点考虑以下因素：\n", "1. 商品描述：考虑商品描述中的关键信息（如品种、产地、品牌）是否相近\n", "2. 规格一致性：优先匹配相同规格（如同为\"中果\"）\n", "3. 重量匹配度：比较商品的净重/毛重是否接近\n", "4. 价格相似度：比较商品的价格差异，价格越接近越好\n", "\n", "输出要求：\n", "1. 列出匹配到的前3个对手的商品，按相似度从高到低排序\n", "2. 对每个匹配项，说明选择理由，包括：\n", "   - 相似度得分（0-100分）\n", "   - 价格对比分析\n", "   - 重量对比分析\n", "   - 规格匹配说明\n", "3. 输出格式：\n", "```csv\n", "序号,对手商品ID,相似度得分,匹配理由,对手商品名称,对手商品价格\n", "1,对手商品ID,XX分,匹配理由：[详细说明价格、重量、规格等维度的匹配情况],商品名称1,商品价格1\n", "2,[第二匹配项...]\n", "3,[第三匹配项...]\n", "```\n", "\n", "注意事项：\n", "1. 商品价格的细微差异可以接受，但差距不应超过15%\n", "2. 品种必须精确匹配，如‘库尔勒香梨’必须完全匹配‘库尔勒香梨’\n", "3. 重量必须精确匹配，允许±1斤的误差\n", "4. 如果找不到完全匹配的规格，可以推荐相近规格的商品，但需要在理由中说明，且此时推荐分数不得超过75分。\n", "5. 所有分析和推理过程必须基于输入信息中明确提供的数据\n", "6. 仅需返回CSV内容即可，无须返回其他信息\n", "\"\"\"\n", "\n", "\n", "def get_top_matched_biaoguo_sku(xianmu_sku_specification: str, biaoguo_sku_to_match=[]):\n", "    sku_to_match = \"\\n\".join(biaoguo_sku_to_match)\n", "    message_content = system_prompt.replace(\n", "        \"__XIANMU_PRODUCT__\", xianmu_sku_specification\n", "    )\n", "    message_content = message_content.replace(\"__BIAOGUO_PRODUCTS__\", sku_to_match)\n", "    messages = [\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": message_content,\n", "        },\n", "    ]\n", "    completion = client.chat.completions.create(\n", "        model=model, temperature=0.7, max_tokens=4095, messages=messages\n", "    )\n", "\n", "    response = completion.choices[0].message.content\n", "    if (\n", "        len(completion.choices) <= 0\n", "        or f\"{completion.choices[0].finish_reason}\" == \"content_filter\"\n", "    ):\n", "        print(f\"azure过滤了本次请求:{completion.choices[0].to_dict()}\")\n", "    if response is None:\n", "        print(f\"azure API返回了异常:{completion.to_dict()}\")\n", "\n", "    return response"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 调用LLM进行匹配\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pop_sku_order_query=\"\"\"\n", "SELECT  a.sku_id\n", "        ,a.spu_name AS pd_name\n", "        ,CONCAT(a.category3,'>',a.category4) AS category\n", "        ,SUM(CASE WHEN b.status IN (2,3,6) THEN b.amount * b.price ELSE 0.0 END) AS gmv\n", "        ,COUNT(DISTINCT CASE WHEN b.status IN (2,3,6) THEN b.order_no END) AS order_cnt\n", "        ,COALESCE(SUM(CASE WHEN b.status IN (2,3,6) THEN b.amount END),0) AS order_quantity\n", "        ,MAX(b.create_time) as last_order_time\n", "FROM    summerfarm_tech.dim_sku_df a\n", "LEFT JOIN summerfarm_tech.ods_order_item_df b\n", "ON      b.ds = MAX_PT('summerfarm_tech.ods_order_item_df')\n", "AND     a.sku_id = b.sku\n", "WHERE   a.ds = MAX_PT('summerfarm_tech.dim_sku_df')\n", "AND     a.sub_type = 5\n", "GROUP BY a.sku_id\n", "         ,a.spu_name\n", "         ,category\n", "ORDER BY gmv DESC\n", ";\n", "\"\"\"\n", "\n", "pop_sku_order_df=get_odps_sql_result_as_df(pop_sku_order_query)\n", "pop_sku_order_df.head(2)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import csv\n", "import json\n", "from io import StringIO\n", "\n", "\n", "def matched_csv_to_json(csv_string: str, competitor: str = \"\"):\n", "    # 移除markdown格式的前缀和后缀\n", "    csv_string = csv_string.replace(\"```csv\", \"\").replace(\"```\", \"\").strip()\n", "\n", "    try:\n", "        # Convert the CSV string into a list of dictionaries\n", "        csv_reader = csv.DictReader(StringIO(csv_string))\n", "        json_data = []\n", "        for row in csv_reader:\n", "            # Rename keys to English using camelCase\n", "            row[\"rank\"] = row.pop(\"序号\")\n", "            row[\"competitorSkuCode\"] = row.pop(\"对手商品ID\")\n", "            row[\"similarityScore\"] = row.pop(\"相似度得分\").replace(\"分\", \"\")\n", "            row[\"matchingReason\"] = row.pop(\"匹配理由\")\n", "            row[\"competitorProductName\"] = row.pop(\"对手商品名称\")\n", "            row[\"competitorSkuPrice\"] = row.pop(\"对手商品价格\")\n", "            row[\"competitor\"] = competitor\n", "            json_data.append(row)\n", "\n", "        # Convert list of dictionaries to JSON format\n", "        return json.dumps(json_data, ensure_ascii=False, indent=2)\n", "    except Exception as e:\n", "        print(f\"转换CSV到JSON时出错: {e}\")\n", "        return \"[]\"\n", "\n", "\n", "def get_top_matched_by_llm(xianmu_sku):\n", "    specification = f\"skucode:{xianmu_sku['sku']}, {xianmu_sku['categoryName']}-{xianmu_sku['sku_name']}, 规格:{xianmu_sku['weight']}\"\n", "    specification = f\"{specification}, 毛重:{xianmu_sku.get('weightNum', 0)*2}斤, 净重:{xianmu_sku.get('netWeightNum', 0)*2}斤, 售价:¥{xianmu_sku['price']}\"\n", "    top_matches = xianmu_sku[\"top_matches\"]\n", "    biaoguo_sku_to_match = []\n", "    if len(top_matches) <= 0:\n", "        print(f\"鲜沐SKU embedding算法匹配不到任何标果商品:{specification}\")\n", "        return \"[]\"\n", "    for biaoguo_matched in top_matches:\n", "        sku = f\"skucode:{biaoguo_matched['skucode']}, {biaoguo_matched['categoryname']}-{biaoguo_matched['goods_name']}\"\n", "        sku = f\"{sku}, 规格:{biaoguo_matched['specification']}, 毛重:{biaoguo_matched['grossweight']}斤, 净重:{biaoguo_matched['netweight']}斤, 售价:¥{biaoguo_matched['finalstandardprice']}\"\n", "        print(\"标果SKU\", sku)\n", "        biaoguo_sku_to_match.append(sku)\n", "    matched_csv = get_top_matched_biaoguo_sku(\n", "        xianmu_sku_specification=specification,\n", "        biaoguo_sku_to_match=biaoguo_sku_to_match,\n", "    )\n", "\n", "    print(\"鲜沐SKU\", specification, f\"top{top_n}:{matched_csv}\")\n", "    matched_csv_json = matched_csv_to_json(\n", "        matched_csv, competitor=top_matches[0][\"competitor\"]\n", "    )\n", "    return json.dumps(matched_csv_json, ensure_ascii=False)\n", "\n", "\n", "sku_list_df_top10[\"top_matched_competitor_sku_list\"] = sku_list_df_top10.apply(\n", "    get_top_matched_by_llm, axis=1\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from odps_client import write_pandas_df_into_odps\n", "\n", "sku_list_to_save_df = sku_list_df_top10[\n", "    [\"sku\", \"weight\", \"sku_name\", \"pd_name\", \"top_matched_competitor_sku_list\"]\n", "]\n", "\n", "sku_list_to_save_df = pd.merge(\n", "    left=sku_list_to_save_df,\n", "    right=pop_sku_order_df,\n", "    left_on=\"sku\",\n", "    right_on=\"sku_id\",\n", "    suffixes=[\"\", \"_b\"],\n", ")\n", "\n", "sku_list_to_save_df = sku_list_to_save_df.drop(\n", "    columns=[\"pd_name_b\", \"sku_id\"], errors=\"ignore\"\n", ")\n", "\n", "partition_spec = f\"ds={datetime.now().strftime('%Y%m%d')}\"\n", "\n", "write_pandas_df_into_odps(\n", "    df=sku_list_to_save_df,\n", "    table_name=\"app_pop_top_matched_competitor_sku_list_df\",\n", "    partition_spec=partition_spec,\n", "    overwrite=True,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 把鲜沐的SKU写入到本地CSV"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# sku_list_clean_df = sku_list_df[\n", "#     [\n", "#         \"sku\",\n", "#         \"pdId\",\n", "#         \"categoryName\",\n", "#         \"openSale\",\n", "#         \"sku_name\",\n", "#         \"pd_name\",\n", "#         \"price\",\n", "#         \"supplyPrice\",\n", "#         \"areaName\",\n", "#         \"netWeightNum\",\n", "#         \"weightNum\",\n", "#         \"weight\",\n", "#         \"sku_spec\",\n", "#         \"spu_spec\",\n", "#         \"buyer_name\",\n", "#         \"picture\",\n", "#     ]\n", "# ].copy()\n", "# sku_list_clean_df.rename(\n", "#     columns={\n", "#         \"categoryName\": \"类目\",\n", "#         \"openSale\": \"是否上架\",\n", "#         \"areaName\": \"运营区域\",\n", "#         \"netWeightNum\": \"净重(公斤)\",\n", "#         \"weightNum\": \"毛重(公斤)\",\n", "#         \"picture\": \"图片链接\",\n", "#         \"weight\": \"SKU规格\",\n", "#         \"price\": \"售价\",\n", "#         \"supplyPrice\": \"供应商提报价\",\n", "#         \"buyer_name\": \"买手名字\",\n", "#     },\n", "#     inplace=True,\n", "# )\n", "# sku_list_clean_df[\"供应商提报价\"].fillna(0, inplace=True)\n", "# sku_list_clean_df.sort_values(by=[\"类目\", \"pd_name\"], inplace=True)\n", "# sku_list_clean_df.to_csv(f\"{DATA_PATH}/pop/鲜果POP全部SKU_{date_of_now}.csv\", index=False)\n", "# sku_list_clean_df.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 按照标果的销量排序"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["# all_sorted_sku = []\n", "# added_sku_set = set()\n", "# top_n_to_sort = 2\n", "\n", "# for index, row in biaoguo_df.head(200)[\n", "#     [\n", "#         \"top_matches\",\n", "#         \"goodsname\",\n", "#         \"categoryname\",\n", "#         \"monthsale_gmv\",\n", "#         \"monthsale\",\n", "#         \"finalstandardprice\",\n", "#         \"skucode\",\n", "#         \"specification\",\n", "#         \"grossweight\",\n", "#         \"url\",\n", "#     ]\n", "# ].iterrows():\n", "#     new_row = {}\n", "#     new_row[\"biaoguo_goodsname\"] = row[\"goodsname\"]\n", "#     new_row[\"biaoguo_categoryname\"] = row[\"categoryname\"]\n", "#     new_row[\"biaoguo_monthsale_gmv\"] = row[\"monthsale_gmv\"]\n", "#     new_row[\"biaoguo_monthsale\"] = row[\"monthsale\"]\n", "#     new_row[\"biaoguo_finalstandardprice\"] = row[\"finalstandardprice\"]\n", "#     new_row[\"biaoguo_skucode\"] = row[\"skucode\"]\n", "#     new_row[\"biaoguo_specification\"] = row[\"specification\"]\n", "#     new_row[\"biaoguo_grossweight\"] = row[\"grossweight\"]\n", "#     new_row[\"biaoguo_url\"] = row[\"url\"]\n", "#     my_top_n = 0\n", "\n", "#     for index, matched_item in enumerate(row[\"top_matches\"]):\n", "#         sku = matched_item[\"sku\"]\n", "#         if sku in added_sku_set:\n", "#             print(f\"duplicated sku:{sku}\")\n", "#             continue\n", "#         on_sale = matched_item.get(\"onSale\", \"下架\")\n", "#         if \"下架\" in on_sale:\n", "#             print(\n", "#                 f\"sku未上架:{sku},{matched_item.get('onSale',False)}, {matched_item['sku_name']}, price:{matched_item['price']}\"\n", "#             )\n", "#             continue\n", "#         my_top_n = my_top_n + 1\n", "#         if my_top_n > top_n_to_sort:\n", "#             print(f'为了防止太多重复的品...{matched_item[\"sku_name\"]}')\n", "#             break\n", "#         new_row_with_xianmu = {}\n", "#         new_row_with_xianmu.update(new_row)\n", "#         new_row_with_xianmu[\"xianmu_sku\"] = matched_item[\"sku\"]\n", "#         new_row_with_xianmu[\"xianmu_picture\"] = matched_item[\"picture\"]\n", "#         new_row_with_xianmu[\"xianmu_price\"] = matched_item[\"price\"]\n", "#         new_row_with_xianmu[\"xianmu_onSale\"] = matched_item[\"onSale\"]\n", "#         new_row_with_xianmu[\"xianmu_weight\"] = matched_item[\"weight\"]\n", "#         new_row_with_xianmu[\"xianmu_weightNum\"] = matched_item[\"weightNum\"]\n", "#         new_row_with_xianmu[\"xianmu_sku_name\"] = matched_item[\"sku_name\"]\n", "#         new_row_with_xianmu[\"xianmu_categoryName\"] = matched_item[\"categoryName\"]\n", "#         new_row_with_xianmu[\"xianmu_buyer_name\"] = matched_item[\"buyer_name\"]\n", "#         all_sorted_sku.append(new_row_with_xianmu)\n", "#         added_sku_set.add(sku)\n", "\n", "# all_sorted_sku_df = pd.DataFrame(all_sorted_sku)\n", "# all_sorted_sku_df.head(10)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["# sorted_html_content=[]\n", "# for index, row in all_sorted_sku_df.iterrows():\n", "#     content = f\"\"\"<div class=\"xianmu-container no-list-style\">\n", "#     <img class=\"large-img\" src=\"{row['xianmu_picture']}\">\n", "#     <ul>\n", "#     <li class=\"sku-title\">{row['xianmu_sku']}, {row[\"xianmu_sku_name\"]}</li>\n", "#     <li>鲜沐售价: <span class=\"sku-price\">¥{row[\"xianmu_price\"]}, ¥{round(float(row[\"xianmu_price\"])/float(row['xianmu_weightNum'])/2, 2)}/斤</span>(毛重)</li>\n", "#     <li>规格:{row[\"xianmu_weight\"]}, 毛重:{row.get('xianmu_weightNum', 0)*2}斤</li>\n", "#     <li>买手:{row[\"xianmu_buyer_name\"]}, 是否上架: {row[\"xianmu_onSale\"]}</li>\n", "#     <li class=\"sku-title\">标果SKU:{row[\"biaoguo_skucode\"]},{row[\"biaoguo_goodsname\"]},{row[\"biaoguo_specification\"]}</li>\n", "#     <li>标果售价: <span class=\"sku-price\">¥{row[\"biaoguo_finalstandardprice\"]}, ¥{round(float(row[\"biaoguo_finalstandardprice\"])/float(row['biaoguo_grossweight']),2)}/斤</span>(毛重)</li>\n", "#     <li>标果月GMV: ¥{round(row[\"biaoguo_monthsale_gmv\"],1)}, 月销{row[\"biaoguo_monthsale\"]}件</li>\n", "#     </ul></div>\"\"\".replace('\\n', '')\n", "#     sorted_html_content.append(content)\n", "\n", "# html_content = f\"\"\"<html><head><meta charset=\"UTF-8\"><meta name=\"title\" content=\"POP商品首页排序-{date_of_now}\">\n", "# <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, user-scalable=no\">\n", "# {css}\n", "# <style>\n", "# .xianmu-container{{font-size:smaller;}}\n", "# .large-img{{object-fit: contain;width:160;}}\n", "# .xianmu-container{{max-width:200px;padding:0.2vh 0.5vw;}}\n", "# .sorted-container{{display: flex;width: 100%;flex-wrap: wrap;padding: 20px;}}\n", "# .xianmu-container span.sku-price{{color:lightcoral;font-weight:bolder;}}\n", "# </style>\n", "# </head><body><div class=\"sorted-container\">{''.join(sorted_html_content)}</div></body></html>\"\"\"\n", "\n", "# file_path = f\"{DATA_PATH}/pop/POP商品首页排序-根据标果销量-{date_of_now}.html\"\n", "\n", "# # 保存HTML到本地文件：\n", "# with open(file_path, \"w\", encoding=\"utf-8\") as f:\n", "#     f.write(html_content)\n", "\n", "# print(f\"写入HTML成功！{file_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 联合匹配标果、蜂果供、果速送、壹生鲜果等多家平台"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 标准SQL模型"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["from odps_client import get_odps_sql_result_as_df\n", "\n", "ds_now = datetime.now().strftime(\"%Y%m%d\")\n", "ds_3days_ago = (datetime.now() - timed<PERSON>ta(days=3)).strftime(\"%Y%m%d\")\n", "\n", "\n", "def get_standard_df_by_sql_template(sql_template: str) -> pd.DataFrame:\n", "    competitor_df = get_odps_sql_result_as_df(sql=sql_template.format(ds=ds_now))\n", "    competitor_3d_df = get_odps_sql_result_as_df(\n", "        sql=sql_template.format(ds=ds_3days_ago)\n", "    )\n", "    return merge_now_and_3d_data(\n", "        competitor_df=competitor_df, competitor_3d_df=competitor_3d_df\n", "    )\n", "\n", "\n", "def merge_now_and_3d_data(\n", "    competitor_df: pd.DataFrame, competitor_3d_df: pd.DataFrame\n", ") -> pd.DataFrame:\n", "    competitor_3d_df.rename(columns={\"month_sale\": \"sales_volume_3d\"}, inplace=True)\n", "    competitor_df = pd.merge(\n", "        competitor_df,\n", "        competitor_3d_df[[\"sku_code\", \"sales_volume_3d\"]],\n", "        on=\"sku_code\",\n", "        how=\"left\",\n", "    )\n", "\n", "    # Convert to numeric and handle errors\n", "    competitor_df[\"month_sale\"] = pd.to_numeric(\n", "        competitor_df[\"month_sale\"], errors=\"coerce\"\n", "    )\n", "    competitor_df[\"sales_volume_3d\"] = pd.to_numeric(\n", "        competitor_df[\"sales_volume_3d\"], errors=\"coerce\"\n", "    )\n", "\n", "    # Fill NaNs with a default value (e.g., 0) or handle them as needed\n", "    competitor_df[\"month_sale\"].fillna(0, inplace=True)\n", "    competitor_df[\"sales_volume_3d\"].fillna(0, inplace=True)\n", "\n", "    # Convert to integers\n", "    competitor_df[\"month_sale\"] = competitor_df[\"month_sale\"].astype(int)\n", "    competitor_df[\"sales_volume_3d\"] = competitor_df[\"sales_volume_3d\"].astype(int)\n", "\n", "    # Perform the subtraction\n", "    competitor_df[\"sales_volume_3d\"] = (\n", "        competitor_df[\"month_sale\"] - competitor_df[\"sales_volume_3d\"]\n", "    )\n", "\n", "    competitor_df.sort_values(by=\"sales_volume_3d\", ascending=False, inplace=True)\n", "    return competitor_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 蜂果供\n", "\n", "暂时先注释掉"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# fengguogong_sql = \"\"\"\n", "# SELECT  categoryname AS category_name\n", "#         ,competitor\n", "#         ,goodscode AS sku_code\n", "#         ,spider_fetch_time\n", "#         ,NULL AS goods_siphon_commission_rate\n", "#         ,wholeggguoprice AS standard_price\n", "#         ,wholeggguoprice AS final_standard_price\n", "#         ,salenum AS month_sale\n", "#         ,goodslogo AS url\n", "#         ,NULL AS seller_siphon_commission_rate\n", "#         ,ggguoprice AS unit_price_catty\n", "#         ,priceunit AS unit\n", "#         ,NULL AS seller_name\n", "#         ,netweight AS gross_weight\n", "#         ,roughweight AS net_weight\n", "#         ,concat(sizedesc,pricedesc,',单果:',singlenutweight) AS specification\n", "#         ,goodsname AS goods_name\n", "#         ,NULL as seven_day_after_sale\n", "# FROM    (\n", "#             SELECT  *\n", "#                     ,RANK() OVER (PARTITION BY id,goodscode ORDER BY spider_fetch_time DESC ) AS rnk\n", "#             FROM    summerfarm_ds.spider_fengguogong_product_result_df\n", "#             WHERE   ds = '{ds}'\n", "#         ) \n", "# WHERE   rnk = 1\n", "# ;\n", "# \"\"\"\n", "\n", "# fengguogong_df = get_standard_df_by_sql_template(fengguogong_sql)\n", "# fengguogong_df.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 标果-杭州"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["biaoguo_sql=\"\"\"\n", "SELECT  categoryname AS category_name,\n", "        competitor,\n", "        skucode AS sku_code,\n", "        spider_fetch_time,\n", "        goodssiphoncommissionrate AS goods_siphon_commission_rate,\n", "        standardprice AS standard_price,\n", "        finalstandardprice AS final_standard_price,\n", "        monthsale AS month_sale,\n", "        attachurlr AS url,\n", "        sellersiphoncommissionrate AS seller_siphon_commission_rate,\n", "        unitpricecatty AS unit_price_catty,\n", "        unit,\n", "        sellername AS seller_name,\n", "        grossweight AS gross_weight,\n", "        netweight AS net_weight,\n", "        specification,\n", "        goodsname AS goods_name,\n", "        sevendayaftersale AS seven_day_after_sale\n", "FROM(\n", "            SELECT  *\n", "                    ,RANK() OVER (PARTITION BY id,skucode ORDER BY spider_fetch_time DESC ) AS rnk\n", "            FROM    summerfarm_ds.spider_biaoguo_with_prop_product_result_df\n", "            WHERE   ds = '{ds}'\n", "            AND     competitor = '标果-杭州' \n", "            AND     categoryname like '%/%'\n", "        ) \n", "WHERE   rnk = 1\n", "LIMIT   100000;\n", "\"\"\"\n", "\n", "biaoguo_df = get_standard_df_by_sql_template(biaoguo_sql)\n", "biaoguo_df.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 果速送\n", "\n", "爬不到了"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# guosusong_sql = \"\"\"\n", "# select   typename AS category_name,\n", "#     competitor,\n", "#     goodscode AS sku_code,\n", "#     spider_fetch_time,\n", "#     NULL AS goods_siphon_commission_rate,\n", "#     wholegssprice AS standard_price,\n", "#     wholegssprice AS final_standard_price,\n", "#     salenum AS month_sale,\n", "#     goodslogo AS url,\n", "#     NULL AS seller_siphon_commission_rate,\n", "#     roughprice AS unit_price_catty,\n", "#     priceunit AS unit,\n", "#     NULL AS seller_name,\n", "#     roughweight AS gross_weight,\n", "#     netweight AS net_weight,\n", "#     concat(sizedesc,',',pricedesc) AS specification,\n", "#     goodsname AS goods_name,\n", "#     NULL AS seven_day_after_sale\n", "# from (\n", "#             SELECT  *\n", "#                     ,RANK() OVER (PARTITION BY id,goodscode ORDER BY spider_fetch_time DESC ) AS rnk\n", "#             FROM    summerfarm_ds.spider_guosusong_product_result_df\n", "#             WHERE   ds = '{ds}'\n", "#         ) \n", "# WHERE   rnk = 1\n", "# \"\"\"\n", "\n", "# guosusong_df = get_standard_df_by_sql_template(guosusong_sql)\n", "# guosusong_df.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 壹生鲜果\n", "\n", "好像已经不用了"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# yishengxianguo_sql = \"\"\"\n", "# select * from (\n", "#     select category_name AS category_name,\n", "#         competitor AS competitor,\n", "#         goods_code AS sku_code,\n", "#         spider_fetch_time AS spider_fetch_time,\n", "#         sales_volume AS month_sale,\n", "#         pic_path AS url,\n", "#         min_unit AS unit,\n", "#         provider_name AS seller_name,\n", "#         goods_describe AS specification,\n", "#         goods_name AS goods_name,\n", "#         item_vos,basics_item,RANK() OVER (PARTITION BY goods_code ORDER BY spider_fetch_time DESC ) AS rnk\n", "#     from summerfarm_ds.spider_yishengxianguo_product_result_df \n", "#     where ds='{ds}'\n", "# )t\n", "# where rnk=1\n", "# \"\"\"\n", "\n", "# yishengxianguo_df = get_odps_sql_result_as_df(sql=yishengxianguo_sql.format(ds=ds_now))\n", "# yishengxianguo_3d_df = get_odps_sql_result_as_df(\n", "#     sql=yishengxianguo_sql.format(ds=ds_3days_ago)\n", "# )\n", "\n", "# import re\n", "\n", "\n", "# def extract_gross_weight(item_name):\n", "#     # Use regex to find the number before '斤'\n", "#     match = re.search(r\"(\\d+)(?=斤)\", item_name)\n", "#     if match:\n", "#         return int(match.group(1))\n", "#     else:\n", "#         return None\n", "\n", "\n", "# def convert_yishengxianguo_df(yishengxianguo_df: pd.DataFrame) -> pd.DataFrame:\n", "#     all_yishengxianguo_items = []\n", "#     for inx, row in yishengxianguo_df.iterrows():\n", "#         item = {}\n", "#         item[\"category_name\"] = row[\"category_name\"]\n", "#         item[\"competitor\"] = row[\"competitor\"]\n", "#         item[\"sku_code\"] = row[\"sku_code\"]\n", "#         item[\"spider_fetch_time\"] = row[\"spider_fetch_time\"]\n", "#         item[\"url\"] = row[\"url\"]\n", "#         item[\"unit\"] = row[\"unit\"]\n", "#         item[\"seller_name\"] = row[\"seller_name\"]\n", "#         item[\"specification\"] = row[\"specification\"]\n", "#         item[\"goods_name\"] = row[\"goods_name\"]\n", "\n", "#         json_string = (\n", "#             row[\"item_vos\"]\n", "#             .replace(\"'\", '\"')\n", "#             .replace(\"None\", \"null\")\n", "#             .replace(\"False\", \"false\")\n", "#             .replace(\"True\", \"true\")\n", "#         )\n", "#         # print(json_string)\n", "#         # Convert single quotes to double quotes\n", "#         item_vo = json.loads(json_string)[0]\n", "#         # print(item_vo)\n", "#         item[\"goods_name\"] = row[\"goods_name\"]\n", "#         item[\"specification\"] = f\"{item['specification']}, {item_vo['itemName']}\"\n", "#         item[\"final_standard_price\"] = item_vo[\"price\"]\n", "#         item[\"standard_price\"] = item_vo[\"price\"]\n", "#         item[\"seller_siphon_commission_rate\"] = \"\"\n", "#         item[\"goods_siphon_commission_rate\"] = \"\"\n", "#         item[\"month_sale\"] = item_vo[\"saleNum\"]\n", "#         item[\"gross_weight\"] = extract_gross_weight(item_vo[\"itemName\"])\n", "#         item[\"unit_price_catty\"] = (\n", "#             float(item_vo[\"price\"]) / item[\"gross_weight\"]\n", "#             if item[\"gross_weight\"] is not None\n", "#             else 0\n", "#         )\n", "#         item[\"net_weight\"] = \"-1\"\n", "#         item[\"seven_day_after_sale\"] = None\n", "#         all_yishengxianguo_items.append(item)\n", "#     return pd.DataFrame(all_yishengxianguo_items)\n", "\n", "\n", "# yishengxianguo_df = convert_yishengxianguo_df(yishengxianguo_df)\n", "# yishengxianguo_3d_df = convert_yishengxianguo_df(yishengxianguo_3d_df)\n", "\n", "# yishengxianguo_df = merge_now_and_3d_data(\n", "#     competitor_df=yishengxianguo_df, competitor_3d_df=yishengxianguo_3d_df\n", "# )\n", "# yishengxianguo_df.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 合并起来"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Concatenating DataFrames\n", "# all_competitor_df = pd.concat([biaoguo_df, guosusong_df, fengguogong_df, yishengxianguo_df], ignore_index=True)\n", "all_competitor_df = biaoguo_df\n", "\n", "categories = all_competitor_df[\"category_name\"].unique()\n", "\n", "print(f\"所有的类目:{categories}\")\n", "\n", "# List of known fruit categories\n", "fruit_keywords = [\n", "    \"榴莲\",\n", "    \"火龙果\",\n", "    \"菠萝\",\n", "    \"山竹\",\n", "    \"菠萝蜜\",\n", "    \"蓝莓\",\n", "    \"梨\",\n", "    \"椰子\",\n", "    \"柠檬\",\n", "    \"猕猴桃\",\n", "    \"牛油果\",\n", "    \"芒果\",\n", "    \"苹果\",\n", "    \"柚\",\n", "    \"葡萄\",\n", "    \"桃\",\n", "    \"李\",\n", "    \"西瓜\",\n", "    \"枣\",\n", "    \"樱桃\",\n", "    \"草莓\",\n", "    \"橙\",\n", "    \"荔枝\",\n", "    \"柿子\",\n", "    \"石榴\",\n", "    \"杏\",\n", "    \"桔\",\n", "    \"柑\",\n", "    \"甜瓜\",\n", "    \"提子\",\n", "    \"木瓜\",\n", "    \"杨桃\",\n", "    \"人参果\",\n", "    \"释迦果\",\n", "    \"莲雾\",\n", "    \"芭乐\",\n", "    \"百香果\",\n", "    \"黄瓜\",\n", "]\n", "\n", "# Filter categories based on the fruit keywords\n", "fruit_categories = [\n", "    category\n", "    for category in categories\n", "    if any(keyword in category for keyword in fruit_keywords)\n", "]\n", "\n", "# Filter rows where category_name is in fruit_categories\n", "fruit_filtered_df = all_competitor_df[\n", "    all_competitor_df[\"category_name\"].isin(fruit_categories)\n", "]\n", "\n", "# Filter rows where category_name is NOT in fruit_categories\n", "not_fruit_filtered_df = all_competitor_df[\n", "    ~all_competitor_df[\"category_name\"].isin(fruit_categories)\n", "]\n", "\n", "print(f\"过滤后的数量:{len(fruit_filtered_df)}, 过滤前的数量:{len(all_competitor_df)}\")\n", "not_fruit_filtered_df.sort_values(by=\"month_sale\", ascending=False, inplace=True)\n", "not_fruit_filtered_df.head(20)\n", "\n", "\n", "fruit_filtered_df[\"sku_embeddings\"] = fruit_filtered_df.apply(\n", "    get_competitor_like_biaoguo_embedding, axis=1\n", ")\n", "sku_list_df[\"sku_embeddings\"] = sku_list_df.apply(get_xianmu_embedding, axis=1)\n", "\n", "fruit_filtered_df[\"category_embeddings\"] = fruit_filtered_df.apply(\n", "    lambda row: get_category_embedding(f\"{row['category_name']}\"),\n", "    axis=1,\n", ")\n", "sku_list_df[\"category_embeddings\"] = sku_list_df.apply(\n", "    lambda row: get_category_embedding(f\"{row['categoryName']}\"),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 将数据类型转换为数值类型\n", "fruit_filtered_df[\"final_standard_price\"] = pd.to_numeric(\n", "    fruit_filtered_df[\"final_standard_price\"], errors=\"coerce\"\n", ")\n", "fruit_filtered_df[\"month_sale\"] = pd.to_numeric(\n", "    fruit_filtered_df[\"month_sale\"], errors=\"coerce\", downcast=\"integer\"\n", ")\n", "\n", "# 计算月销售额和3天销售额\n", "fruit_filtered_df[\"monthsale_gmv\"] = fruit_filtered_df.apply(\n", "    lambda row: row[\"month_sale\"] * row[\"final_standard_price\"], axis=1\n", ")\n", "fruit_filtered_df[\"monthsale_gmv_3d_ago\"] = fruit_filtered_df.apply(\n", "    lambda row: row[\"sales_volume_3d\"] * row[\"final_standard_price\"], axis=1\n", ")\n", "\n", "# 按照竞争对手、3天销售额和月销售额进行排序\n", "fruit_filtered_df.sort_values(\n", "    by=[\"competitor\",\"sales_volume_3d\", \"monthsale_gmv\"],\n", "    ascending=False,\n", "    inplace=True,\n", ")\n", "\n", "# 查看前20行数据\n", "fruit_filtered_df.head(20)[\n", "    [\n", "        \"monthsale_gmv_3d_ago\",\n", "        \"monthsale_gmv\",\n", "        \"sales_volume_3d\",\n", "        \"month_sale\",\n", "        \"goods_name\",\n", "        \"competitor\",\n", "    ]\n", "]\n", "\n", "all_competitor_to_xianmu_top_n = 10\n", "\n", "\n", "## 把表格的商品映射到鲜沐上，标果的商品作为主键，和 def find_top_matches_for_xian<PERSON>() 的顺序是反过来的\n", "def find_top_xianmu_matches_for_competitor(competitor_sku_row: pd.Series):\n", "    similarities = []\n", "    for index, xianmu_row in sku_list_df.iterrows():\n", "        category_similarity_score = calculate_cosine_similarity(\n", "            competitor_sku_row[\"category_embeddings\"], xianmu_row[\"category_embeddings\"]\n", "        )\n", "        if category_similarity_score <= category_similarity_score_theshold:\n", "            # 如果类目的相似度小于0.88，则不需要再考虑名字的embedding\n", "            # print(\n", "            #     f'类目的相似度小于{category_similarity_score_theshold}:{category_similarity_score}, {competitor_sku_row[\"competitor\"]}类目:{competitor_sku_row[\"category_name\"]}, 鲜沐类目:{xianmu_row[\"categoryName\"]}'\n", "            # )\n", "            continue\n", "        similarity_score = calculate_cosine_similarity(\n", "            competitor_sku_row[\"sku_embeddings\"], xianmu_row[\"sku_embeddings\"]\n", "        )\n", "        # 类目embedding + sku embedding分数\n", "        xianmu_sku_row_dict = xianmu_row.to_dict()\n", "        keys_to_remove = [\"sku_embeddings\", \"category_embeddings\"]\n", "        for key in keys_to_remove:\n", "            if key in xianmu_sku_row_dict:\n", "                del xianmu_sku_row_dict[key]\n", "        similarities.append(\n", "            (xianmu_sku_row_dict, similarity_score + category_similarity_score)\n", "        )\n", "    if len(similarities) <= 0:\n", "        print(f\"SKU:{competitor_sku_row['goods_name']} 未匹配到\")\n", "        return []\n", "    elif len(similarities) <= 1:\n", "        # 如果匹配出来的结果很少，则立即返回\n", "        return similarities[0][0]\n", "\n", "    # Sort the results based on similarity score in descending order\n", "    sorted_similarities = sorted(similarities, key=lambda x: x[1], reverse=True)\n", "\n", "    # Get the top N matches\n", "    top_n_matches = [\n", "        item[0] for item in sorted_similarities[:all_competitor_to_xianmu_top_n]\n", "    ]\n", "    print(\n", "        f\"Matched rows for:{competitor_sku_row['goods_name']}, {[xianmu.get('sku_name') for xianmu in top_n_matches]}\"\n", "    )\n", "    competitor_sku_row[\"top_matches\"] = top_n_matches\n", "    return top_n_matches\n", "\n", "\n", "fruit_filtered_df[\"top_matches\"] = fruit_filtered_df.apply(\n", "    find_top_xianmu_matches_for_competitor, axis=1\n", ")\n", "fruit_filtered_df.head(10)[\n", "    [\n", "        \"top_matches\",\n", "        \"goods_name\",\n", "        \"category_name\",\n", "        \"competitor\",\n", "        \"monthsale_gmv_3d_ago\",\n", "        \"monthsale_gmv\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandasql\n", "\n", "fruit_filtered_df[\"category_level2\"] = (\n", "    fruit_filtered_df[\"category_name\"].str.split(\"/\").str[0]\n", ")\n", "\n", "fruit_filtered_clean_df = fruit_filtered_df[\n", "    [\n", "        \"sku_code\",\n", "        \"goods_name\",\n", "        \"category_name\",\n", "        \"month_sale\",\n", "        \"monthsale_gmv\",\n", "        \"monthsale_gmv_3d_ago\",\n", "        \"category_level2\",\n", "        \"competitor\",\n", "    ]\n", "]\n", "\n", "static_df = pandasql.sqldf(\n", "    \"\"\"select category_name,competitor\n", "    ,sum(monthsale_gmv) as category_monthsale_gmv \n", "    ,sum(monthsale_gmv_3d_ago) as category_3d_gmv \n", "    from fruit_filtered_clean_df \n", "    group by competitor,category_name\"\"\"\n", ")\n", "\n", "# Step 2: Merge total_gmv into fruit_filtered_df\n", "fruit_filtered_with_category_gmv_df = fruit_filtered_df.merge(\n", "    static_df[\n", "        [\"category_name\", \"competitor\", \"category_monthsale_gmv\", \"category_3d_gmv\"]\n", "    ],\n", "    on=[\"category_name\", \"competitor\"],\n", "    how=\"left\",\n", ")\n", "\n", "fruit_filtered_with_category_gmv_df.head(10)\n", "\n", "fruit_filtered_with_category_gmv_df[\"monthsale_gmv_percentile_of_category\"] = round(\n", "    100.00\n", "    * fruit_filtered_with_category_gmv_df[\"monthsale_gmv\"].astype(float)\n", "    / fruit_filtered_with_category_gmv_df[\"category_monthsale_gmv\"].astype(float),\n", "    2,\n", ")\n", "\n", "fruit_filtered_with_category_gmv_df.head(10)\n", "\n", "fruit_filtered_with_category_gmv_df[\"monthsale_gmv_3d_ago_percentile_of_category\"] = round(\n", "    100.00\n", "    * fruit_filtered_with_category_gmv_df[\"monthsale_gmv_3d_ago\"].astype(float)\n", "    / fruit_filtered_with_category_gmv_df[\"category_3d_gmv\"].astype(float),\n", "    2,\n", ")\n", "\n", "\n", "# Step 4: Rank each item within its category based on monthsale_gmv\n", "fruit_filtered_with_category_gmv_df[\"category_rank\"] = (\n", "    fruit_filtered_with_category_gmv_df.groupby([\"category_name\", \"competitor\"])[\n", "        \"monthsale_gmv_3d_ago\"\n", "    ].rank(ascending=False)\n", ")\n", "\n", "fruit_filtered_top10_of_each_category_df = fruit_filtered_with_category_gmv_df[\n", "    fruit_filtered_with_category_gmv_df[\"category_rank\"] <= 10.0\n", "]\n", "fruit_filtered_top10_of_each_category_df = fruit_filtered_top10_of_each_category_df[\n", "    fruit_filtered_top10_of_each_category_df[\"category_monthsale_gmv\"] > 1000.00\n", "]\n", "\n", "fruit_filtered_top10_of_each_category_df.head(10)[\n", "    [\n", "        \"category_monthsale_gmv\",\n", "        \"monthsale_gmv\",\n", "        \"monthsale_gmv_3d_ago_percentile_of_category\",\n", "        \"monthsale_gmv_percentile_of_category\",\n", "        \"category_rank\",\n", "        \"category_level2\",\n", "        \"category_name\",\n", "        \"goods_name\",\n", "        \"competitor\",\n", "    ]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime, timedelta\n", "\n", "date_of_now = datetime.now().strftime(\"%Y-%m-%d\")\n", "sku_list_for_csv_top200 = []\n", "for _, row in fruit_filtered_top10_of_each_category_df.iterrows():\n", "    competitor = row[\"competitor\"]\n", "    csv_object = {\"competitor\": competitor}\n", "    unit_price_catty = row[\"unit_price_catty\"]\n", "    gross_weight = row.get(\"gross_weight\", \"\")\n", "    if \"\" == gross_weight:\n", "        print(f\"未找到gross_weight:{gross_weight}\")\n", "    else:\n", "        unit_price_catty = round(\n", "            float(row.get(\"final_standard_price\", \"0.0\"))\n", "            / float(row.get(\"gross_weight\", \"1\")),\n", "            2,\n", "        )\n", "    competitor_content = f\"\"\"{row[\"sku_code\"]},{row[\"goods_name\"]},{row[\"specification\"]}\n", "{competitor}售价: ¥{row.get(\"final_standard_price\",\"0.0\")}, ¥{unit_price_catty}/斤(毛重)\n", "{competitor}月GMV: ¥{round(row[\"monthsale_gmv\"],1)}, 月销{row[\"month_sale\"]}件\n", "{competitor}近3日GMV: ¥{round(row[\"monthsale_gmv_3d_ago\"],1)}, 近3日销{row[\"sales_volume_3d\"]}件\n", "毛重: {row[\"gross_weight\"]}斤, 商品抽佣:{row.get('goods_siphon_commission_rate',0)}%, 向卖家抽佣:{row.get('seller_siphon_commission_rate',0)}%\"\"\"\n", "    csv_object[\"竞争对手skucode\"] = row[\"sku_code\"]\n", "    csv_object[\"竞争对手类目\"] = f\"\"\"{row[\"category_name\"]}\"\"\"\n", "    csv_object[\"类目月GMV\"] = f\"{row['category_monthsale_gmv']}\"\n", "    csv_object[\"类目近3日GMV\"] = f\"{row['category_3d_gmv']}\"\n", "    csv_object[\"近3日占类目GMV百分比\"] = f\"{row['monthsale_gmv_3d_ago_percentile_of_category']}%\"\n", "    csv_object[\"月GMV占类目百分比\"] = f\"{row['monthsale_gmv_percentile_of_category']}%\"\n", "    csv_object[\"竞争对手SKU售价\"] = (\n", "        f\"\"\"¥{row.get(\"final_standard_price\",\"0.0\")}, ¥{unit_price_catty}/斤(毛重)\"\"\"\n", "    )\n", "    csv_object[\"竞争对手SKU月GMV\"] = (\n", "        f\"\"\"¥{round(row[\"monthsale_gmv\"],1)}, 月销{row[\"month_sale\"]}件\"\"\"\n", "    )\n", "    csv_object[\"竞争对手SKU近3日GMV\"] = (\n", "        f\"\"\"¥{round(row[\"monthsale_gmv_3d_ago\"],1)}, 近3日销{row[\"sales_volume_3d\"]}件\"\"\"\n", "    )\n", "    csv_object[\"竞争对手SKU\"] = competitor_content\n", "\n", "    top_matches = row[\"top_matches\"]\n", "    if isinstance(top_matches, list):\n", "        for i in range(0, 20):\n", "            if i >= len(top_matches):\n", "                break\n", "            item = top_matches[i]\n", "        single_item = f\"\"\"{item['sku']}, {item['sku_name']}, {item['weight']}\n", "顺鹿达价格: ¥{item['price']}\n", "单价: ¥{round(float(item['price'])/float(item['weightNum'])/2,2)}/斤(毛重)\n", "类目:{item['categoryName']}, 毛重:{item['weightNum']*2}斤\"\"\"\n", "        csv_object[f\"顺鹿达SKU{i+1}\"] = single_item\n", "    sku_list_for_csv_top200.append(csv_object)\n", "\n", "sku_list_for_csv_top200_df = pd.DataFrame(sku_list_for_csv_top200)\n", "sku_list_for_csv_top200_df.drop_duplicates(\n", "    subset=[f\"竞争对手skucode\", \"competitor\"], inplace=True\n", ")\n", "sku_list_for_csv_top200_df.to_csv(\n", "    f\"{DATA_PATH}/顺鹿达SKU_vs_竞争对手SKU_top热销200_{date_of_now}.csv\",\n", "    index=False,\n", ")\n", "\n", "for competitor in sku_list_for_csv_top200_df[\"competitor\"].unique():\n", "    print(competitor)\n", "    competitor_df = sku_list_for_csv_top200_df[\n", "        sku_list_for_csv_top200_df[\"competitor\"] == competitor\n", "    ]\n", "    competitor_df.to_csv(\n", "        f\"{DATA_PATH}/顺鹿达SKU_vs_{competitor}SKU_top热销200_{date_of_now}.csv\",\n", "        index=False,\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 使用LLM进行匹配"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import csv\n", "import json\n", "from io import StringIO\n", "\n", "\n", "def matched_csv_to_xian<PERSON>_json(csv_string: str, competitor: str = \"\"):\n", "    # 移除markdown格式的前缀和后缀\n", "    csv_string = csv_string.replace(\"```csv\", \"\").replace(\"```\", \"\").strip()\n", "\n", "    try:\n", "        # Convert the CSV string into a list of dictionaries\n", "        csv_reader = csv.DictReader(StringIO(csv_string))\n", "        json_data = []\n", "        for row in csv_reader:\n", "            # Rename keys to English using camelCase\n", "            row[\"rank\"] = row.pop(\"序号\")\n", "            row[\"xianmuSkuCode\"] = row.pop(\"对手商品ID\")\n", "            row[\"similarityScore\"] = row.pop(\"相似度得分\").replace(\"分\", \"\")\n", "            row[\"matchingReason\"] = row.pop(\"匹配理由\")\n", "            row[\"xianmuProductName\"] = row.pop(\"对手商品名称\")\n", "            row[\"xianmuSkuPrice\"] = row.pop(\"对手商品价格\")\n", "            json_data.append(row)\n", "\n", "        # Convert list of dictionaries to JSON format\n", "        return json.dumps(json_data, ensure_ascii=False, indent=2)\n", "    except Exception as e:\n", "        print(f\"转换CSV到JSON时出错: {e}\")\n", "        return \"[]\"\n", "\n", "\n", "def get_top_matched_xianmu_sku_by_llm(competitor_sku: pd.Series):\n", "    specification = f\"skucode:{competitor_sku['sku_code']}, {competitor_sku['category_name']}-{competitor_sku['goods_name']}, 规格:{competitor_sku['specification']}\"\n", "    specification = f\"{specification}, 毛重:{competitor_sku.get('grossweight', 0)}斤, 净重:{competitor_sku.get('netweight', 0)}斤, 售价:¥{competitor_sku['final_standard_price']}\"\n", "    top_matches = competitor_sku[\"top_matches\"]\n", "    print(f\"鲜沐SKU\", specification)\n", "    biaoguo_sku_to_match = []\n", "    if not isinstance(top_matches, list) or len(top_matches) <= 0:\n", "        print(f\"对手的SKU embedding算法匹配不到任何鲜沐商品:{specification}\")\n", "        return \"[]\"\n", "    for xianmu_matched in top_matches:\n", "        sku = f\"skucode:{xianmu_matched['sku']}, {xianmu_matched['categoryName']}-{xianmu_matched['sku_name']}\"\n", "        sku = f\"{sku}, 规格:{xianmu_matched['weight']}, 毛重:{xianmu_matched['weightNum']*2}斤, 净重:{xianmu_matched['netWeightNum']*2}斤, 售价:¥{xianmu_matched['price']}\"\n", "        print(\"鲜沐SKU\", sku)\n", "        biaoguo_sku_to_match.append(sku)\n", "\n", "    matched_csv = get_top_matched_biaoguo_sku(\n", "        xianmu_sku_specification=specification,\n", "        biaoguo_sku_to_match=biaoguo_sku_to_match,\n", "    )\n", "\n", "    print(\"对手的SKU\", specification, f\"top{top_n}:{matched_csv}\")\n", "    matched_csv_json = matched_csv_to_xianmu_json(matched_csv)\n", "    return json.dumps(matched_csv_json, ensure_ascii=False)\n", "\n", "\n", "top_products_to_save = 500\n", "\n", "fruit_filtered_df.sort_values(\n", "    by=[\"monthsale_gmv_3d_ago\", \"monthsale_gmv\"], ascending=False, inplace=True\n", ")\n", "fruit_filtered_df_top_sales = fruit_filtered_df.head(top_products_to_save)\n", "\n", "fruit_filtered_df_top_sales[\"top_matched_xianmu_sku_list\"] = (\n", "    fruit_filtered_df_top_sales.apply(get_top_matched_xianmu_sku_by_llm, axis=1)\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fruit_filtered_df_to_save = fruit_filtered_df_top_sales[\n", "    [\n", "        \"category_name\",\n", "        \"competitor\",\n", "        \"sku_code\",\n", "        \"spider_fetch_time\",\n", "        \"final_standard_price\",\n", "        \"month_sale\",\n", "        \"url\",\n", "        \"gross_weight\",\n", "        \"net_weight\",\n", "        \"specification\",\n", "        \"goods_name\",\n", "        \"sales_volume_3d\",\n", "        \"monthsale_gmv\",\n", "        \"last_3d_gmv\",\n", "        \"category_level2\",\n", "        \"monthsale_gmv_3d_ago\",\n", "        \"top_matched_xianmu_sku_list\",\n", "    ]\n", "]\n", "\n", "write_pandas_df_into_odps(\n", "    df=fruit_filtered_df_to_save,\n", "    table_name=\"app_pop_biaoguo_top_sale_sku_df\",\n", "    partition_spec=partition_spec,\n", "    overwrite=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 2}