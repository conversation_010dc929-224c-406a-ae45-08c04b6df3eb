// 初始化代码高亮
$(document).ready(function () {
    hljs.highlightAll();
});

// 获取DOM元素和 define variables
const queryInput = $('#query-input');
const resultsContainer = $('#results-container'); // Make sure this exists in your HTML
const tableContainer = $('#table-container'); // Default table container
const loadingMask = $('#loading-mask');
const sqlQueryCode = $('#sql-query-code');
const codeBlock = $('.code-block');
const loadingTimer = $('#loading-timer');
const userQueryHistoryListDropdown = $('#user_query_history_list');
const systemQueryTemplatesDropdown = $('#system_query_templates');
const messageContainer = $('#message');
const copyButton = $('.copy-button')[0];
const reasoningContainer = $('#reasoning-container');
const reasoningContent = $('#reasoning-content');
const submitBtn = $('#submit-btn');


var currentSortColumn = null;
var sortAscending = true;
var tableData = null;
var timerInterval;
var hasErrors = false;
const md = new markdownit();

// 集中管理元素的启用/禁用状态
function disableEnableElements(action, options = {}) {
    const { btnText, originalBtnText } = options;

    switch (action) {
        case 'disable-for-query':
            // 查询开始时禁用元素
            submitBtn.text(btnText || "处理中...");
            submitBtn.prop('disabled', true);
            // loadingMask.css('display', 'flex');
            tableContainer.html(''); // 清空表格
            codeBlock.css('display', 'none');
            reasoningContainer.css('display', 'none'); // 隐藏推理内容容器
            break;

        case 'enable-after-query':
            // 查询完成后启用元素
            loadingMask.css('display', 'none');
            clearInterval(timerInterval);
            submitBtn.text(originalBtnText || "提交");
            submitBtn.prop('disabled', false);
            break;

        case 'enable-table-buttons':
            // 表格渲染后启用相关按钮
            break;

        case 'disable-for-import':
            // 导入飞书时禁用按钮
            submitBtn.prop('disabled', true);
            break;

        case 'enable-after-import':
            // 导入飞书后启用按钮
            submitBtn.prop('disabled', false);
            break;
    }
}

function displayInfoMessage(message) {
    messageContainer.html(md.render(message));
    messageContainer.removeClass('error-message').addClass('info-message');
    messageContainer.scrollTop(messageContainer[0].scrollHeight); // Scroll to bottom
}

function displayErrorMessage(message) {
    messageContainer.html(md.render(message));
    messageContainer.addClass('error-message').removeClass('info-message');
    messageContainer.scrollTop(messageContainer[0].scrollHeight); // Scroll to bottom
}


// 监听历史查询下拉框变化
userQueryHistoryListDropdown.on('change', function (e) {
    queryInput.val(e.target.value);
    systemQueryTemplatesDropdown.val('');
});
systemQueryTemplatesDropdown.on('change', function (e) {
    console.log("changing", e.target.value);
    queryInput.val(e.target.value);
    userQueryHistoryListDropdown.val('');
});

// 复制代码功能
async function copyCode() {
    const codeElement = $('.code-block code')[0];
    const text = codeElement.textContent;

    try {
        await navigator.clipboard.writeText(text);
        // 显示复制成功提示
        const originalText = copyButton.textContent;
        copyButton.textContent = '已复制!';
        setTimeout(() => {
            copyButton.textContent = originalText;
        }, 2000);
    } catch (err) {
        console.error('Failed to copy text: ', err);
        // Fallback for browsers that don't support Clipboard API
        const textArea = document.createElement('textarea');
        textArea.value = text;
        $('body').append(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            const msg = successful ? 'successful' : 'unsuccessful';
            console.log('Fallback: Copying text command was ' + msg);
            // 显示复制成功提示
            const originalText = copyButton.textContent;
            copyButton.textContent = '已复制!';
            setTimeout(() => {
                copyButton.textContent = originalText;
            }, 2000);
        } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
            copyButton.textContent = '复制失败!';
        }

        $(textArea).remove();
    }
}

function displayCode(sqlQuery) {
    // 显示 SQL 查询
    if (sqlQuery) {
        sqlQueryCode.text(sqlQuery);
        codeBlock.css('display', 'block');
        hljs.highlightBlock(sqlQueryCode[0]);
    }
}

// Function to process a single line of the streamed data
function processStreamLine(line, totalMessage) {
    let sqlQuery = "";
    let finished = false;
    if (line.startsWith("[DATA]: ")) {
        const jsonStr = line.substring(7); // Remove "[DATA]: " prefix
        try {
            const parsedData = JSON.parse(jsonStr);

            if (parsedData.error) {
                hasErrors = true;
                displayErrorMessage(parsedData.message || 'Error occurred');
            }
            if (parsedData.message) {
                // 显示中间消息
                const now = new Date();
                const timeString = now.toLocaleTimeString('en-US', { hour12: false });
                totalMessage = `${totalMessage}##### ${timeString}: \n\n${parsedData.message}\n\n`;
                displayInfoMessage(parsedData.message);
            }

            if (parsedData.sql_query && parsedData.sql_description) {
                sqlQuery = `-- ${parsedData.sql_description}\n\n${parsedData.sql_query}`;
                displayCode(sqlQuery);
            }

            // 处理推理内容
            if (parsedData.reasoning_content) {
                reasoningContent.html(md.render(parsedData.reasoning_content));
                reasoningContainer.css('display', 'block');
            }

            if (parsedData.columns && parsedData.data) {
                renderTable(parsedData);
                displayInfoMessage("");
            }
        } catch (e) {
            console.error("JSON parse error:", e, "line:", jsonStr);
            displayErrorMessage(`数据解析失败: ${jsonStr}`)
        }
    } else if (line.startsWith("[DONE]: ")) {
        finished = true;
    }
    return { sqlQuery, totalMessage, finished };
}

// Function to handle the streamed response
function handleStreamedResponse(response, originalBtnText) {
    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const sqlQueryList = [];
    let finished = false;
    tableContainer.empty();
    let bufferedData = '';
    let totalMessage = "";

    function read() {
        reader.read().then(({ done, value }) => {
            if (done) {
                disableEnableElements('enable-after-query', { originalBtnText });
                displayCode(sqlQueryList.join("\n\n\n"));
                if (!hasErrors) { displayInfoMessage(""); }
                updateQueryHistory();
                return;
            }
            const chunk = decoder.decode(value, { stream: true });
            bufferedData += chunk;

            let lines = bufferedData.split('\n');
            bufferedData = lines.pop(); // Keep the last part as it might be incomplete

            lines.filter(line => line.trim() !== '').forEach(line => {
                const result = processStreamLine(line, totalMessage);
                sqlQuery = result.sqlQuery;
                totalMessage = result.totalMessage;
                finished = result.finished;
                if (sqlQuery && !sqlQueryList.includes(sqlQuery)) {
                    sqlQueryList.push(sqlQuery);
                }
            });
            read(); // 继续读取流
        }).catch(error => {
            if (finished) {
                displayInfoMessage("");
                disableEnableElements('enable-after-query', { originalBtnText });
                return;
            }
            console.error("Stream reading error:", error);
            loadingMask.css('display', 'none');
            displayErrorMessage(`发生错误，请重试${error}`);
            disableEnableElements('enable-after-query', { originalBtnText });
            reader.cancel(); // 取消 stream reader
        });
    }
    read(); // 开始读取流
}

function updateQueryHistory() {
    fetch('query_history/mine', {
        method: 'GET'
    })
        .then(response => response.json())
        .then(historyData => {
            userQueryHistoryListDropdown.empty();
            userQueryHistoryListDropdown.append($('<option>', {
                value: '',
                text: '历史查询记录',
                disabled: true,
                selected: true
            }));
            historyData.forEach(historyItem => {
                const displayText = `${historyItem.insert_time.substring(5, 16)}, ${historyItem.query.length > 50 ? historyItem.query.substring(0, 50) + '...' : historyItem.query}`;
                userQueryHistoryListDropdown.append($('<option>', {
                    value: historyItem.query,
                    text: displayText
                }));
            });
            userQueryHistoryListDropdown.append($('<option>', {
                value: '',
                text: '清空'
            }));
        })
        .catch(error => {
            console.error("Failed to fetch query history:", error);
        });
}

// 监听 Enter 键按下事件
queryInput.on('keydown', function (event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault(); // 阻止默认的换行行为
        submitQuery();
    }
});

submitBtn.on("click", submitQuery);

timerInterval = -1;

// Function to handle query submission
function submitQuery() {
    hasErrors = false;

    // Store original button text and update to "processing"
    let seconds = 0;
    const originalBtnText = submitBtn.text();
    disableEnableElements('disable-for-query', { btnText: `处理中...${seconds}s` });


    loadingTimer.text(seconds + 's');
    timerInterval = setInterval(() => {
        seconds++;
        loadingTimer.text(seconds + 's');
        submitBtn.text(`处理中...${seconds}s`);
    }, 1000);

    const query = queryInput.val();

    // 使用 Fetch API 处理流式响应
    fetch('query/stream', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `query=${encodeURIComponent(query)}`
    }).then(response => handleStreamedResponse(response, originalBtnText));
}

function renderTable(tableData) {
    tableData.sort = true;
    const randomTableId = `table-${Math.random().toString(36).substring(2, 15)}`;

    // Create a new div element to hold the table
    const tableChild = $(`<div class="table-container-child">
        <div class="table-header-lines">
        <h4>${tableData.sql_description}:</h4>
        <div class="download-export-btns">
            <button type="button" id="download-csv-btn-${randomTableId}" class="action-buttons download-as-csv">下载CSV</button>
            <button type="button" id="import-feishu-btn-${randomTableId}" class="action-buttons export-to-feishu">导入到飞书</button>
        </div></div>
    </div>`);
    const tableChildContainerDiv = $(`<div class="single-table-container" id="${randomTableId}"></div>`);
    tableChild.append(tableChildContainerDiv);
    tableContainer.prepend(tableChild);
    new gridjs.Grid(tableData).render(document.getElementById(randomTableId));
    // tableChildContainerDiv.Grid(tableData);

    let callback = downloadCSV.bind(this, tableData);


    // 绑定下载CSV事件
    $(`#download-csv-btn-${randomTableId}`).on('click', callback);

    // 绑定导入到飞书事件
    const exportToFeishuBtn = $(`#import-feishu-btn-${randomTableId}`);
    const exportCallback = importToFeishu.bind(exportToFeishuBtn, tableData);
    exportToFeishuBtn.on('click', exportCallback);
}

// 生成CSV内容 (moved the implementation from the commented-out downloadCSV function)
function generateCsvContent(tableData) {
    let csvContent = tableData.columns.map(col => `"${col.replace(/"/g, '""')}"`).join(",") + "\n";
    tableData.data.forEach(row => {
        csvContent += row.map(cell => {
            if (typeof cell === "string") {
                return `"${cell.replace(/"/g, '""')}"`; // Escape double quotes
            }
            return cell;
        }).join(",") + "\n";
    });
    return csvContent;
}

// 下载CSV 函数
function downloadCSV(tableData) {
    const csvContent = generateCsvContent(tableData);
    if (!csvContent) return;

    // 创建下载链接
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `${tableData.sql_description}_查询结果.csv`);
    link.style.visibility = 'hidden';
    $('body').append(link);
    link.click();
    $(link).remove();
}

// 导入到飞书
function importToFeishu(tableData) {
    const csvContent = generateCsvContent(tableData);
    if (!csvContent) {
        displayErrorMessage('没有可导入的数据');
        return;
    }

    // 禁用所有操作按钮
    const exportToFeishuBtn = this;
    const originalBtnText = exportToFeishuBtn.text(); // Use dynamic ID
    exportToFeishuBtn.text("处理中...")
    exportToFeishuBtn.prop("disabled", true);

    const query = encodeURIComponent(queryInput.val());
    const reasoningContentStr = encodeURIComponent(reasoningContent.text());
    const sqlDescription = encodeURIComponent(tableData.sql_description);
    console.log("reasoningContentStr:", reasoningContentStr)

    fetch('import_to_feishu', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        body: `query=${query}&csv_content=${encodeURIComponent(csvContent)}&reasoning_content=${reasoningContentStr}&name=${sqlDescription}`
    })
        .then(response => response.json())
        .then(data => {
            // 恢复按钮状态
            exportToFeishuBtn.text(originalBtnText);
            exportToFeishuBtn.prop("disabled", false);
            if (data.status === 'success') {
                showPopupMessage(`${data.message}<br><a href="${data.url}" target="_blank">${data.name}</a>`, false, 8);
            } else {
                showPopupMessage(`${data.message}<br>Error: ${data.error}<br>Detail: ${data.detail}`, true);
            }
        })
        .catch(error => {
            // 恢复按钮状态
            exportToFeishuBtn.text(originalBtnText);
            exportToFeishuBtn.prop("disabled", false);
            console.error(error);  // Log the full error to the console
            showPopupMessage(`导入失败，请重试<br>${error}<br>${error.stack}`, true);
        });
}


function showPopupMessage(message, isError = false, autoCloseTimeInSeconds = 3) {
    const popup = $('#feishu-import-popup');
    const popupMessageContent = $('#popup-message-content');

    // 将换行符转换为<br>标签
    const formattedMessage = message.replace(/\n/g, '<br>');
    popupMessageContent.html(formattedMessage);

    if (isError) {
        popupMessageContent.addClass('error-message');
        popupMessageContent.removeClass('info-message');
    } else {
        popupMessageContent.removeClass('error-message');
        popupMessageContent.addClass('info-message');
    }
    popup.css('display', 'block');

    // 3秒后自动关闭弹窗
    setTimeout(function () {
        popup.css('display', 'none');
    }, 1000 * autoCloseTimeInSeconds);
}

// 关闭弹窗
$('#popup-close-btn').on('click', function () {
    $('#feishu-import-popup').css('display', 'none');
});
