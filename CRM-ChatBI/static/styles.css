body {
    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #fff;
    color: #333;
}

#app th.gridjs-th, #app td.gridjs-td{
    padding: 6px;
}

h1 {
    text-align: center;
    font-weight: 600;
    font-size: 1.8em; /* Slightly smaller font size */
    margin-bottom: 20px; /* Increased margin */
}

.header-info {
    display: flex;
    justify-content: space-between; /* Align items to the edges */
    align-items: center; /* Vertically center items */
    padding: 0 20px;
    margin-bottom: 1rem;
    background-color: lightskyblue;
    color: #fff;
}

.user-info {
    padding-top: 0; /* Remove padding-top */
    padding-left: 0; /* Remove padding-left */
    display: flex; /* Use flexbox for alignment */
    align-items: center; /* Vertically center items */
}

.user-info img {
    margin-right: 10px; /* Space between image and text */
}

.user-info b {
    font-size: 0.9em; /* Smaller font size for username */
}

#query-form {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column; /* Stack elements vertically */
    align-items: flex-start;
    width: 100%; /* Take full width */
}

p.input-block{
    margin-top: 0;
    margin-bottom: 10px;
}

#message{
    max-height: 40vh;
    overflow-y: auto;
    width: 100%;
    overflow-x: auto;
}

#query-input {
    width: 600px;
    padding: 15px;
    max-height: 100px;
    margin-right: 0;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 8px;
    font-size: 1em;
    box-sizing: border-box;
}

.table-header-lines {
    display: flex;
    justify-content: space-between;
}

.table-header-lines h4 {
    margin: 0;
    padding: 10px;
}

.action-buttons {
    padding: 5px 10px;
    margin-right: 5px;
    border: none;
    border-radius: 8px;
    background-color: #007aff;
    color: #fff;
    font-size: 1em;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3sease;
    width: auto;
    min-width: 100px;
    max-width: 600px;
}

.action-buttons:disabled {
    color: darkgray;
    background-color: lightgrey;
}

.action-buttons:hover {
    background-color: #0056b3;
}

#results-container {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

#table-container {
    width: 100%;
    overflow-x: auto;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    margin-bottom: 20px;
}

#data-table {
    border-collapse: collapse;
    white-space: nowrap;
    width: 100%;
}

#data-table th,
#data-table td {
    border: 1px solid #ddd;
    padding: 12px;
    text-align: left;
    font-size: 0.9em;
}

#data-table th {
    background-color: #f2f2f2;
    font-weight: 500;
}

#chart-container {
    width: 100%;
    height: 500px;
}

.code-block {
    position: relative;
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin: 20px 0;
    font-family: Courier New;
    width: 100%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.copy-button {
    position: absolute;
    top: 8px;
    right: 8px;
    padding: 5px 10px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.8em;
}

.code-block:hover .copy-button {
    opacity: 1;
}

.copy-button:hover {
    background: #f0f0f0;
}

/* 代码高亮的自定义样式 */
.hljs {
    background: #f8f8f8;
    padding: 0;
    border-radius: 4px;
}

/* Loading mask styles */
#loading-mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    color: white;
    font-size: 2em;
}

.template-select {
    padding: 8px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 6px;
    font-size: 0.9em;
    background-color: #fff;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 600px;
}
.template-select + .template-select{
    margin-left: 0;
}

.error-message {
    color: red;
}

.info-message {
    color: #666;
}

/* Popup styles */
.popup {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* Popup Content/Box */
.popup-content {
    background-color: #fefefe;
    margin: 15% auto; /* 15% from the top and centered */
    padding: 20px;
    border: 1px solid #888;
    width: 80%; /* Could be more or less, depending on screen size */
    border-radius: 10px;
    position: relative;
}

/* The Close Button */
.popup-close-button {
    color: #aaa;
    position: absolute;
    top: 0;
    right: 0;
    font-size: 28px;
    font-weight: bold;
}

.popup-close-button:hover,
.popup-close-button:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

#reasoning-container {
    margin: 20px auto;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: none;
}

#reasoning-content {
    color: #555;
    line-height: 1.6;
    overflow-wrap: break-word;
}

#reasoning-container h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

#actionButtonContainer{
    display: flex;
    flex-direction: row;
    align-items: flex-start;
}

@media (min-width: 800px) {
    #app{
        padding: 0 20px;
    }
}

/* Media query for smaller screens */
@media (max-width: 768px) {
    .header-info {
        flex-direction: row;
        align-items: flex-end;
        padding: 0 10px;
    }
    .user-info {
        margin-top: 0.5rem;
    }
    h1 {
        font-size: 1.5em;
        margin-bottom: 10px;
    }

    #message{
        max-height: 50vh;
    }

    #query-input {
        width: 95%; /* Wider input on smaller screens */
        font-size: 0.9em;
        padding: 10px;
    }

    .action-buttons {
        font-size: 0.9em;
        padding: 10px 20px;
        min-width: auto;
    }

    #table-container,
    .code-block,
    #reasoning-container {
        width: 95%; /* Wider containers on smaller screens */
    }

    #data-table th,
    #data-table td {
        padding: 10px;
        font-size: 0.8em;
    }
    .template-select{
        max-width: 95%;
        width: 95%;
        min-width: auto;
    }
    .table-header-lines {
        flex-flow: column;
    }
}

/* Media query for even smaller screens (e.g., iPhone SE) */
@media (max-width: 375px) {
    h1 {
        font-size: 1.3em;
    }
    #query-input, #submit-btn, #download-csv-btn, .template-select, #table-container, .code-block, #reasoning-container {
        width: 100%;
    }
}
