import logging

# Get the logger for chat_bi
logger = logging.getLogger("chat_bi")
logger.setLevel(logging.INFO)

# Create a file handler and set the level to INFO
file_handler = logging.FileHandler("./chatbi_app.log")
file_handler.setLevel(logging.INFO)

# Create a stream handler (console handler) and set the level to INFO
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)

# Create a formatter and add it to the handlers
formatter = logging.Formatter(
    "%(asctime)s - %(levelname)s - %(name)s - %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
)
file_handler.setFormatter(formatter)
console_handler.setFormatter(formatter)

# Add the handlers to the logger
logger.addHandler(file_handler)
logger.addHandler(console_handler)