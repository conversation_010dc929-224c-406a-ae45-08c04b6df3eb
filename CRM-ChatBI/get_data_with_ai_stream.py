import json
import os
from logger import logger
import sys
import pandas as pd
from datetime import datetime

# Add the scripts directory to the sys.path
sys.path.append("../")

from odps_client import get_odps_sql_result_as_df
from langchain_core.tools import tool
from llm_client import llm, llm_with_tools
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from system_prompt_builder import get_system_prompt, table_info
from decimal import Decimal


# Check if a column contains Decimal type values
def is_decimal_column(series: pd.Series):
    return series.apply(lambda x: isinstance(x, Decimal)).any()


def fix_odps_issue(tool_call, user_query: str, error: str) -> dict:
    fix_result = llm_with_tools.invoke(
        [
            HumanMessage(content=f"用户提问:{user_query}\n\n{table_info}"),
            AIMessage(content=f"{tool_call}"),
            HumanMessage(
                content=f"请你修复你的SQL。请注意，ODPS兼容Hive的语法。异常信息：{error}"
            ),
        ]
    )
    if fix_result.tool_calls:
        return fix_result.tool_calls[0]
    logger.error(f"未能修复SQL:{tool_call.get('args')}, fix_result:{fix_result}")
    return None


def get_odps_sql_result_as_json_real(
    sql: str, sql_description: str = ""
) -> tuple[pd.DataFrame, bool]:
    df = get_odps_sql_result_as_df(sql)
    logger.info(f"sql_description:{sql_description}")
    if not df.empty:
        has_chinese = False
        # 检查列名中是否至少有一列包含中文字符
        logger.info(f"df.columns: {df.columns}")

        # Convert Decimal columns to float to avoid JSON serialization issues
        for col in df.columns:
            if is_decimal_column(df[col]):
                df[col] = df[col].apply(float)

        for col in df.columns:
            if any("\u4e00" <= c <= "\u9fff" for c in col):
                has_chinese = True
                break
        df = df.astype(str)
        return df, has_chinese
    else:
        return pd.DataFrame(), False


ensure_all_chinese_columns_prompt = """OK很好，刚才的SQL已经可以执行，且结果准确。
但客户希望获取到的结果中，所有列名都是中文，请确保输出的所有列名都是中文。"""

no_answer_prompt = "我只能回答销售数据相关的问题，你的问题我回答不了。"

import requests


def yield_message(
    message: str,
    sql_query: str = None,
    sql_description: str = None,
    error: bool = False,
    reasoning_content: str = None,
) -> str:
    ai_message = {"message": message}
    if sql_query:
        ai_message["sql_query"] = sql_query
    if error:
        ai_message["error"] = True
    if sql_description:
        ai_message["sql_description"] = sql_description
    if reasoning_content:
        ai_message["reasoning_content"] = reasoning_content
    return yield_json_data(ai_message)


def yield_json_data(ai_message: dict) -> str:
    return f"[DATA]: {json.dumps(ai_message, ensure_ascii=False)}\n\n"


def yield_dataframe_to_client(
    df: pd.DataFrame,
    sql_query: str = "",
    reasoning_content: str = "",
    sql_description: str = "",
) -> str:
    is_valid_result = isinstance(df, pd.DataFrame) and not df.empty
    if not is_valid_result:
        logger.error(f"不符合预期的df:{df}")

    table_data = {
        "columns": df.columns.tolist() if is_valid_result else ["无数据"],
        "data": (
            [list(row) for row in df.itertuples(index=False)]
            if is_valid_result
            else [
                [f"您的问题太难了，AI也回答不了（请把SQL发给研发看看）\n\n{sql_query}"]
            ]
        ),
        "sql_query": sql_query,
        "reasoning_content": reasoning_content,
        "sql_description": sql_description,
    }
    return yield_json_data(table_data)


def extract_core_problem_for_query(
    query: str = "在10月购买了爱乐薇(铁塔)淡奶油但是在11月没有购买的用户有哪些？按照他们的10月采购总额倒序排序，列出它们ID、名字、BD名字、省市区、最后下单时间。",
    user: str = None,
):
    today_chinese = datetime.now().strftime("%Y年%m月%d日")
    add_user_filter = (
        "" if user is None else f"提问的用户的名字是:{user},这非常重要!!\n"
    )
    user_to_ask = user if user is not None else "用户"
    core_content = f"""{add_user_filter}{query}
    
    咱们一步一步的分析{user_to_ask}的问题，仅需要列出解答用户问题所需的每个步骤以及关键信息。
    
    咱们无需回答与这些表内容无关的问题。如果用户问了无关的问题，请一定要回答‘{no_answer_prompt}’"""

    resolving_steps_content = ""
    reasoning_content = ""
    data = {
        "model": "ep-20250206185410-zzb56",
        "messages": [
            {
                "role": "user",
                "content": get_system_prompt(),
            },
            {"role": "assistant", "content": "好的。"},
            {"role": "user", "content": core_content},
        ],
        "stream": True,
    }

    try:
        response = requests.post(
            "https://ark.cn-beijing.volces.com/api/v3/chat/completions",
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {os.getenv('ARK_API_KEY')}",
            },
            json=data,
            stream=True,
        )
    except requests.RequestException as e:
        logger.error(f"Request failed: {e}")
        yield yield_message(f"请求失败: {e}", error=True)
        return

    final_content = ""
    final_reasoning_content = ""
    if response.status_code == 200:
        for line in response.iter_lines():
            if line:
                line_content = line.decode("utf-8").strip()
                if line_content.startswith("data: "):
                    try:
                        json_content = line_content[6:]  # Remove "data: " prefix
                        if len(json_content) <= 2 or "[DONE]" == json_content:
                            # JSON content should be at least {}
                            logger.warning(
                                f"接收到了非JSON数据:{line_content}, 将跳过.."
                            )
                            continue
                        delta_content = json.loads(json_content)
                        if delta_content:
                            choices = delta_content.get("choices")
                            if choices and len(choices) > 0:
                                delta = choices[0].get("delta")
                                if delta:
                                    reasoning_content = delta.get(
                                        "reasoning_content", ""
                                    )
                                    content = delta.get("content", "")
                                    if reasoning_content:
                                        final_reasoning_content += reasoning_content
                                        yield final_reasoning_content
                                    elif content:
                                        final_content += content
                                        yield f"{final_reasoning_content}\n---\n{final_content}"

                    except json.JSONDecodeError as e:
                        logger.error(f"JSONDecodeError: {e} - Data: {line_content}")
                        continue  # Skip to the next line if JSON decoding fails
        logger.info(f"Yielding final content: {final_content}")
    else:
        logger.error(f"Error: {response.status_code} - {response.text}")
        yield yield_message(f"请求失败，状态码: {response.status_code}", error=True)
        return

    resolving_steps_content = final_content
    if no_answer_prompt in resolving_steps_content:
        yield resolving_steps_content
        return
    yield f"""{user_to_ask}提问: {query}

解决该问题的关键SQL:
{resolving_steps_content}

请提取以上分析过程中的SQL和其他关键信息，然后返回结构化的答案，用以调用代码获取答案。
__final_reasoning_content__
{final_reasoning_content}
"""


def _prepare_initial_data(query: str, user: str):
    """Initializes the data dictionary and extracts the core problem."""
    # logger.info(f"用户问题：{query}")
    response_data = {
        "messages": [],
        "sql_query": None,
        "data": {},
        "error": None,
        "final_reasoning_content": "",
    }

    core_problem = ""
    try:
        for core_problem_chunk in extract_core_problem_for_query(
            query=query, user=user
        ):
            yield yield_message(core_problem_chunk)
            core_problem = core_problem_chunk
    except Exception as e:
        error_msg = f"核心问题提取失败: {e}"
        logger.error(f"Error during core problem extraction: {e}")
        yield yield_message(error_msg, error=True)
        response_data["error"] = str(e)
        yield f"[DONE]: \n\n"
        return response_data, None, None  # Return early on error

    if no_answer_prompt in core_problem or not core_problem:
        logger.warning(f"这个问题没有答案：{query}...{core_problem}")
        yield yield_message(core_problem, error=True)
        response_data["sql_query"] = core_problem
        yield f"[DONE]: \n\n"
        return response_data, None, None  # Return early if no answer

    if "__final_reasoning_content__" in core_problem:
        try:
            core_problems = core_problem.split("__final_reasoning_content__")
            final_reasoning_content = core_problems[1]
            yield yield_message(
                message="AI分析完成了", reasoning_content=final_reasoning_content
            )
            response_data["final_reasoning_content"] = final_reasoning_content
            core_problem = core_problems[0]
        except IndexError as e:
            error_msg = f"解析最终推理内容失败: {e}"
            logger.error(error_msg)
            yield yield_message(error_msg, error=True)
            return response_data, None, None  # Return early on error

    logger.info(f"提取出来的核心问题和信息:{core_problem}")
    yield yield_message(core_problem)
    response_data["messages"].append(core_problem)
    return response_data, core_problem, final_reasoning_content


def _prepare_messages(core_problem: str):
    """Prepares the initial messages for the language model."""
    today_chinese = datetime.now().strftime("%Y年%m月%d日")
    return [
        HumanMessage(content="今天是哪一天？"),
        AIMessage(content=f"今天是{today_chinese}"),
        HumanMessage(
            content=f"咱们无需回答与这些表内容无关的问题:{table_info}\n\n\n如果用户问了无关的问题，请一定要回答“{no_answer_prompt}”，明白吗？"
        ),
        AIMessage(content=f"好的明白了。"),
        HumanMessage(content=core_problem),
    ]


def _process_tool_call(
    tool_call,
    response_data,
    final_reasoning_content,
    result,
    user_query="",
    can_retry=2,
):
    """Processes a single tool call from the AI."""
    call_using = tool_call
    # logger.info(f"new call_using:{call_using}")

    if call_using["name"] == "get_odps_sql_result_as_json":
        args = call_using["args"]

        sql_query = args["sql"]
        sql_description = args["sql_description"]
        response_data["sql_query"] = sql_query

        yield yield_message(
            f"请求数据库获取数据中...{sql_description}",
            sql_query=sql_query,
            sql_description=sql_description,
        )
        response_data["messages"].append("AI重新思考后返回了新的SQL: " + sql_query)

        try:
            result, has_chinese = get_odps_sql_result_as_json_real(
                sql_query, sql_description
            )
        except Exception as e:
            logger.error(f"can_retry:{can_retry}, SQL执行异常:{e}, SQL: {sql_query}")
            if can_retry <= 0:
                yield yield_message(
                    message=f"AI无法修复SQL错误:{e}",
                    sql_query=sql_query,
                    sql_description=sql_description,
                )
                return "error", result, sql_query, sql_description
            if "ODPS" in f"{e}" or "Semantic analysis exception" in f"{e}":
                # 尝试让AI修复语法错误
                yield yield_message(
                    f"尝试让AI修复语法错误:{e}, sql_query:{sql_query}",
                    sql_query=sql_query,
                    error=True,
                )
                new_tool_call = fix_odps_issue(
                    tool_call=tool_call, user_query=user_query, error=f"{e}"
                )
                if new_tool_call is None:
                    yield yield_message(
                        f"AI修复语法错误失败了:{e}", sql_query=sql_query, error=True
                    )
                    raise e
                else:
                    yield yield_message(
                        f"AI修复语法错误成功了:{new_tool_call}", error=False
                    )
                    return (
                        yield from _process_tool_call(
                            tool_call=new_tool_call,
                            response_data=response_data,
                            final_reasoning_content=final_reasoning_content,
                            result=result,
                            user_query=user_query,
                            can_retry=can_retry - 1,
                        )
                    )

        yield yield_dataframe_to_client(
            df=result,
            sql_query=sql_query,
            reasoning_content=final_reasoning_content,
            sql_description=sql_description,
        )
        response_data["data"] = result
    return "success", result, sql_query, sql_description


def _handle_ai_response(
    tool_call_results,
    response_data,
    sql_query,
    sql_description,
    result,
):
    """Handles the AI's response, including tool calls and messages."""
    ai_message = tool_call_results.content
    if no_answer_prompt in ai_message:
        yield yield_message(ai_message, error=True)
        return (
            "no_answer",
            result,
            sql_query,
            sql_description,
        )  # Indicate AI cannot answer

    if ai_message:
        yield yield_message(ai_message)
        response_data["messages"].append(ai_message)

    tool_calls = tool_call_results.tool_calls
    if not isinstance(tool_calls, list) or len(tool_calls) <= 0:
        _error_msg = f"AI返回的tool_calls不是list"
        yield yield_message(_error_msg, error=True)
        return (
            "invalid_tool_calls",
            result,
            sql_query,
            sql_description,
        )  # Indicate invalid tool calls

    return "continue", result, sql_query, sql_description  # ready to process tool calls


def get_data_with_ai_stream(
    query: str = "昨天一共有多少个门店下单？",
    user: str = None,
    callback=None,
):
    response_data, core_problem, final_reasoning_content = (
        yield from _prepare_initial_data(query, user)
    )

    def handle_callback(data):
        if callback:
            callback(data)

    if response_data.get("error"):
        handle_callback(response_data)
        return

    if core_problem is None:
        logger.warning(f"core_problem is None, terminated. {response_data}")
        handle_callback(response_data)
        return

    messages = _prepare_messages(core_problem)

    sql_query = ""
    sql_description = ""
    result = pd.DataFrame()

    started_at = datetime.now()
    tool_call_results = llm_with_tools.invoke(messages)
    duration = datetime.now() - started_at
    token_info = f"AI耗时:{duration.total_seconds()}秒, 速率:{tool_call_results.response_metadata}"
    logger.info(token_info)
    yield yield_message(token_info)

    status, result, sql_query, sql_description = yield from _handle_ai_response(
        tool_call_results,
        response_data,
        sql_query,
        sql_description,
        result,
    )

    if status == "continue":
        is_multiple_tools = len(tool_call_results.tool_calls) > 1
        for tool_call in tool_call_results.tool_calls:
            process_status, result, sql_query, sql_description = (
                yield from _process_tool_call(
                    tool_call=tool_call,
                    response_data=response_data,
                    final_reasoning_content=final_reasoning_content,
                    result=result,
                    user_query=query,
                )
            )

            if is_multiple_tools:
                logger.info(f"{sql_description} 已完成，继续下个表格的获取...")
                continue
    response_data.setdefault("data", {"result": [], "sql_query": sql_query})
    handle_callback(response_data)
    yield "[DONE]: "
