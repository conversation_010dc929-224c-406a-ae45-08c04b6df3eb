<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>销售AI小助手-历史记录</title>
  <link rel="icon" type="image/x-icon" href="//azure.summerfarm.net/favicon.ico">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
  <style>
  body {
    padding: 20px;
  }
  
  .form-group {
    display: inline-block;
    margin-right: 20px;
  }
  </style>
</head>
<body>
<form>
  <div class="form-group">
    <label for="name">用户名:</label>
    <input type="text" class="form-control" id="name" name="name">
  </div>
  <div class="form-group">
    <label for="email">用户email:</label>
    <select class="form-control" id="email" name="email">
        <option value="">全部</option>
        {% for email in emails %}
            <option value="{{ email }}">{{ email }}</option>
        {% endfor %}
    </select>
  </div>
  <button type="submit" class="btn btn-primary">查询</button>
  <a href="{{hostname}}/"  class="btn btn-primary">返回🔙</a>
</form>
<table class="table table-striped table-bordered table-hover">
  <thead>
    <tr>
      <th>用户</th>
      <th>用户email</th>
      <th>query</th>
      <th>SQL</th>
      <th>操作时间</th>
      <th>Sample Data</th>
    </tr>
  </thead>
  <tbody>
    {% for item in user_query_history_list %}
    <tr>
      <td>{{ item.user_name }}</td>
      <td>{{ item.user_email }}</td>
      <td>{{ item.query }}</td>
      <td>{{ item.sql }}</td>
      <td>{{ item.insert_time }}</td>
      <td>{{ item.sample_data }}</td>
    </tr>
    {% endfor %}
  </tbody>
</table>
<ul class="pagination">
  {% if max_page > 1 %}
  <li class="page-item"><a class="page-link" href="?page={{ max_page }}">最后一页</a></li>
  {% endif %}
  {% if page > 1 %}
  <li class="page-item"><a class="page-link" href="?page={{ page - 1 }}">上一页</a></li>
  {% endif %}
  {% for i in range(1, max_page + 1) %}
  <li class="page-item"><a class="page-link" href="?page={{ i }}">{{ i }}</a></li>
  {% endfor %}
  {% if page < max_page %}
  <li class="page-item"><a class="page-link" href="?page={{ page + 1 }}">下一页</a></li>
  {% endif %}
  {% if max_page > 1 %}
  <li class="page-item"><a class="page-link" href="?page=1">第一页</a></li>
  {% endif %}
</ul>
</body>
</html>
