<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>销售AI小助手</title>
    <link rel="icon" type="image/x-icon" href="//azure.summerfarm.net/favicon.ico">
    <link rel="stylesheet" href="{{hostname}}/static/styles.css?_t={{cache_control_timestamp}}">
    <!-- 添加代码高亮的CSS和JS -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/highlight.js/11.9.0/styles/default.min.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/highlight.js/11.9.0/languages/sql.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/markdown-it/13.0.1/markdown-it.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- gridjs you may need to self-host or try jsdelivr -->
    <script src="https://cdn.jsdelivr.net/npm/gridjs/dist/gridjs.umd.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/gridjs/dist/theme/mermaid.min.css" rel="stylesheet" />
    <!-- 飞书远程调试 -->
    <script src='https://lf-package-cn.feishucdn.com/obj/feishu-static/op/fe/devtools_frontend/remote-debug-0.0.1-alpha.6.js'></script>

    <!-- <script src="https://unpkg.com/gridjs-jquery/dist/gridjs.production.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/gridjs/dist/theme/mermaid.min.css" /> -->

    <!-- <script src="https://cdn.bootcdn.net/ajax/libs/gridjs/6.2.0/gridjs.min.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.bootcdn.net/ajax/libs/gridjs/6.2.0/theme/mermaid.min.css" /> -->
</head>

<body>
    <div class="header-info">
        <h1>销售AI小助手</h1>
        <span class="user-info"><a target="_blank" href="https://summerfarm.feishu.cn/drive/home/"><img
                    src="{{ user_info.data.avatar_thumb}}" style="width: 30px;height: 30px;border-radius: 15px;"></a>
            欢迎，<b>{{ user_info.data.name }}！</b>
        </span>
    </div>
    <div id="app">
        <p class="input-block">
            <select id="user_query_history_list" class="template-select">
                <option value="">历史查询记录</option>
                {% if user_query_history_list %}
                {% for history in user_query_history_list %}
                <option value="{{ history.query }}">{{ history.insert_time[5:16] }}, {{ history.query[:50] + '...' if
                    history.query|length > 50 else history.query }}</option>
                {% endfor %}
                {% else %}
                <option value="">暂无历史查询记录</option>
                {% endif %}
            </select>
        </p>
        {% if system_query_templates %}
        <p class="input-block">
            <select id="system_query_templates" class="template-select">
                <option value="">选择系统模版</option>
                {% for template in system_query_templates %}
                <option value="{{ template }}">{{ template[:56] + '...' if template|length > 50 else template }}
                </option>
                {% endfor %}
            </select>
        </p>
        {% endif %}

        <p class="input-block">
            <textarea id="query-input" name="query" rows="4" cols="30"
                placeholder="输入您的查询，例如：我的客户中，上个月有下单，但是目前为止还未下单的客户有哪些？列出他们ID、名字、手机号、11月下单额、最后一次下单日期、最后1次下单商品明细（聚合起来），按照11月下单额倒序排序">{{ query }}</textarea>
        <div id="actionButtonContainer">
            <button type="submit" id="submit-btn" class="action-buttons">查询</button>
        </div>
        </p>

        <div id="results-container">
            <div id="message" class="info-message">{{ message }}</div>
            <div id="table-container"></div>
            <div class="code-block" style="display:none;">
                <button class="copy-button" onclick="copyCode()">复制</button>
                <pre><code class="language-sql" id="sql-query-code"></code></pre>
            </div>
        </div>
        <div id="reasoning-container">
            <h3>AI 分析过程</h3>
            <div id="reasoning-content"></div>
        </div>
    </div>

    <!-- The Popup -->
    <div id="feishu-import-popup" class="popup" style="display: none;">
        <div class="popup-content">
            <span class="popup-close-button" id="popup-close-btn">&times;</span>
            <p id="popup-message-content"></p>
        </div>
    </div>

    <div id="loading-mask">数据加载中...<span id="loading-timer"></span></div>

    <script src="{{hostname}}/static/scripts.js?_t={{cache_control_timestamp}}"></script>
</body>

</html>