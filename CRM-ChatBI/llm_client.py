import os
from langchain_openai import ChatOpenAI
from langchain_core.tools import tool


llm_gemini = ChatOpenAI(
    api_key=os.getenv("LITELLM_MASTER_KEY"),
    base_url="http://127.0.0.1:4000/v1",
    model="gemini-2.0-flash-exp",
    temperature=1,
    timeout=60,
)

llm_deepseek = ChatOpenAI(
    api_key=os.getenv("ARK_API_KEY"),
    base_url="https://ark.cn-beijing.volces.com/api/v3",
    model="ep-20250206185410-zzb56",
    temperature=0.7,
    timeout=60,
)

llm_deepseek_v3 = ChatOpenAI(
    api_key=os.getenv("XM_FAST_GPT_API_KEY"),
    base_url="https://litellm-test.summerfarm.net/v1",
    model="deepseek-v3",
    temperature=0.7,
    timeout=60,
)

llm_qwen_max = ChatOpenAI(
    api_key=os.getenv("XM_FAST_GPT_API_KEY"),
    base_url="https://litellm-test.summerfarm.net/v1",
    model="qwen-max",
    temperature=0.7,
    timeout=60,
)

llm_gpt_4o_one_api = ChatOpenAI(
    api_key=os.getenv("XM_FAST_GPT_API_KEY"),
    base_url="https://litellm-test.summerfarm.net/v1",
    model="gpt-4o",
    temperature=0.7,
    timeout=60,
)

llm = llm_gpt_4o_one_api


@tool
def get_odps_sql_result_as_json(sql: str, sql_description: str = ""):
    """
    获取ODPS SQL查询结果并返回JSONArray
    返回的数据中至少有一列包含中文字符，便于用户阅读。

    Args:
        sql (str): 阿里云ODPS(maxCompute) SQL查询语句。
        sql_description (str): 用来简要描述这段SQL的功能，长度不超过20个汉字。

    Returns:
        tuple[pandas.DataFrame, bool, str]: 返回一个元组，包含:
            - 查询结果的JSON数组
            - 布尔值，表示结果中的至少一列包含中文字符
            - 字符串，表示这个SQL的简要描述
    """


tools = [get_odps_sql_result_as_json]

# 此处可切换模型:
llm_with_tools = llm_gpt_4o_one_api.bind_tools(tools)
