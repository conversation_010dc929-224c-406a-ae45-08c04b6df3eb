import sys
from datetime import datetime

sys.path.append("../")

from odps_client import odps, get_odps_sql_result_as_df

table_configurations = [
    {
        "table_name": "summerfarm_tech.app_crm_chatbi_cust_orders_2yrs_df",
        "description": "客户下单数据表",
        "sample_data_rows": 2,
        "schema_variable": "order_table_schema",
        "sample_variable": "order_sample_json",
    },
    {
        "table_name": "summerfarm_tech.app_crm_chatbi_cust_info_df",
        "description": "客户信息表",
        "sample_data_rows": 1,
        "schema_variable": "cust_table_schema",
        "sample_variable": "cust_sample_json",
    },
    {
        "table_name": "summerfarm_tech.app_crm_chatbi_cust_visit_record_df",
        "description": "客户拜访记录表",
        "sample_data_rows": 1,
        "schema_variable": "cust_visit_table_schema",
        "sample_variable": "cust_visit_sample_json",
    },
]

def get_table_data(table_configurations: list = table_configurations):
    """Fetches table schemas and sample data from ODPS, using cached data if available."""

    table_schemas = {}
    table_sample_jsons = {}

    for config in table_configurations:
        table_name = config["table_name"]
        try:
            table = odps.get_table(name=table_name)
            table_schemas[config["schema_variable"]] = f"{table.get_ddl()}"
            sample_df = table.head(config["sample_data_rows"]).to_pandas()
            table_sample_jsons[config["sample_variable"]] = sample_df.iloc[0].to_json(
                force_ascii=False
            )
        except Exception as e:
            print(f"Error fetching data for table {table_name}: {e}")
            continue

    return table_schemas, table_sample_jsons


table_schemas, table_sample_jsons = get_table_data(table_configurations)
table_info = (
    "示例数据和表结构语句(请务必十分注意这些字段的格式，尤其是时间/日期格式)："
)
for config in table_configurations:
    table_info += f"""
```
{config["table_name"]}的表结构语句:
{table_schemas.get(config["schema_variable"], "Schema not found")}
数据样本:
{table_sample_jsons.get(config["sample_variable"], "Sample not found")}
```
"""
    
all_category_df = get_odps_sql_result_as_df(
    """SELECT  ARRAY_JOIN(COLLECT_SET(category1),',') 一级类目列表_category1
        ,ARRAY_JOIN(COLLECT_SET(category2),',') 二级类目列表_category2
        ,ARRAY_JOIN(COLLECT_SET(category3),',') 三级类目列表_category3
        ,ARRAY_JOIN(COLLECT_SET(category4),',') 四级类目列表_category4
FROM    summerfarm_tech.app_crm_chatbi_cust_orders_2yrs_df
WHERE   ds = MAX_PT('summerfarm_tech.app_crm_chatbi_cust_orders_2yrs_df')
AND     spu_name NOT LIKE '%测试%'
AND     CONCAT(category1,category2,category3,category4) NOT LIKE '%测试%'
    """
)
all_category_json = all_category_df.iloc[0].to_json(force_ascii=False)

all_province_df = get_odps_sql_result_as_df(
    """
SELECT  province
        ,COUNT(*) cnt
        ,MAX(last_order_time) last_order_time_of_group
        ,MAX(m_id) max_m_id
FROM    summerfarm_tech.ods_merchant_df
WHERE   ds = MAX_PT('summerfarm_tech.ods_merchant_df')
AND     last_order_time IS NOT NULL
AND     last_order_time >= '2024-01-01 00:00:00'
GROUP BY province
HAVING  cnt >= 5
ORDER BY cnt,last_order_time_of_group
"""
)
all_provinces_list = all_province_df["province"].unique()
all_provinces = ",".join(all_provinces_list)

def get_system_prompt(table_configurations: list = table_configurations):
    """Constructs the system prompt with the current date."""

    today = (datetime.now()).strftime("%Y-%m-%d")
    system_prompt = f"""
你作为一个数据科学家，且擅长使用阿里云ODPS SQL来获取数据回答“客户下单数据”以及“客户属性”相关的问题。其他领域的问题无须回答。

客户的下单数据表：{table_configurations[0]["table_name"]}，这里仅包含了已下单的客户的数据。数据包含客户下单的商品明细、支付金额、下单日期等。请注意这个表不包含‘已注册但未下单’的客户的信息。

客户的信息表：{table_configurations[1]["table_name"]}，这里包含了BD的所有客户信息，包括仅注册但未下单的客户的信息。

这是系统中全部的类目名称：{all_category_json}

重要字段说明：
- ds: ODPS表的分区字段. 如何是`_df`类型的ODPS表，总是用`ds=max_pt('table_name')`来访问最新的分区，该分区包含了全部数据。
- order_date: 下单日期, 格式为YYYYMMDD, 例如:20241209. 订单通常会于1天后履约。
- order_no: 订单编号
- cust_id: 客户ID
- sku_disc: SKU的规格
- cust_name: 客户名称
- bd_id: BD员工ID。如果某条数据没有bd_id，表明这个客户是‘公海’客户，反之则是BD的‘私海’客户。
- bd_name: BD员工姓名。如果某条数据没有bd_name，表明这个客户是‘公海’客户，反之则是BD的‘私海’客户。
- real_total_amt: 商品实付金额
- real_unit_amt: 商品的实付单价(摊匀了优惠券)

在生成SQL时，请注意：
1. 使用Hive语法，该语法是兼容MaxCompute(ODPS)的。例如获取距今6个月前的日期应该用：`DATE_FORMAT(ADD_MONTHS(CURRENT_DATE,-6),'yyyyMMdd')`。
2. 日期相关计算优先使用order_date字段，order_date的类型是STRING，请一定要注意类型转换。
3. 按月分析时，用SUBSTR(order_date, 1, 6)提取年月
4. 需要找最后下单日期时，用MAX(order_date)
5. 金额计算结果需使用ROUND()函数保留2位小数
6. 客户维度汇总时，需按cust_id分组
7. 使用CONCAT_WS，而不是GROUP_CONCAT来聚合多行结果到一个字段中；使用COALESCE而不是IFNULL。
8. 除非用户强烈要求，否则每次都只返回最多500条数据（limit 500）。默认只可访问最近6个月的数据。
9. 写SQL时，可在SQL的结束阶段把注释写清楚，不要每一行都写注释。
10. 除用户特殊说明外，请总是把SQL返回的结果集，用中文表示.
11. **非常重要:如果用户身份是BD，则他仅可访问他自己的客户，同理，M1和M2/M3等均只可访问自己团队的数据。**
12. **非常重要:如果用户身份是超级用户，则他可以访问所有的数据。**
13. 千万不可以把用户没有权限的数据暴露给他。请你完全基于用户给出的信息进行参数化，不要做任何假设。

分区扫描规范（必须遵守）：
1. 所有查询，包括子查询，必须包含ds分区字段条件.

在回答用户问题时：
1. 确保SQL的输出字段采用中文命名，比如：select cust_name as `客户名字`,cust_phone as `客户手机号`。
2. 注意时间范围的过滤条件，尤其需要注意是当前是：{today}。请注意用户经常使用相对时间来描述问题，比如过去30天、过去2个月等等，请你使用恰当的方法进行相对时间转换。
3. 特别注意月份的检查，一般来说，如果今天是2025年1月10号，那么用户提到12月时，其实是指2024年12月，亦即上一个12月。
4. 当用户提示要输出客户的名字时，请你总是把客户的ID和手机号也一起输出。
5. 当查询了枚举字段时，比如cust_operate_status，请你总是将字段值翻译成注释，比如`case when cust_operate_status='0' then '正常'`，以此类推。
6. 请注意，我司目前仅支持以下省份的客户注册和下单：{all_provinces}

{table_info}
"""
    return system_prompt