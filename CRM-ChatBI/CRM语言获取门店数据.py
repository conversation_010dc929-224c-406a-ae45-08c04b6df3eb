import json
import os

os.environ["OPENAI_API_VERSION"] = "2024-10-01-preview"
os.environ["AZURE_OPENAI_ENDPOINT"] = "https://esat-us.openai.azure.com"
os.environ["AZURE_OPENAI_API_KEY"] = os.getenv("AZURE_API_KEY_XM")

import logging

# Configure logging with a specific format and file output
logging.basicConfig(
    level=logging.INFO,
    filename="./app.log",  # 输出到指定日志文件
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",  # 格式化日志内容
    datefmt="%Y-%m-%d %H:%M:%S",  # 日期时间格式
)
logger = logging.getLogger(__name__)

from langchain_openai import AzureChatOpenAI, ChatOpenAI

llm = AzureChatOpenAI(
    # azure_deployment="gpt-4o-mini",
    azure_deployment="gpt-4o",
    api_version="2024-05-01-preview",
    temperature=0,
    max_tokens=None,
    timeout=None,
    max_retries=2,
)

# llm_tool = ChatOpenAI(
#     api_key=os.getenv("OPENAI_API_KEY1"),
#     base_url="https://api.gptsapi.net/v1",
#     model="chatgpt-4o-latest",
#     temperature=1,
#     timeout=60,
# )

llm_tool = AzureChatOpenAI(
    # azure_deployment="gpt-4o-mini",
    azure_deployment="gpt-4o",
    api_version="2024-05-01-preview",
    temperature=0,
    max_tokens=None,
    timeout=None,
    max_retries=2,
)

llm = ChatOpenAI(
    api_key=os.getenv("OPENAI_API_KEY1"),
    base_url="https://api.gptsapi.net/v1",
    model="o1-mini",
    temperature=1,
    timeout=60,
)

llm_gemini = ChatOpenAI(
    api_key=os.getenv("LITELLM_MASTER_KEY"),
    base_url="http://127.0.0.1:4000/v1",
    model="gemini-2.0-flash-exp",
    temperature=1,
    timeout=60,
)

llm = llm_tool  # 优先使用AzureChatOpenAI
# llm = llm_gemini # 试验一下Gemini


import sys
import pandas as pd
from datetime import datetime

# Add the scripts directory to the sys.path
sys.path.append("../")

from odps_client import get_odps_sql_result_as_df, odps
from langchain_core.tools import tool


@tool
def get_odps_sql_result_as_json(sql: str) -> tuple[list[dict], bool]:
    """
    获取ODPS SQL查询结果并返回JSONArray
    返回的数据中至少有一列包含中文字符，便于用户阅读。

    Args:
        sql (str): 阿里云ODPS(maxCompute) SQL查询语句。

    Returns:
        tuple[list[dict], bool]: 返回一个元组，包含:
            - 查询结果的JSON数组
            - 布尔值，表示结果中的至少一列包含中文字符
    """
    df = get_odps_sql_result_as_df(sql)
    if not df.empty:
        has_chinese = False
        # 检查列名中是否至少有一列包含中文字符
        logging.info(f"df.columns: {df.columns}")
        for col in df.columns:
            if any("\u4e00" <= c <= "\u9fff" for c in col):
                has_chinese = True
                break
        return df.to_dict(orient="records"), has_chinese
    else:
        return [], False


# table_name = "summerfarm_ds.tmp_telemarketing_team_dlv_order_detail_di"
table_name = "summerfarm_tech.app_cust_orders_2yrs_df"
table = odps.get_table(name=table_name)
table_schema = f"{table.get_ddl()}"

# cust_table_name = "summerfarm_tech.dim_cust_df"
cust_table_name = "summerfarm_tech.app_crm_chatbi_info_df"
cust_table = odps.get_table(name=cust_table_name)
cust_table_schema = f"{cust_table.get_ddl()}"
cust_sample_json = cust_table.head(1).to_pandas().iloc[0].to_json(force_ascii=False)

from datetime import datetime, timedelta

yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y%m%d")
top_2_df = table.head(2).to_pandas()
head_1_rows_json = top_2_df.iloc[0].to_json(force_ascii=False)

all_category_df = get_odps_sql_result_as_df(
    """select array_join(collect_set(category1),',') 一级类目列表,
        array_join(collect_set(category2),',') 二级类目列表,
        array_join(collect_set(category3),',') 三级类目列表
        -- ,array_join(collect_set(category4),',') 四级类目列表
    from summerfarm_tech.dim_sku_df
    where ds=max_pt('summerfarm_tech.dim_sku_df')
    and outdated = 0
    and spu_name not like '%测试%'
    and concat(category1,category2,category3,category4) not like '%测试%'
    """
)

all_category_json = all_category_df.iloc[0].to_json(force_ascii=False)

all_province_df= get_odps_sql_result_as_df(
    """
SELECT  province
        ,COUNT(*) cnt
        ,MAX(last_order_time) last_order_time_of_group
        ,MAX(m_id) max_m_id
FROM    summerfarm_tech.ods_merchant_df
WHERE   ds = MAX_PT('summerfarm_tech.ods_merchant_df')
AND     last_order_time IS NOT NULL
AND     last_order_time >= '2024-01-01 00:00:00'
GROUP BY province
HAVING  cnt >= 5
ORDER BY cnt,last_order_time_of_group
"""
)

all_provice=','.join(all_province_df['province'].unique())

print(f"head_1_rows_json:{head_1_rows_json}")


system_prompt = f"""
你作为一个数据科学家，且擅长使用阿里云ODPS SQL来获取数据回答“客户下单数据”以及“客户属性”相关的问题。其他领域的问题无须回答。

客户的下单数据表：{table_name}，这里仅包含了已下单的客户的数据。数据包含客户下单的商品明细、支付金额、下单日期等。请注意这个表不包含‘已注册但未下单’的客户的信息。

客户的信息表：{cust_table_name}，这里包含了BD的所有客户信息，包括仅注册但未下单的客户的信息。

这是系统中全部的类目名称：{all_category_json}

重要字段说明：
- ds: ODPS表的分区字段. 如何是`_df`类型的ODPS表，总是用`ds=max_pt('table_name')`来访问最新的分区，该分区包含了全部数据。
- order_date: 下单日期, 格式为YYYYMMDD, 例如:20241209. 订单通常会于1天后履约。
- order_no: 订单编号
- cust_id: 客户ID
- sku_disc: SKU的规格
- cust_name: 客户名称
- bd_id: BD员工ID。如果某条数据没有bd_id，表明这个客户是‘公海’客户，反之则是BD的‘私海’客户。
- bd_name: BD员工姓名。如果某条数据没有bd_name，表明这个客户是‘公海’客户，反之则是BD的‘私海’客户。
- real_total_amt: 商品实付金额
- real_unit_amt: 商品的实付单价(摊匀了优惠券)

在生成SQL时，请注意：
1. 使用MaxCompute(ODPS) SQL语法
2. 日期相关计算优先使用order_date字段
3. 按月分析时，用SUBSTR(order_date, 1, 6)提取年月
4. 需要找最后下单日期时，用MAX(order_date)
5. 金额计算结果需使用ROUND()函数保留2位小数
6. 客户维度汇总时，需按cust_id分组
7. 使用CONCAT_WS，而不是GROUP_CONCAT来聚合多行结果到一个字段中
8. 除非用户强烈要求，否则每次都只返回最多500条数据（limit 500）.
9. 除非用户强烈要求，否则只可访问最近6个月的数据.
10. 除用户特殊说明外，请总是把SQL返回的结果集，用中文表示.
11. **非常重要:如果用户身份是BD，则他仅可访问他自己的客户，同理，M1和M2/M3等均只可访问自己团队的数据。**
12. **非常重要:如果用户身份是超级用户，则他可以访问所有的数据。**
13. 千万不可以把用户没有权限的数据暴露给他。请你完全基于用户给出的信息进行参数化，不要做任何假设。

分区扫描规范（必须遵守）：
1. 所有查询，包括子查询，必须包含ds分区字段条件.

在回答用户问题时：
1. 确保SQL的输出字段采用中文命名，比如cust_name可以用“客户名字”。
2. 确保SQL的WHERE条件完整且准确
3. 注意时间范围的过滤条件，尤其需要注意是当前是：{datetime.now().strftime('%Y年%m月%d日')}。请注意用户经常使用相对时间来描述问题，比如过去30天、过去2个月等等，请你使用恰当的方法进行相对时间转换。
4. 特别注意月份的检查，一般来说，如果今天是2025年1月10号，那么用户提到12月时，其实是指2024年12月，亦即上一个12月。
5. 当用户提示要输出客户的名字时，请你总是把客户的ID和手机号也一起输出。
6. 请注意，我司目前仅支持以下省份的客户注册和下单：{all_provice}

示例数据和表结构语句(请务必十分注意这些字段的格式，尤其是时间/日期格式)：
```
{table_name}的表结构语句:
{table_schema}
数据样本:
{head_1_rows_json}
```

```
{cust_table_name}的表结构语句:
{cust_table_schema}
数据样本：
{cust_sample_json}
```
"""

logger.info(f"system_prompt:\n{system_prompt}")

tools = [get_odps_sql_result_as_json]

# 此处可切换模型:
llm_with_tools = llm_tool.bind_tools(tools)


from langchain_core.messages import SystemMessage, HumanMessage, AIMessage


def recalulate_prompt() -> str:
    return f"""查询结果为空,请注意:
1. 再次审查用户的请求，检查日期范围是否正确，且请特别再次注意，今天是：{datetime.now().strftime("%Y年%m月%d日")}.
2. 商品名称/人名/类目名建议使用LIKE '%关键词%'进行模糊匹配。但请注意不可修改输入的关键词。
3. 当使用到了order_date和ds, deliver_date等日期字段时，请务必参考样例数据中的日期格式，否则可能会导致查询结果为空。
样例数据给你重新参考:{head_1_rows_json}
请调整SQL重试。"""


ensure_all_chinese_columns_prompt = """OK很好，刚才的SQL已经可以执行，且结果准确。
但客户希望获取到的结果中，所有列名都是中文，请确保输出的所有列名都是中文。"""


def extract_core_problem_for_query(
    query: str = "在10月购买了爱乐薇(铁塔)淡奶油但是在11月没有购买的用户有哪些？按照他们的10月采购总额倒序排序，列出它们ID、名字、BD名字、省市区、最后下单时间。",
    user: str = None,
) -> str:
    today_chinese = datetime.now().strftime("%Y年%m月%d日")
    add_user_filter = (
        "" if user is None else f"提问的用户的名字是:{user},这非常重要!!\n"
    )
    user_to_ask = user if user is not None else "用户"
    messages = [
        HumanMessage(
            content=f"{add_user_filter}{query}\n\n请提取{user_to_ask}的核心问题，仅限最全面且详细的那一个！{table_schema}"
        )
    ]
    core_question = llm.invoke(messages)
    logger.info(core_question)
    messages.append(AIMessage(content=core_question.content))
    messages.append(
        HumanMessage(
            content="请提取与解决问题相关的信息，仅提取最有用的信息，并详细说明为什么对解决问题有用，逐条列出"
        )
    )
    logger.info(f"messages:{messages}")
    resolving_steps = llm.invoke(messages)
    logger.info(resolving_steps)
    return f"""{user_to_ask}提问:今天是{today_chinese},{query}

核心问题:
{core_question.content}

解决问题的关键信息:
{resolving_steps.content}

请深刻理解{user_to_ask}的提问，然后逐步解决问题并输出最终答案。
"""


def get_data_with_ai(
    query: str = "昨天一共有多少个门店下单？",
    user: str = None,
) -> tuple[list[dict], str]:
    logger.info(f"用户问题：{query}")
    core_problem = extract_core_problem_for_query(query=query, user=user)
    logger.info(f"提取出来的核心问题和信息:{core_problem}")
    today_chinese = datetime.now().strftime("%Y年%m月%d日")
    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content="今天是哪一天？"),
        AIMessage(content=f"今天是{today_chinese}。"),
        HumanMessage(content=core_problem),
    ]

    max_retries = 5
    empty_data_check_cnt = 3
    retry_count = 0

    sql_query = ""
    call_using = {}
    # // TODO 看看是否需要double check
    double_checked = False
    while retry_count < max_retries:
        retry_count += 1
        try:
            logging.info(f"{messages}")
            tool_call_results = llm_with_tools.invoke(messages)
            logger.info(f"AI的返回:{tool_call_results}")
            tool_calls = tool_call_results.tool_calls
            if f"{user}的问题已解决" in tool_call_results.content:
                logging.info(f"AI再次思考后觉得前序SQL已经足够:{sql_query}")
            elif not isinstance(tool_calls, list) or len(tool_calls) <= 0:
                logger.info(f"AI返回的tool_calls不是list,重试retry_count:{retry_count}")
                continue
            else:
                call_using = tool_calls[0]
                logger.info(f"new call_using:{call_using}")
            if call_using["name"] == "get_odps_sql_result_as_json":
                args = call_using["args"]  # Extract arguments
                sql_query = args["sql"]

                result, has_chinese = get_odps_sql_result_as_json(sql_query)

                if not result or len(result) <= 0:
                    messages.append(AIMessage(content=f"{call_using}"))
                    messages.append(HumanMessage(content=recalulate_prompt()))
                    msg_str = "\n".join([m.content for m in messages])
                    logger.info(f"SQL结果为空,提醒AI重新思考:{msg_str}")
                    empty_data_check_cnt -= 1
                    if empty_data_check_cnt <= 0:
                        logger.error(f"结果为空,不再提醒AI重新思考:{query},{sql_query}")
                        break
                    else:
                        continue
                elif not has_chinese:
                    messages.append(AIMessage(content=f"{call_using}"))
                    messages.append(
                        HumanMessage(content=ensure_all_chinese_columns_prompt)
                    )
                    msg_str = "\n".join([m.content for m in messages])
                    logger.info(
                        f"AI返回的SQL中并不是全部都是中文,提醒AI重新思考:{msg_str}"
                    )
                    continue
                elif not double_checked:
                    double_checked = True
                    logger.info(f"尝试让AI double check SQL:{sql_query}")
                    messages.append(AIMessage(content=f"{call_using}"))
                    messages.append(
                        HumanMessage(
                            content=f"以上SQL解决了问题吗?如果解决了，请回答“{user}的问题已解决”。如果没有，则请再仔细阅读以下任务内容，修改SQL，重新尝试：\n{core_problem}"
                        )
                    )
                    msg_str = "\n".join([m.content for m in messages])
                    logger.info(f"AI思考中:{msg_str}")
                    continue

                logger.info(f"是否是中文字段名:{has_chinese}")
                return result, sql_query
            return [], sql_query

        except Exception as e:
            error_message = str(e)
            logger.warning(f"SQL:{sql_query} 执行错误: {error_message}")
            messages.append(AIMessage(content=f"{call_using}"))
            messages.append(
                HumanMessage(
                    content=f"Previous query failed with error: {error_message}. Please correct it."
                )
            )
            msg_str = "\n".join([m.content for m in messages])
            logger.info(f"retry_count:{retry_count}...{msg_str}")
            if retry_count >= max_retries:
                logger.error(
                    f"达到最大重试次数{max_retries}次,返回空结果.sql_query:\n{sql_query}"
                )
                return (
                    [],
                    f"达到最大重试次数{max_retries}次,返回空结果。error_message:{error_message}.\nSQL:\n{sql_query}",
                )
            continue
    return [], sql_query
