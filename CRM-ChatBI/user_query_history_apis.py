from flask import (
    render_template,
    request,
    jsonify,
)
from user_query_history import <PERSON>rQueryHistory
from logger import logger

user_query_history = UserQueryHistory()

def my_query_history(user_info:dict={}):
    user_email = user_info.get("data", {}).get("email")
    page = int(request.args.get("page", 1))
    user_query_history_list = user_query_history.get_history(
        user_email=user_email, page=page
    )
    return jsonify(user_query_history_list)

def query_history(user_info:dict={}, hostname:str=""):
    user_email = user_info.get("data", {}).get("email")
    if user_email != "<EMAIL>":
        return "您没有权限访问这个页面"
    page = int(request.args.get("page", 1))
    name = request.args.get("name", "")
    email = request.args.get("email", "")
    logger.info(f"query_history: {name}, {email}, {page}")
    emails = []
    if name:
        user_query_history_list = user_query_history.get_history(name=name, page=page)
        max_page = (user_query_history.get_max_page(name=name) + 19) // 20
    elif email:
        user_query_history_list = user_query_history.get_history(
            user_email=email, page=page
        )
        max_page = (user_query_history.get_max_page(user_email=email) + 19) // 20
    else:
        user_query_history_list, emails = user_query_history.get_history(
            page=page, fetch_emails=True
        )
        max_page = (user_query_history.get_max_page() + 19) // 20
    if not user_query_history_list:
        user_query_history_list = []
    return render_template(
        "query_history.html",
        user_query_history_list=user_query_history_list,
        max_page=max_page,
        emails=emails,
        page=page,
        hostname=hostname,
    )
