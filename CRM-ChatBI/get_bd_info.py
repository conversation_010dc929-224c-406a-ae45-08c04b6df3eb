import sys
from logger import logger

sys.path.append("..")

import pandas as pd
from odps_client import get_odps_sql_result_as_df, odps

# Define the SQL query to get BD information
sql_query = """
SELECT
  bd_id,
  bd_name,
  create_time,
  administrative_city,
  m1_id,
  m1_name,
  m2_id,
  m2_name,
  m3_id,
  m3_name,
  zone_name,
  team_tag,
  is_disabled,
  ds
FROM summerfarm_tech.dim_bd_df
WHERE ds = max_pt('summerfarm_tech.dim_bd_df') and is_disabled = 0;
"""

# Get the BD information from ODPS
df = get_odps_sql_result_as_df(sql_query)

# Parse the m3/m2/m1 information
m3_dict = {}
m2_dict = {}
m1_dict_raw = {}
m1_dict = {}
bd_dict = {}

for index, row in df.iterrows():
    m3_id = row["m3_id"]
    m3_name = row["m3_name"]
    m2_id = row["m2_id"]
    m2_name = row["m2_name"]
    m1_id = row["m1_id"]
    m1_name = row["m1_name"]

    m3_dict[m3_name] = m3_id
    m2_dict[m2_name] = m2_id
    m1_dict_raw[m1_name] = m1_id
    bd_dict[row["bd_name"]] = row["bd_id"]

for m1_name, m1_id in m1_dict_raw.items():
    if m1_name in m2_dict or m1_name in m3_dict:
        logger.info(f"Warning: {m1_name} is in both m1_dict and m2_dict or m3_dict")
    else:
        m1_dict[m1_name] = m1_id

# Print the parsed information
logger.info(f"M3 Information:{m3_dict}")
logger.info(f"M2 Information:{m2_dict}")
logger.info(f"M1 Information:{m1_dict}")
logger.info(f"BD Information:{bd_dict}")

import os

super_user_list = os.getenv(
    "CHAT_BI_SUPER_USERS", "唐鹏,李钦,康凯,江涛,杨涵,李茂源,黄晓明,周勇镔,鲁嘉浩"
).split(",")
super_user_list = set(super_user_list)


def get_user_title(user: str) -> str:
    if user in super_user_list:
        return f"{user}(超级用户)"
    elif user in m3_dict:
        return f"{user}(M3销售总监)"
    elif user in m2_dict:
        return f"{user}(M2销售大区经理)"
    elif user in m1_dict:
        return f"{user}(M1销售经理)"
    elif user in bd_dict:
        return f"{user}(普通BD,bd_id:{bd_dict[user]})"
    else:
        return f"{user}(未知用户)"
