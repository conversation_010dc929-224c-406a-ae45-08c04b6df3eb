import sys
import os
from datetime import datetime
from typing import Optional, List, Tuple, Dict
from logger import logger


sys.path.append("../")

from get_arena_db_connection_and_execute import (
    get_arena_db_connection_and_execute,
    ensure_db_name_is_provided,
)

PG_HOST = os.getenv("CHAT_BI_PG_HOST", "127.0.0.1")

logger.info(f"PG_HOST:{PG_HOST}")


class UserQueryHistory:
    def __init__(self, database: str = "chatbi"):
        self.database = database
        self.create_table()

    def create_table(self):
        """Creates the user_query_history table if it doesn't exist."""
        try:
            logger.info(f"PG_HOST:{PG_HOST}")
            ensure_db_name_is_provided(db_name=self.database, host=PG_HOST)
            get_arena_db_connection_and_execute(
                """
                CREATE TABLE IF NOT EXISTS user_query_history (
                    id SERIAL PRIMARY KEY,
                    user_name TEXT,
                    user_email TEXT,
                    query TEXT,
                    sql TEXT,
                    insert_time TIMESTAMP WITHOUT TIME ZONE,
                    sample_data TEXT
                )
            """,
                db_name=self.database,
                pg_host=PG_HOST,
            )
            logger.info("Successfully created user_query_history table.")
        except Exception as e:
            logger.error(f"Error creating table: {e}")

    def _execute_query(self, sql: str, params: tuple = ()):
        """Helper function to execute SQL queries."""
        return get_arena_db_connection_and_execute(
            sql, params, db_name=self.database, pg_host=PG_HOST
        )

    def insert_history(
        self, user_name: str, user_email: str, query: str, sql: str, sample_str
    ):
        """Inserts or updates a user query history record."""

        current_time = datetime.now()
        try:
            existing_id = self._check_existing_query(user_email, query)
            if existing_id:
                self._update_existing_record(current_time, sql, existing_id)
            else:
                self._insert_new_record(
                    user_name, user_email, query, sql, current_time, sample_str
                )
        except Exception as e:
            logger.error(f"Error inserting/updating history: {e}")

    def _check_existing_query(self, user_email: str, query: str) -> Optional[int]:
        """Checks if a query already exists for the user, returns the ID if found or None"""
        check_sql = """
            SELECT id FROM user_query_history
            WHERE user_email = %s AND query = %s
        """
        try:
            existing = self._execute_query(check_sql, (user_email, query))
            return existing[0]["id"] if existing else None
        except Exception as e:
            logger.error(f"Error checking existing query: {e}")
            return None

    def _update_existing_record(
        self, current_time: datetime, sql: str, existing_id: int
    ):
        """Updates an existing record with new timestamp and SQL"""
        update_sql = """
            UPDATE user_query_history
            SET insert_time = %s, sql = %s
            WHERE id = %s
        """
        self._execute_query(update_sql, (current_time, sql, existing_id))
        logger.info(f"Updated history record with ID {existing_id}")

    def _insert_new_record(
        self,
        user_name: str,
        user_email: str,
        query: str,
        sql: str,
        current_time: datetime,
        sample_str,
    ):
        insert_sql = """
            INSERT INTO user_query_history (user_name, user_email, query, sql, insert_time, sample_data)
            VALUES (%s, %s, %s, %s, %s, %s)
        """
        self._execute_query(
            insert_sql,
            (user_name, user_email, query, sql, current_time, sample_str),
        )
        logger.info(f"Inserted new history record for user {user_email}")

    def get_all_emails(self) -> List[str]:
        """Retrieves all distinct user emails."""
        sql = "SELECT DISTINCT user_email FROM user_query_history"
        try:
            emails = self._execute_query(sql)
            return [email["user_email"] for email in emails]
        except Exception as e:
            logger.error(f"Error getting all emails: {e}")
            return []

    def get_history(
        self,
        user_email: Optional[str] = None,
        page: int = 1,
        page_size: int = 20,
        name: Optional[str] = None,
        fetch_emails: bool = False,
    ) -> Tuple[List[Dict], Optional[List[str]]]:
        """Retrieves user query history based on email or name, with pagination."""

        base_sql = """
            SELECT id, user_name, user_email, query, sql, insert_time, sample_data
            FROM user_query_history
            {where_clause}
            ORDER BY insert_time DESC
            LIMIT %s OFFSET %s
        """
        params: List = [page_size, (page - 1) * page_size]
        if user_email:
            where_clause = "WHERE user_email = %s"
            params.insert(0, user_email)

        elif name:
            where_clause = "WHERE user_name = %s"
            params.insert(0, name)
        else:
            where_clause = ""

        sql = base_sql.format(where_clause=where_clause)

        try:
            history = self._execute_query(sql, tuple(params))
            if fetch_emails:
                emails = self.get_all_emails()
                return history, emails
            return history
        except Exception as e:
            logger.error(f"Error getting history: {e}")
            return []

    def get_max_page(
        self,
        user_email: Optional[str] = None,
        page_size: int = 20,
        name: Optional[str] = None,
    ) -> int:
        """Calculates the maximum number of pages for pagination."""
        base_sql = "SELECT COUNT(*) FROM user_query_history {where_clause}"

        if user_email:
            where_clause = "WHERE user_email = %s"
            params = (user_email,)
        elif name:
            where_clause = "WHERE user_name = %s"
            params = (name,)
        else:
            where_clause = ""
            params = ()

        sql = base_sql.format(where_clause=where_clause)

        try:
            count = self._execute_query(sql, params)[0]["count"]
            max_page = (count + page_size - 1) // page_size  # Calculate max page
            return max_page
        except Exception as e:
            logger.error(f"Error getting max page: {e}")
            return 0
