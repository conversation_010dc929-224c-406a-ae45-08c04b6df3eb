from datetime import datetime
import sys
from flask import (
    Flask,
    render_template,
    request,
    jsonify,
    redirect,
    session,
    make_response,
    Response,
)
import json

from user_query_history import UserQueryHistory
from user_login_with_feishu import login, callback, login_required, HOST_NAME
from import_csv_to_feishu_bitable import create_bitable_and_upload_csv

from logger import logger

# Add the parent directory to sys.path
sys.path.append("..")

app = Flask("CRM-ChatBI", static_folder="static")
app.config["SEND_FILE_MAX_AGE_DEFAULT"] = 86400
app.secret_key = "your_secret_key"  # 用于会话加密
MEGABYTE = (2 ** 10) ** 2
app.config['MAX_CONTENT_LENGTH'] = None
app.config['MAX_FORM_MEMORY_SIZE'] = 20 * MEGABYTE


# 获取授权码
@app.route("/login")
def login_route():
    return login()


# 回调处理
@app.route("/callback")
def callback_route():
    return callback()


user_query_history = UserQueryHistory()
empty_data = {"columns": [], "data": []}


@app.route("/", methods=["GET"])
@login_required
def index():
    user_info = session.get("user_info")
    user_email = user_info.get("data", {}).get("email")
    page = int(request.args.get("page", 1))
    user_query_history_list = user_query_history.get_history(
        user_email=user_email, page=page
    )
    with open("system_query_template.json", "r") as f:
        system_query_templates = json.load(f)
    response = make_response(
        render_template(
            "index.html",
            table_data=empty_data,
            query="",
            user_info=user_info,
            hostname=HOST_NAME,
            user_query_history_list=user_query_history_list,
            system_query_templates=system_query_templates,
            cache_control_timestamp=datetime.now().timestamp(),
        )
    )
    response.headers["Cache-Control"] = "no-store, no-cache, must-revalidate, max-age=0"
    response.headers["Pragma"] = "no-cache"
    response.headers["Expires"] = "0"
    return response


from get_bd_info import get_user_title
from get_data_with_ai_stream import get_data_with_ai_stream
from pandas import DataFrame


@app.route("/query/stream", methods=["POST"])
@login_required
def stream_query():
    user_info = session.get("user_info")
    user_name = user_info.get("data", {}).get("name")
    user_email = user_info.get("data", {}).get("email")
    query = request.form.get("query", "")

    def stream_query_callback(response_data):
        sql_query = response_data.get("sql_query")
        data = response_data.get("data")
        sample_data = None
        if data is not None:
            try:
                if isinstance(data, dict) and "data" in data:
                    sample_data_list = data["data"]
                    if sample_data_list:
                        sample_data = str(sample_data_list[0])
                elif isinstance(data, DataFrame) and not data.empty:
                    sample_data = str(data.iloc[0].to_dict())
            except Exception as e:
                logger.error(f"parse data error:{e}")

        user_query_history.insert_history(
            user_name,
            user_email,
            query,
            sql_query,
            sample_data,
        )

    # streaming response
    return Response(
        get_data_with_ai_stream(
            query, user=get_user_title(user_name), callback=stream_query_callback
        ),
        mimetype="text/event-stream",
    )


from llm_client import llm_deepseek_v3
from langchain_core.messages import HumanMessage, AIMessage


def get_feishu_document_name(query: str, reasoning_content: str = "") -> str:
    time_str = datetime.now().strftime("%m-%d_%H%M")
    messages = [
        HumanMessage(
            content=f"请为以下内容提取20个字以内的标题。仅需要返回标题，其他不需要返回:\n\n{query}\n{reasoning_content}"
        )
    ]
    respons = llm_deepseek_v3.invoke(messages)
    return respons.content + time_str


@app.route("/import_to_feishu", methods=["POST"])
@login_required
def import_to_feishu():
    access_token = request.cookies.get("access_token")
    if not access_token:
        return jsonify({"status": "failed", "message": "Access token not found."})
    query = request.form.get("query", "")
    csv_content = request.form.get("csv_content", "")
    reasoning_content = request.form.get("reasoning_content", "")

    if not csv_content:
        return jsonify({"status": "failed", "message": "No CSV content to import."})

    # Create Bitable and upload CSV
    bitable_name = get_feishu_document_name(
        query=query, reasoning_content=reasoning_content
    )
    result = create_bitable_and_upload_csv(csv_content, access_token, bitable_name)

    if result and result.get("status") == "success":
        return jsonify(
            {
                "status": "success",
                "message": "导入飞书多维表格成功了!",
                "url": result.get("url"),
                "name": bitable_name,
            }
        )
    else:
        logger.error(f"Failed to import data to Feishu Bitable: {result}")
        return jsonify(
            {
                "status": "failed",
                "message": "导入失败了",
                "error": result.get("error"),
                "detail": result.get("detail"),
            }
        )


import user_query_history_apis


@app.route("/query_history/mine", methods=["GET"])
@login_required
def my_query_history():
    return user_query_history_apis.my_query_history(user_info=session.get("user_info"))


@app.route("/query_history", methods=["GET"])
@login_required
def query_history():
    return user_query_history_apis.query_history(
        user_info=session.get("user_info"), hostname=HOST_NAME
    )


import argparse

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--debug", action="store_true", default=False, help="Enable debug mode"
    )
    args, unknown = parser.parse_known_args()
    app.run(debug=args.debug, port=5700, host="0.0.0.0")
