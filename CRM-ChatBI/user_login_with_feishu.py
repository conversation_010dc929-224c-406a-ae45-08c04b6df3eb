import sys
import os
import requests
import datetime
import urllib.parse
from flask import redirect, make_response, session, request

from logger import logger

# 飞书应用的 App ID 和 App Secret
APP_ID = "cli_a7d55bc7cb7cd00b"
APP_SECRET = "S95m7GmcGhteLaaOImj7Uf0GkESpyig0"

# 本地调试：http://127.0.0.1:5700
HOST_NAME = os.getenv(
    "CHAT_BI_HOST_NAME", "http://xianmuai.s7.tunnelfrp.com/crm-chatbi"
)
REDIRECT_URI = f"{HOST_NAME}/callback"  # 配置的重定向URL


scope = [
    "offline_access",
    "contact:user.email:readonly",
    "contact:department.base:readonly",
    "contact:user.base:readonly",
    "contact:user.employee:readonly",
    "docs:document:import",
    "drive:drive",
    "drive:file",
    "drive:file:upload",
    "base:app:create",
    "bitable:app",
    "aily:file:write",
]

scope_encoded = urllib.parse.quote_plus(" ".join(scope))


def get_expiry_at():
    return (datetime.datetime.now() + datetime.timedelta(hours=2)).isoformat()


def login():
    access_token = request.cookies.get("access_token")
    expires_at = request.cookies.get("expires_at")
    refresh_token = request.cookies.get("refresh_token")

    if access_token and expires_at:
        expires_at_datetime = datetime.datetime.fromisoformat(expires_at)
        if expires_at_datetime > datetime.datetime.now():
            # access token 仍然有效，无需重新授权
            return redirect(f"{HOST_NAME}/")

    if refresh_token:
        # 使用 refresh token 刷新 access token
        new_access_token, new_refresh_token = refresh_token_and_get_new_access_token(
            refresh_token=refresh_token
        )
        if new_access_token:
            # 更新 access token
            resp = make_response(redirect(f"{HOST_NAME}/"))
            resp.set_cookie("access_token", new_access_token)
            resp.set_cookie("refresh_token", new_refresh_token)
            resp.set_cookie(
                "expires_at",
                get_expiry_at(),
            )
            return resp

    # 如果 access token 和 refresh token 都不有效，则重新授权
    auth_url = f"https://accounts.feishu.cn/open-apis/authen/v1/authorize?"
    auth_url = f"{auth_url}app_id={APP_ID}&redirect_uri={REDIRECT_URI}&response_type=code&scope={scope_encoded}"
    return redirect(auth_url)


def callback():
    code = request.args.get("code")
    logger.info(f"feishu-auth-code:{code}")
    token_url = "https://open.feishu.cn/open-apis/authen/v2/oauth/token"
    payload = {
        "grant_type": "authorization_code",
        "client_id": APP_ID,
        "client_secret": APP_SECRET,
        "code": code,
        "redirect_uri": f"{HOST_NAME}/callback",
        # "code_verifier": "TxYmzM4PHLBlqm5NtnCmwxMH8mFlRWl_ipie3O0aVzo",
    }

    response = requests.post(token_url, json=payload)
    access_token = response.json()
    logger.info(f"user access_token:{access_token}")

    # 将用户访问令牌存储在HTTP cookie中
    resp = make_response(redirect(f"{HOST_NAME}/"))
    resp.set_cookie("access_token", access_token["access_token"])
    if "refresh_token" in access_token:
        resp.set_cookie("refresh_token", access_token.get("refresh_token"))
    resp.set_cookie(
        "expires_at",
        get_expiry_at(),
    )

    # 使用 access_token 获取用户信息
    user_info = get_user_info(access_token["access_token"])
    logger.info(f"user_info:{user_info}")

    # 将用户信息存储在会话中
    session["user_info"] = user_info

    return resp


def get_user_info(access_token):
    user_info_url = "https://open.feishu.cn/open-apis/authen/v1/user_info"
    headers = {"Authorization": f"Bearer {access_token}"}

    user_info_response = requests.get(user_info_url, headers=headers)

    if user_info_response.status_code == 200:
        user_info = user_info_response.json()
        return user_info
    else:
        return None


from functools import wraps
from flask import jsonify


def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        access_token = request.cookies.get("access_token")
        is_json_request = "application/json" in request.headers.get("Accept")
        if not access_token:
            if is_json_request:
                return make_response(jsonify({"message": "Unauthorized"}), 403)
            return redirect(f"{HOST_NAME}/login")

        # Always check if access_token is valid
        expires_at = request.cookies.get("expires_at")
        if not expires_at or datetime.datetime.now() > datetime.datetime.fromisoformat(
            expires_at
        ):
            refresh_token = request.cookies.get("refresh_token")
            if refresh_token:
                new_access_token, new_refresh_token = (
                    refresh_token_and_get_new_access_token(refresh_token)
                )
                if new_access_token:
                    resp = make_response(redirect(f"{HOST_NAME}/"))
                    resp.set_cookie("access_token", new_access_token)
                    resp.set_cookie("refresh_token", new_refresh_token)
                    resp.set_cookie(
                        "expires_at",
                        get_expiry_at(),
                    )
                    return resp
            if is_json_request:
                return make_response(jsonify({"message": "Unauthorized"}), 403)
            return redirect(f"{HOST_NAME}/login")

        user_info = session.get("user_info")
        logger.info(f"user-info-in session:{user_info}")
        return f(*args, **kwargs)

    return decorated_function


def refresh_token_and_get_new_access_token(refresh_token: str) -> tuple[str, str]:
    # 使用refresh_token进行refresh并获取新的access_token
    token_url = "https://open.feishu.cn/open-apis/authen/v2/oauth/token"
    payload = {
        "grant_type": "refresh_token",
        "client_id": APP_ID,
        "client_secret": APP_SECRET,
        "refresh_token": refresh_token,
    }
    response = requests.post(token_url, json=payload)
    access_token = response.json()
    logger.info(f"refreshed access_token:{access_token}")
    if "access_token" not in access_token:
        return None, None
    return access_token["access_token"], access_token["refresh_token"]
