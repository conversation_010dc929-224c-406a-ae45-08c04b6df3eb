import requests, os

# https://open.feishu.cn/open-apis/bot/v2/hook/4b97e52c-830d-4204-83ed-b527142f6a6b

FEISHU_NOTIFY_TOKEN = os.environ.get(
    "FEISHU_NOTIFY_TOKEN", "4b97e52c-830d-4204-83ed-b527142f6a6b"
)


def send_feishu_notice_with_title_and_content(feishu_url, title, markdown_content):
    headers = {"Content-Type": "application/json"}
    data = {
        "msg_type": "interactive",
        "card": {
            "header": {
                "template": "blue",
                "title": {
                    "content": f"**{title}**",
                    "tag": "lark_md",
                },
            },
            "elements": [
                {
                    "tag": "markdown",
                    "content": markdown_content,
                }
            ],
        },
    }
    feishu_result = requests.post(
        url=feishu_url, json=data, headers=headers, verify=False, proxies={}
    ).json()
    return feishu_result


def send_markdown_to_feishu(
    title: str, markdown_content: str, feishu_token: str = None
) -> dict:
    if feishu_token is None or len(feishu_token) <= 5:
        feishu_token = FEISHU_NOTIFY_TOKEN
    if len(feishu_token) > 5:
        url = f"https://open.feishu.cn/open-apis/bot/v2/hook/{feishu_token}"
        print(f"即将发送消息到:{url}, title:{title}, markdown_content:{markdown_content}")
        feishu_result = send_feishu_notice_with_title_and_content(
            feishu_url=url,
            title=title,
            markdown_content=markdown_content,
        )
        print(f"feishu_result:{feishu_result}, url:{url}")
        return feishu_result
    else:
        return {"error": "未配置 FEISHU_NOTIFY_TOKEN 环境变量"}
