<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>com.xianmu.predict.day-only</string>
    <key>ProgramArguments</key>
    <array>
        <string>/bin/bash</string>
        <string>-l</string>
        <string>-c</string>
        <string>cd /Users/<USER>/Documents/xianmu-ai && source np/bin/activate && truncate -s 0 logs/predict.day.error.log logs/predict.day.log ; rm -fr ./lightning_logs && python -m 销量预测.predict_week_multi_process_202410 --period day --use_mps true --early_stopping true --total_batch_no 40</string>
    </array>
    <key>StartCalendarInterval</key>
    <array>
        <dict>
            <key>Hour</key>
            <integer>5</integer>
            <key>Minute</key>
            <integer>20</integer>
        </dict>
    </array>
    <key>EnvironmentVariables</key>
    <dict>
        <key>PATH</key>
        <string>/Users/<USER>/Documents/xianmu-ai/np/bin:/opt/homebrew/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin</string>
        <key>LANG</key>
        <string>en_US.UTF-8</string>
        <key>LC_ALL</key>
        <string>en_US.UTF-8</string>
        <key>HOME</key>
        <string>/Users/<USER>/string>
        <key>ALIBABA_CLOUD_ACCESS_KEY_ID</key>
        <string>LTAI5tQzmpz2nQEWdiqvQGsc</string>
        <key>ALIBABA_CLOUD_ACCESS_KEY_SECRET</key>
        <string>******************************</string>
        <key>LOCAL_MLX_MODEL_Q4_PATH</key>
        <string>/Users/<USER>/Documents/mlx-whisper-v3-turbo-q4</string>
        <key>LOCAL_MLX_MODEL_PATH</key>
        <string>/Users/<USER>/Documents/mlx-whisper</string>
        <key>AZURE_GPT4O_API_KEY</key>
        <string>a7ef58e6aae0407f9b354c4d01a04478</string>
        <key>AZURE_GPT4O_MINI_API_KEY</key>
        <string>4f78538699ae4d8eb90d055c8bf440b2</string>
        <key>OPENAI_API_KEY</key>
        <string>sk-1122334455abc</string>
    </dict>
    <key>WorkingDirectory</key>
    <string>/Users/<USER>/Documents/xianmu-ai</string>
    <key>StandardOutPath</key>
    <string>/Users/<USER>/Documents/xianmu-ai/logs/predict.day.log</string>
    <key>StandardErrorPath</key>
    <string>/Users/<USER>/Documents/xianmu-ai/logs/predict.day.error.log</string>
    <key>RunAtLoad</key>
    <true/>
    <key>UserName</key>
    <string>jishubu</string>
</dict>
</plist>