import os, time
import logging
import httpx
import json

# Configure the logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

HTTP_PROXY_HOST = os.getenv("HTTP_PROXY_HOST", "127.0.0.1")
HTTP_PROXY_PORT = os.getenv("HTTP_PROXY_PORT", "7890")
USE_PROXY_WITH_OPENAI = "true" == os.getenv("USE_PROXY_WITH_OPENAI", "false")

# 新的环境变量配置
XM_LLM_API_KEY = os.getenv("XM_LLM_API_KEY", "")
XM_LLM_BASE_URL = os.getenv("XM_LLM_BASE_URL", "https://litellm-test.summerfarm.net/v1")
XM_LLM_MODEL = os.getenv("XM_LLM_MODEL", "deepseek-v3-250324")

logging.info(f"是否使用proxy:{USE_PROXY_WITH_OPENAI}")

proxy_object = {
    "http://": f"http://{HTTP_PROXY_HOST}:{HTTP_PROXY_PORT}",
    "https://": f"http://{HTTP_PROXY_HOST}:{HTTP_PROXY_PORT}",
}

from openai import OpenAI


def parse_json_string(json_str) -> dict[str, str]:
    try:
        # Remove the prefix '```json' and suffix '```' if present
        cleaned_str = json_str.strip()
        if cleaned_str.startswith("```json") and cleaned_str.endswith("```"):
            cleaned_str = cleaned_str[7:-3].strip()
        elif cleaned_str.startswith("```") and cleaned_str.endswith("```"):
            cleaned_str = cleaned_str[3:-3].strip()

        # Parse the cleaned JSON string into a dictionary
        parsed_data = json.loads(cleaned_str)
        return parsed_data
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
        return {"error": str(e), "original_input": json_str}


# OpenAI 客户端，使用环境变量配置
client_xm_llm = OpenAI(
    api_key=XM_LLM_API_KEY,
    base_url=XM_LLM_BASE_URL,
    http_client=httpx.Client(proxies=proxy_object if USE_PROXY_WITH_OPENAI else None),
)


def call_azure_openai_inner(
    messages: list[dict],
    retrying=1,
    json=True,
    max_tokens=4096,
) -> tuple[str, bool]:
    if retrying < 0:
        return "超过了最大重试次数", False
    completion = None

    # 使用 XM LLM 客户端
    model = XM_LLM_MODEL
    client_to_use = client_xm_llm
    logging.info(f"using XM LLM...:{messages}")
    try:
        completion = client_to_use.chat.completions.create(
            model=model,
            temperature=0.1,
            max_tokens=max_tokens,
            messages=messages,
            response_format={"type": "json_object"} if json else {"type": "text"},
        )
        usage = getattr(completion, 'usage', None)
        completion_tokens = getattr(usage, 'completion_tokens', None)
        prompt_tokens = getattr(usage, 'prompt_tokens', None)
        logging.info(
            f"completion_tokens:{completion_tokens},prompt_tokens:{prompt_tokens}"
        )
        response = completion.choices[0].message.content
        if (
            len(completion.choices) <= 0
            or f"{completion.choices[0].finish_reason}" == "content_filter"
        ):
            return f"LLM过滤了本次请求:{completion.choices[0].to_dict()}", False
        if response is None:
            logging.info(f"LLM API返回了异常:{completion.to_dict()}")
            time.sleep(10)
            return call_azure_openai_inner(
                messages=messages,
                retrying=retrying - 1,
            )
        logging.info(f"total usage:{completion.usage}")
        return response, True
    except Exception as e:
        logging.info(
            f"请求LLM接口报错了:{e}\n messages:{messages}, completion:{completion}"
        )
        if retrying <= 0 or "Error code: 400" in f"{e}":
            return f"{e}", False
        logging.info(f"重试中...{retrying}, messages:{messages}")
        return call_azure_openai_inner(
            messages=messages,
            retrying=retrying - 1,
        )


from enum import Enum


def call_azure_openai(
    text_input: str,
    system_prompt: str = None,
    retrying: int = 1,
    json: bool = True,
    max_tokens: int = 4096,
) -> tuple[str, bool]:
    messages = []
    if system_prompt:
        messages.append({"role": "system", "content": system_prompt})
    messages.append({"role": "user", "content": text_input})
    return call_azure_openai_inner(
        messages=messages,
        retrying=retrying,
        json=json,
        max_tokens=max_tokens,
    )
