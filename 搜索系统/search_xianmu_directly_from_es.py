import requests
from requests.auth import HTTPBasicAuth
import json
import sys
from datetime import datetime
import time

sys.path.append("../")
from area_and_category_lib import get_area_no_by_name, get_sku_monthly_sales, get_category_text, get_query_category_prediction

# 导入时间装饰器
from time_decorator import log_function_time_spend


class SynonymManager:
    """同义词管理器，负责获取和缓存同义词数据"""

    def __init__(self):
        self._synonym_dict = {}  # 词 -> 同义词列表的映射
        self._cache_time = 0     # 缓存时间戳
        self._cache_duration = 3600  # 缓存1小时

    def _fetch_synonyms_from_api(self):
        """从API获取同义词数据"""
        try:
            response = requests.get("http://dic.summerfarm.net/synonym.txt", timeout=10)
            response.raise_for_status()
            return response.text
        except Exception as e:
            print(f"获取同义词数据失败: {e}")
            return ""

    def _parse_synonym_data(self, synonym_text):
        """解析同义词数据，构建词典"""
        synonym_dict = {}

        for line in synonym_text.strip().split('\n'):
            if not line.strip():
                continue

            # 每行是一组同义词，用逗号分隔
            synonyms = [word.strip() for word in line.split(',') if word.strip()]

            if len(synonyms) > 1:
                # 为每个词建立到整个同义词组的映射
                for word in synonyms:
                    synonym_dict[word] = synonyms

        return synonym_dict

    def _update_cache(self):
        """更新同义词缓存"""
        current_time = time.time()

        # 检查是否需要更新缓存
        if current_time - self._cache_time < self._cache_duration and self._synonym_dict:
            return

        print("正在更新同义词缓存...")
        synonym_text = self._fetch_synonyms_from_api()

        if synonym_text:
            self._synonym_dict = self._parse_synonym_data(synonym_text)
            self._cache_time = current_time
            print(f"同义词缓存更新完成，共加载 {len(self._synonym_dict)} 个词条")
        else:
            print("同义词缓存更新失败")

    def get_synonyms(self, word):
        """获取指定词的同义词列表"""
        self._update_cache()
        return self._synonym_dict.get(word, [])

    def has_synonym_match(self, query, text_fields):
        """
        检查query的同义词是否在指定的文本字段中匹配

        :param query: 查询词
        :param text_fields: 要检查的文本字段列表
        :return: 是否有同义词匹配
        """
        synonyms = self.get_synonyms(query)
        if not synonyms:
            return False

        # 检查同义词是否在任何文本字段中出现
        for field_text in text_fields:
            if not field_text:
                continue

            field_text_lower = str(field_text).lower()
            for synonym in synonyms:
                if synonym.lower() in field_text_lower:
                    return True

        return False


# 全局同义词管理器实例
synonym_manager = SynonymManager()


def _assemble_search_results(hits, city):
    """
    组装搜索结果的通用函数，避免代码重复
    
    :param hits: Elasticsearch返回的hits
    :param city: 城市名称
    :return: 组装后的结果列表
    """
    result_list = []
    for hit in hits:
        source = hit.get("_source", {})
        result = {}
        sku = source.get("itemCode", "")
        result["monthly_sales"], result["monthly_gmv"] = get_sku_monthly_sales(sku)
        result["_score"] = hit.get("_score", "")
        result["sort_score"] = result["_score"]
        result["area_price"] = f"{city}, ¥{source['price']}"
        result["sku_id"] = f'{sku}, {source.get("specification", "")}'
        result["sku"] = sku
        result["spu_name"] = source.get("title", "")
        result["pd_id"] = source.get(
            "marketItemId", source.get("id", "")
        )  # 优先使用marketItemId, 否则使用id
        picture_path = source.get("mainPicture", "404.jpg")
        result["img_url"] = (
            picture_path
            if picture_path.startswith("http")
            else "https://azure.summerfarm.net/" + picture_path
        )+"?imageslim=3"
        result["brand"] = source.get("brandName", "")
        result["weight"] = source.get("specification", "")
        result["store_quantity"] = source.get("storeQuantity", 0)
        result["properties"] = source.get("keyProperty", [])
        result["property_values"] = source.get("propertyValues", [])
        result["category_id"] = source.get("categoryId", "")
        result["sufficient_stock"] = source.get("storeQuantity", 0) > 0
        result["category"] = get_category_text(sku_id=sku)
        category_list = source.get("category", [])
        result["front_category_name"] = category_list
        result_list.append(result)
    return result_list


def search_xianmu_product_directly_from_es(
    query,
    es_host="es-cn-i7m2pv3ht000o90dy.public.elasticsearch.aliyuncs.com:9200",
    es_index="summerfarm_item_info",
    brand_name=None,
    size=60,
    minimum_score=20.0,
    city="杭州",
):
    """
    直接从Elasticsearch搜索鲜沐商品。

    :param query: 搜索关键词。
    :param es_host: Elasticsearch主机地址，默认为公网地址。
    :param es_index: Elasticsearch索引名称，默认为summerfarm_item_info。
    :return: Elasticsearch搜索结果，JSON格式。
    """
    area_no = get_area_no_by_name(city=city)
    es_url = f"http://{es_host}/{es_index}/_search"
    auth = HTTPBasicAuth("elastic", "elastic@Xianmu0619")
    headers = {"Content-Type": "application/json"}
    must = [
        {"term": {"targetId": area_no}},
        {"terms": {"subType": [1, 2, 3, 4]}},  # 添加 subType 的 must 查询
    ]
    if brand_name is not None:
        must.append(
            {"term": {"brandName": brand_name}}
        )  # 这里应该是 brand_name 而不是 query
    search_body = {
        "from": 0,
        "size": size,
        "query": {
            "function_score": {
                "query": {
                    "bool": {
                        "filter": [
                            {"term": {"targetId": area_no}},
                            {"term": {"onSale": 1}},
                            {"term": {"deleteFlag": 1}},
                            {"term": {"marketItemDeleteFlag": 1}},
                            {"term": {"show": 1}},
                            {"term": {"onSaleStrategyType": 3}},
                        ]
                    }
                },
                "functions": [
                    {
                        "filter": {
                            "bool": {
                                "must": [
                                    {"term": {"title": query}},
                                    {"term": {"category.name.keyword": query}},
                                ]
                            }
                        },
                        "weight": 120,
                    },
                    {
                        "filter": {
                            "bool": {
                                "must": {"term": {"title": query}},
                                
                            }
                        },
                        "weight": 80,
                    },
                    {
                        "filter": {
                            "bool": {
                                "should": [
                                    {"match": {"title": query}},
                                    {"term": {"category.name.keyword": query}},
                                ],
                                "minimum_should_match": 1,
                                
                            }
                        },
                        "weight": 40,
                    },
                    {
                        "filter": {
                            "multi_match": {
                                "query": query,
                                "fields": [
                                    "brandName",
                                    "propertyValues",
                                    "keyProperty",
                                    "titlePure",
                                    "titleExt",
                                ],
                                "analyzer": "xianmu_ik_max_word",
                            }
                        },
                        "field_value_factor": {
                            "field": "_matched",
                            "missing": 0,
                            "factor": 1,
                        },
                    },
                ],
                "score_mode": "sum",
                "boost_mode": "replace",
            }
        },
        "sort": [
            {"_score": {"order": "desc"}},
            {
                "_script": {
                    "type": "number",
                    "script": {
                        "source": "doc['storeQuantity'].value * doc['price'].value"
                    },
                    "order": "desc"
                }
            }
        ],
    }

    try:
        print(f"search_body:\n{json.dumps(search_body, indent=4, ensure_ascii=False)}")
        response = requests.post(
            es_url, auth=auth, headers=headers, data=json.dumps(search_body), timeout=5
        )
        response.raise_for_status()  # 检查请求是否成功
        hits = response.json().get("hits", {}).get("hits", [])
        return _assemble_search_results(hits, city)
    except requests.exceptions.RequestException as e:
        print(f"Elasticsearch搜索请求失败: {e}")
        return []


@log_function_time_spend
def search_custom_score_from_es(
    query,
    es_host="es-cn-i7m2pv3ht000o90dy.public.elasticsearch.aliyuncs.com:9200",
    es_index="summerfarm_item_info",
    brand_name=None,
    size=60,
    minimum_score=20.0,
    city="杭州",
):
    """
    使用自定义函数分从 Elasticsearch 搜索鲜沐商品，并用“最高命中规则分”直接替换基础相关性分，以实现可控、稳定的排序。

    设计动机（为什么这样做）：
    - 使用 score_mode = "max"：同一商品可能命中多条规则，只取最高分可防止分数叠加导致排序漂移；规则间天然“互斥”，无需 must_not。
    - 使用 boost_mode = "replace"：用函数分完全替换基础 TF-IDF/向量等分数，确保端到端排序可复现、可调权重。
    - 设定 min_score = 40：清理弱相关召回，提升结果质量与可读性。
    - 引入类目预测与同义词：类目预测用于“强相关召回与兜底”，同义词用于“近义精确词兜底”，共同提升鲁棒性。

    打分规则（functions，按权重从高到低）：
    - 120 分（强精确命中，任一字段精确等值即可）：
      - should 任一命中（minimum_should_match=1）：
        - term(titlePure.keyword, query)
        - term(subTitle.keyword, query)
        - term(marketItemTitle.keyword, query)

    - 100 分（标题/类目强联动，二选一并列规则）：
      1) must：
         - terms(title, [query] + synonyms)
         - terms(category.name.keyword, predicted_front_category_names)
      2) must：
         - wildcard(title, *{query}*)
         - terms(category.name.keyword, predicted_front_category_names)

    - 80 分（标题强相关，二选一并列规则）：
      1) must：
         - terms(title, [query] + synonyms)
      2) must + should（minimum_should_match=1）：
         - must：terms(category.name.keyword, predicted_front_category_names)
         - should：match_phrase(title|marketItemTitle|subTitle, query)

    - 70 分（同义词精确词命中）：
      - should（minimum_should_match=1）：
        - term(title, synonym) 或 term(subTitle, synonym)
      说明：synonym 源自全局同义词表，且排除原词本身，避免重复计分。

    - 60 分（类目强约束 + 文本相关）：
      - must：terms(category.name.keyword, predicted_front_category_names)
      - should（任一命中）：
        - match(title, query, analyzer=xianmu_ik_max_word, operator=OR)
        - match(titlePure, query, operator=OR)
        - match(subTitle, query, operator=OR)

    - 50 分（同类目兜底召回）：
      - terms(category.name.keyword, predicted_front_category_names)

    - 40 分（弱相关兜底）：
      - should（minimum_should_match=1）：
        - match(title, query, analyzer=xianmu_ik_max_word)
        - match(subTitle, query, analyzer=xianmu_ik_max_word)
        - match(brandName, query, analyzer=xianmu_ik_max_word)

    基础查询（bool.should + filter）：
    - should：
      - multi_match(query, fields=[subTitle, titlePure, brandName, category.name, propertyValues, skuName, keyProperty], type=best_fields, analyzer=xianmu_ik_max_word, operator=OR)
      - match(title, query, analyzer=xianmu_ik_max_word, operator=OR)
    - filter：
      - term(targetId, area_no)
      - term(onSale, 1)
      - term(deleteFlag, 1)
      - term(marketItemDeleteFlag, 1)
      - term(show, 1)
      - term(onSaleStrategyType, 3)

    记分与排序：
    - score_mode = "max"
    - boost_mode = "replace"
    - min_score = 40（注意：当前实现将此阈值固定为 40，函数参数 minimum_score 暂未生效）
    - sort：先按 _score desc，再按脚本 doc['storeQuantity'] * doc['price'] desc

    参数：
    - query: 搜索关键词
    - es_host: Elasticsearch 主机地址
    - es_index: Elasticsearch 索引名称
    - brand_name: 可选，品牌过滤
    - size: 返回数量上限
    - minimum_score: 期望的最小分过滤阈值（当前实现未使用，固定为 40）
    - city: 城市（用于 targetId 过滤与区域价格展示）

    返回：
    - 规范化后的商品结果列表（含 _score、area_price、img_url、brand、store_quantity、category 等字段）。
    """
    area_no = get_area_no_by_name(city=city)
    es_url = f"http://{es_host}/{es_index}/_search"
    auth = HTTPBasicAuth("elastic", "elastic@Xianmu0619")
    headers = {"Content-Type": "application/json"}
    
    # 获取查询的类目预测
    category_prediction = get_query_category_prediction(query)
    predicted_front_category_names = category_prediction.get("front_category_name_arr", [])
    
    must = [
        {"term": {"targetId": area_no}},
        {"terms": {"subType": [1, 2, 3, 4]}},  # 添加 subType 的 must 查询
    ]
    if brand_name is not None:
        must.append(
            {"term": {"brandName": brand_name}}
        )
    
    # 构建function_score查询的functions列表
    functions = []
    synonyms = synonym_manager.get_synonyms(query)

    # 规则1.1：120分 - titlePure.keyword 或 subTitle.keyword 或 marketItemTitle.keyword 精确匹配 query（并列条件）
    functions.append({
        "filter": {
            "bool": {
                "should": [
                    {"terms": {"titlePure.keyword": [query]}},
                    {"terms": {"subTitle.keyword": [query]}},
                    {"terms": {"marketItemTitle.keyword": [query]}}
                ],
                "minimum_should_match": 1
            }
        },
        "weight": 120
    })
    
    
    if predicted_front_category_names:
        # 规则2.1：90分 - query在title中term匹配，且前端类目名称term匹配
        functions.append({
            "filter": {
                "bool": {
                    "must": [
                        {"terms": {"title": [query]+synonyms}},
                        {"terms": {"category.name.keyword": predicted_front_category_names}}  # 使用terms替代多个term
                    ]
                }
            },
            "weight": 90
        })

        # 规则2.2：100分 - title 对 query 的wildcard匹配，且类目匹配预测结果
        functions.append({
            "filter": {
                "bool": {
                    "must": [
                        {"wildcard": {"title.keyword": f"*{query}*"}},
                        {"terms": {"category.name.keyword": predicted_front_category_names}}
                    ]
                }
            },
            "weight": 100
        })
        
    
    # 规则3：80分 - query在title中term匹配
    functions.append({
        "filter": {
            "bool": {
                "must": [{"terms": {"title": [query]+synonyms}}]
            }
        },
        "weight": 80
    })

    # 规则3.1：80分 - title 或 marketItemTitle 对 query 的短语匹配，且类目匹配预测结果
    # 说明：在 score_mode=max 下，新增并列 80 分不会影响更高分规则；
    #      使用 should + minimum_should_match=1 支持任一字段短语命中；
    #      同时要求类目 terms 命中预测结果，保证召回相关性。
    if predicted_front_category_names:
        functions.append({
            "filter": {
                "bool": {
                    "must": [
                        {"terms": {"category.name.keyword": predicted_front_category_names}}
                    ],
                    "should": [
                        {"match_phrase": {"title": {"query": query}}},
                        {"match_phrase": {"marketItemTitle": {"query": query}}},
                        {"match_phrase": {"subTitle": {"query": query}}}
                    ],
                    "minimum_should_match": 1
                }
            },
            "weight": 80
        })

    # 规则3.6：70分 - title或subTitle中匹配query的同义词
    if synonyms:
        # 构建同义词的terms查询
        synonym_terms_title = []
        synonym_terms_subtitle = []

        for synonym in synonyms:
            if synonym != query:  # 排除原词本身，避免重复计分
                synonym_terms_title.append({"term": {"title": synonym}})
                synonym_terms_subtitle.append({"term": {"subTitle": synonym}})

        if synonym_terms_title or synonym_terms_subtitle:
            synonym_should_conditions = []
            if synonym_terms_title:
                synonym_should_conditions.extend(synonym_terms_title)
            if synonym_terms_subtitle:
                synonym_should_conditions.extend(synonym_terms_subtitle)

            functions.append({
                "filter": {
                    "bool": {
                        "should": synonym_should_conditions,
                        "minimum_should_match": 1
                    }
                },
                "weight": 70
            })

    # 新规则3.5：60分 - 只要query有类目预测结果，就召回同类目的商品
    if predicted_front_category_names:
        functions.append({
            "filter": {
                "terms": {"category.name.keyword": predicted_front_category_names}  # 使用terms替代循环
            },
            "weight": 50
        })

    # 规则4：60分 - query在title和titlePure中match匹配，且前端类目名称term匹配
    if predicted_front_category_names:
        functions.append({
            "filter": {
                "bool": {
                    "must": [
                        {"terms": {"category.name.keyword": predicted_front_category_names}}
                    ],
                    "should": [
                        {
                            "match": {
                                "title": {
                                    "query": query,
                                    "operator": "OR",
                                    "analyzer": "xianmu_ik_max_word",
                                }
                            }
                        },
                        {
                            "match": {
                                "titlePure": {
                                    "query": query,
                                    "operator": "OR",
                                }
                            }
                        },
                        {
                            "match": {
                                "subTitle": {
                                    "query": query,
                                    "operator": "OR",
                                }
                            }
                        }
                    ],
                }
            },
            "weight": 60
        })
    
    # 规则5：40分 - query在title中match匹配或在brandName中match匹配（不符合前面规则的）
    functions.append({
        "filter": {
            "bool": {
                "should": [
                    {"match": {"title": {"query": query, "analyzer": "xianmu_ik_max_word"}}},
                    {"match": {"subTitle": {"query": query, "analyzer": "xianmu_ik_max_word"}}},
                    {"match": {"brandName": {"query": query, "analyzer": "xianmu_ik_max_word"}}}
                ],
                "minimum_should_match": 1
            }
        },
        "weight": 40
    })
    
    search_body = {
        "from": 0,
        "size": size,
        "query": {
            "function_score": {
                "query": {
                    "bool": {
                        "should": [
                            {
                                "multi_match": {
                                    "query": query,
                                    "fields": [
                                        "subTitle",           # 副标题权重最高
                                        "titlePure",     # 纯标题权重次之，与titlePure.keyword效果相同
                                        "brandName",       # 品牌名权重较高
                                        "category.name", # 类目名权重中等
                                        "propertyValues",    # 属性值
                                        "skuName",        # SKU名称
                                        "keyProperty"        # 关键属性
                                    ],
                                    "type": "best_fields",   # 使用best_fields获得最佳匹配
                                    "analyzer": "xianmu_ik_max_word",
                                    "operator": "OR",
                                }
                            },{
                                "match": {
                                    "title": {
                                        "query": query,
                                        "analyzer": "xianmu_ik_max_word",
                                        "operator": "OR",
                                    }
                                }
                            }
                        ],
                        "filter": [
                            {"term": {"targetId": area_no}},
                            {"term": {"onSale": 1}},
                            {"term": {"deleteFlag": 1}},
                            {"term": {"marketItemDeleteFlag": 1}},
                            {"term": {"show": 1}},
                            {"term": {"onSaleStrategyType": 3}},
                        ]
                    }
                },
                "functions": functions,
                "score_mode": "max",  # 使用最高分，确保每个商品只获得一个分数
                "boost_mode": "replace",
                "min_score": 40  # 最低分数阈值
            }
        },
        "sort": [
            {"_score": {"order": "desc"}},
            {
                "_script": {
                    "type": "number",
                    "script": {
                        "source": "doc['storeQuantity'].value * doc['price'].value"
                    },
                    "order": "desc"
                }
            }
        ],
    }

    try:
        print(f"search_body:\n{json.dumps(search_body, indent=4, ensure_ascii=False)}")
        response = requests.post(
            es_url, auth=auth, headers=headers, data=json.dumps(search_body), timeout=5
        )
        response.raise_for_status()  # 检查请求是否成功
        hits = response.json().get("hits", {}).get("hits", [])
        return _assemble_search_results(hits, city)
    except requests.exceptions.RequestException as e:
        print(f"Elasticsearch搜索请求失败: {e}")
        return []
