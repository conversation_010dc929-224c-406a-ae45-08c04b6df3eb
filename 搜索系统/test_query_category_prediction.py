#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试query类目预测功能
"""

import sys
import os

# 添加搜索系统目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from area_and_category_lib import get_query_category_prediction

def test_query_category_prediction():
    """测试query类目预测功能"""
    print("=== 测试query类目预测功能 ===")
    
    # 测试用例
    test_queries = [
        "苹果",
        "香蕉", 
        "牛奶",
        "面包",
        "不存在的查询词"
    ]
    
    for query in test_queries:
        print(f"\n查询词: '{query}'")
        result = get_query_category_prediction(query)
        
        if result:
            print(f"  类目ID: {result['category4_id']}")
            print(f"  类目名称: {result['category4']}")
            print(f"  预测置信度: {result['category_percentile']:.3f}")
        else:
            print("  未找到预测结果")

if __name__ == "__main__":
    test_query_category_prediction()
