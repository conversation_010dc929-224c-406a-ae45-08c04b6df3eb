#!/usr/bin/env python
# coding: utf-8

# ## PG链接

# In[ ]:


import psycopg2
import logging
import argparse

# Add argument parsing for pg_host
parser = argparse.ArgumentParser()
parser.add_argument("--pg_host", default="127.0.0.1", help="PostgreSQL host address")
args, unknown = parser.parse_known_args()
pg_host = args.pg_host


def get_arena_db_connection_and_execute(
    sql: str, args: tuple = (), pg_host: str = pg_host
):
    conn = psycopg2.connect(
        host=pg_host,
        port=5432,
        database="search_arena",
        user="llmproxy",
        password="dbpassword9090",
    )
    try:
        cur = conn.cursor()
        cur.execute(sql, args)
        
        # Only commit for INSERT, UPDATE, DELETE operations
        if sql.strip().upper().startswith(('INSERT', 'UPDATE', 'DELETE')):
            conn.commit()
            logging.info(f"sql: {sql}, args: {args}, rows affected: {cur.rowcount}")
        
        return cur.fetchall() if cur.description else cur.rowcount
    finally:
        cur.close()
        conn.close()


get_arena_db_connection_and_execute(
    """CREATE TABLE IF NOT EXISTS search_arena_record (
        userId TEXT, referer_url TEXT, user_ip TEXT, user_agent TEXT, create_time TEXT, 
        query TEXT, city TEXT, page_size INTEGER, prefered_version TEXT, comment TEXT)"""
)


# In[ ]:


import sys
import sqlite3
import os
import pandas as pd

# Add the scripts directory to the sys.path
sys.path.append("../")

from odps_client import get_odps_sql_result_as_df

sku_dim_query = """
SELECT  a.sku_id
        ,a.spu_id
        ,a.spu_name
        ,b.sku_name
        ,a.spu_no
        ,a.disc
        ,a.store_method
        ,a.sku_type
        ,a.category1
        ,a.category2
        ,a.category3
        ,a.category4
        ,a.sku_spec
        ,a.origin
        ,a.sku_brand
        ,a.tempature
        ,a.other_properties
        ,a.sub_type
        ,a.ds
        ,price.area_price
        ,COALESCE(b.sku_pic,c.picture_path,'404.jpg') AS img_url
        ,d.total_gmv as current_month_gmv
FROM    summerfarm_tech.dim_sku_df a
INNER JOIN summerfarm_tech.ods_inventory_df b
ON      b.ds = MAX_PT('summerfarm_tech.ods_inventory_df')
AND     a.sku_id = b.sku
INNER JOIN summerfarm_tech.ods_products_df c
ON      c.ds = MAX_PT('summerfarm_tech.ods_products_df')
AND     a.spu_id = c.pd_id
INNER JOIN  (
                SELECT  aa.sku
                        ,ARRAY_JOIN(COLLECT_SET(CONCAT(bb.area_name,'¥',aa.price)),',') AS area_price
                FROM    summerfarm_tech.ods_area_sku_df aa
                INNER JOIN summerfarm_tech.ods_area_df bb
                ON      aa.area_no = bb.area_no
                AND     bb.ds = MAX_PT('summerfarm_tech.ods_area_df')
                WHERE   aa.ds = MAX_PT('summerfarm_tech.ods_area_sku_df')
                AND     bb.area_name IN ('上海','杭州','深圳','广州','苏州','重庆','成都','宁波','武汉普冷','南京','青岛','长沙普冷')
                AND     aa.on_sale = 1
                AND     bb.status = 1
                GROUP BY aa.sku
            ) price
ON      a.sku_id = price.sku
LEFT JOIN   (
                SELECT  sku_id
                        ,SUM(gmv) total_gmv
                        ,COUNT(1) total_count
                        ,SUM(sales_volume) total_sales_volume
                FROM    summerfarm_tech.app_crm_sku_month_gmv_di
                WHERE   ds = MAX_PT('summerfarm_tech.app_crm_sku_month_gmv_di')
                GROUP BY sku_id
            ) d
ON      a.sku_id = d.sku_id
WHERE   a.ds = MAX_PT('summerfarm_tech.dim_sku_df')
AND     a.sub_type != 4
AND     a.outdated != 1
ORDER BY d.total_gmv DESC
;
"""
sku_dim_df = get_odps_sql_result_as_df(sku_dim_query)

db_path = os.path.expanduser('~/sqlite/xianmu_sku_dim_df.db')
os.makedirs(os.path.dirname(db_path), exist_ok=True)

conn = sqlite3.connect(db_path)


# In[ ]:


hangzhou_df=sku_dim_df[sku_dim_df['area_price'].str.contains('杭州¥')]

print(len(hangzhou_df), len(sku_dim_df))
hangzhou_df[hangzhou_df['category4']=='芒果'][['spu_name','sku_id','category4','area_price','current_month_gmv']]


# In[ ]:


area_df = get_odps_sql_result_as_df(
    """select area_no, area_name from summerfarm_tech.ods_area_df 
    where ds=max_pt('summerfarm_tech.ods_area_df')"""
)
area_name_to_no_map = {}
for index, row in area_df.iterrows():
    area_name_to_no_map[row["area_name"]] = row["area_no"]


# In[ ]:


print(sku_dim_df.columns)
# Convert complex columns to string or JSON to avoid binding errors
columns_to_convert = [
    "other_properties",
]
for col in columns_to_convert:
    sku_dim_df[col] = sku_dim_df[col].fillna("").astype(str)

# Select only columns that can be easily serialized
columns_to_save = [
    col
    for col in sku_dim_df.columns
    if sku_dim_df[col].dtype in ["int64", "float64", "object"]
]
sku_dim_df['current_month_gmv']=sku_dim_df['current_month_gmv'].astype(float)
sku_dim_df.to_sql("sku_dim_df", conn, if_exists="replace", index=False)

city_list = set()
for _, row in sku_dim_df.iterrows():
    area_price = row["area_price"]
    area_price = area_price.split(",")
    for price in area_price:
        city_list.add(price.split("¥")[0])

print(f"sku_dim_df.columns:{sku_dim_df.columns}, city_list:{city_list}")


# In[ ]:


import logging
import time
import json
from typing import List
import numpy as np
import faiss
from tqdm import tqdm


from openai import AzureOpenAI
import httpx

client = AzureOpenAI(
    # https://esat-us.openai.azure.com/openai/deployments/text-embedding-3-small/embeddings?api-version=2023-05-15
    api_version="2023-07-01-preview",
    azure_endpoint="https://esat-us.openai.azure.com",
    api_key=os.getenv("AZURE_API_KEY_XM", "please set:AZURE_API_KEY_XM"),
    http_client=httpx.Client(proxies={"http://": None, "https://": None}),
)

embedding_model = os.getenv("AZURE_EMBEDDING_MODEL", "text-embedding-3-small")


def get_embedding_directly_from_azure(input: str) -> list:
    embbed = client.embeddings.create(model=embedding_model, input=input)
    return embbed.to_dict().get("data", [{}])[0].get("embedding")


def get_embedding(input_text: str) -> str:
    result = get_arena_db_connection_and_execute(
        sql="SELECT embedding FROM embeddings WHERE input_text = %s", args=(input_text,)
    )

    if result:
        embedding = result[0]
        embedding = embedding[0] if isinstance(embedding, tuple) else embedding
        logging.info(
            f"Found, return the embedding of input_text:{input_text}, {embedding[:20]}"
        )
    else:
        logging.info(
            f"Not found, call the OpenAI API to get the embedding:{input_text}"
        )
        embedding = str(get_embedding_directly_from_azure(input_text))

        # Insert the new input text and embedding into the database
        get_arena_db_connection_and_execute(
            "INSERT INTO embeddings (input_text, embedding) VALUES (%s, %s)",
            (input_text, embedding),
        )
    return embedding


def get_text_embeddings(text: str, from_local: bool = False) -> np.ndarray:
    """获取文本嵌入向量
    Args:
        text: 输入文本
        batch_size: 批处理大小
    Returns:
        embeddings: 文本嵌入向量
    """
    start_time = time.time()

    embeddings = get_embedding(text)
    
    # If embeddings is a tuple, get the first element
    if isinstance(embeddings, tuple):
        embeddings = embeddings[0]
    # Convert string embeddings to numpy array
    if isinstance(embeddings, str):
        embeddings = json.loads(embeddings)
    embeddings = np.array(embeddings, dtype=np.float32)

    logging.info(
        f"total time cost: {time.time() - start_time}ms, embeddings:{embeddings[0:20]}"
    )
    return embeddings


def batch_get_embeddings(texts: List[str], batch_size: int = 32) -> List[np.ndarray]:
    """批量获取文本嵌入向量
    Args:
        texts: 文本列表
        batch_size: 批处理大小
    Returns:
        embeddings_list: 嵌入向量列表
    """
    embeddings_list = []
    for i in tqdm(range(0, len(texts), batch_size)):
        batch_texts = texts[i : i + batch_size]
        batch_embeddings = [get_text_embeddings(text) for text in batch_texts]
        embeddings_list.extend(batch_embeddings)
    return embeddings_list


# In[ ]:


# 1. 数据预处理和特征工程
# 组合多个字段作为文本特征,增加字段权重
sku_dim_df_clean_100 = sku_dim_df.head(50000)
sku_dim_df_clean_100["sku_brand"] = sku_dim_df_clean_100["sku_brand"].fillna("")
sku_dim_df_clean_100["sku_brand"] = sku_dim_df_clean_100["sku_brand"].replace("无", "")

sku_dim_df_clean_100["text_features"] = sku_dim_df_clean_100.apply(
    lambda row: f'{row["spu_name"]},{row["category4"]},{row["other_properties"]}',
    # 增加商品名称权重
    # 增加四级级类目
    axis=1,
)

sku_dim_df_clean_100["name_features"] = sku_dim_df_clean_100.apply(
    lambda row: f'{row["spu_name"]},{row["sku_brand"]}', axis=1
)

# 2. 批量获取嵌入向量
embeddings = batch_get_embeddings(sku_dim_df_clean_100["text_features"].tolist())
name_embeddings = batch_get_embeddings(sku_dim_df_clean_100["name_features"].tolist())

embeddings_array = np.array(embeddings, dtype=np.float32)  # 确保数据类型为float32
name_embeddings_array = np.array(
    name_embeddings, dtype=np.float32
)  # 确保数据类型为float32

# 3. 构建FAISS索引
# 使用IVFFlat索引提高搜索效率
# 设置合适的聚类中心数量,一般建议为数据量的平方根
# nlist = int(np.sqrt(len(embeddings_array)))
nlist = sku_dim_df_clean_100["spu_id"].unique().shape[0]
quantizer = faiss.IndexFlatL2(embeddings_array.shape[1])
index = faiss.IndexIVFFlat(
    quantizer, embeddings_array.shape[1], nlist, faiss.METRIC_INNER_PRODUCT
)  # 改用内积相似度

name_quantizer = faiss.IndexFlatL2(name_embeddings_array.shape[1])
name_index = faiss.IndexIVFFlat(
    name_quantizer, name_embeddings_array.shape[1], nlist, faiss.METRIC_INNER_PRODUCT
)  # 改用内积相似度

# 归一化向量
embeddings_array = embeddings_array.astype(np.float32)  # 再次确保类型
faiss.normalize_L2(embeddings_array)

name_embeddings_array = name_embeddings_array.astype(np.float32)  # 再次确保类型
faiss.normalize_L2(name_embeddings_array)

index.train(embeddings_array)
index.add(embeddings_array)

name_index.train(name_embeddings_array)
name_index.add(name_embeddings_array)


# In[9]:


def search_similar_skus(query: str, k: int = 5, nprobe: int = 2):
    """搜索相似SKU
    Args:
        query: 查询文本
        k: 返回结果数量
        nprobe: 搜索聚类中心数量
    Returns:
        distances: 距离分数
        indices: 相似商品索引
    """
    query_embedding = np.array(get_text_embeddings(query), dtype=np.float32).reshape(
        1, -1
    )
    # 归一化查询向量
    faiss.normalize_L2(query_embedding)

    # 设置搜索聚类中心数量
    index.nprobe = nprobe
    name_index.nprobe = nprobe

    # 分别搜索两个索引
    D1, I1 = index.search(query_embedding, k)
    D2, I2 = name_index.search(query_embedding, k)

    # 合并结果并计算总分
    results = []
    for i in range(len(I1[0])):
        results.append((I1[0][i], D1[0][i], D2[0][i]))
    for i in range(len(I2[0])):
        if I2[0][i] not in [x[0] for x in results]:
            results.append((I2[0][i], D1[0][i], D2[0][i]))

    # 按总分排序
    results.sort(key=lambda x: x[1] + x[2], reverse=True)

    # 取前k个结果
    results = results[:k]

    # 转换为原始格式
    D = np.array([[x[1] + x[2] for x in results]])
    I = np.array([[x[0] for x in results]])

    return D, I


# In[10]:


import logging


category4_cnt = len(sku_dim_df_clean_100["category4"].unique())


def search_items_with_query(
    query: str,
    city: str = None,
    page_size: int = 20,
    nprobe: int = category4_cnt * 2,
) -> list[dict]:
    """使用向量搜索查找相似商品

    根据输入的查询文本,在商品库中搜索相似的商品。支持按城市筛选价格。

    Args:
        query (str): 查询文本
        city (str, optional): 城市名称,用于筛选该城市的价格。默认为 None
        page_size (int, optional): 返回结果数量。默认为 20
        nprobe (int, optional): FAISS索引搜索聚类中心数量。默认为商品总数的一半

    Returns:
        list[dict]: 搜索结果列表,每个结果包含以下字段:
            - spu_name (str): 商品名称
            - category (str): 商品类目(三级类目_四级类目)
            - category4 (str): 四级类目
            - sku_id (str): SKU ID
            - text_features (str): 商品文本特征
            - name_features (str): 商品名称特征
            - area_price_full (str): 所有城市价格
            - area_price (str): 指定城市价格
            - score (float): 相似度分数
            - img_url (str): 商品图片URL
            - json_result (dict): 完整商品信息

    Examples:
        >>> results = search_items_with_query("苹果", city="上海")
        >>> print(results[0]["spu_name"])
        '红富士苹果'
    """
    D, I = search_similar_skus(
        query,
        k=min(max(page_size * 4, 100), int(len(sku_dim_df_clean_100) / 40)),
        nprobe=nprobe,
    )

    results = []

    # Format results
    for idx, (score, index) in enumerate(zip(D[0], I[0])):
        result_row = sku_dim_df_clean_100.iloc[index].to_dict()
        result_row["score"] = round(score, 4)
        logging.info(f"{query}: {result_row}")
        area_price = result_row["area_price"]
        city_price = {}
        for city_info in area_price.split(","):
            city_in_map, price = city_info.split("¥")
            city_price[city_in_map] = float(price.replace(",", ""))
        if city and city not in city_price:
            logging.warning(f"{city} not in area_price:{area_price}")
            continue
        results.append(
            {
                "spu_name": result_row["spu_name"],
                "category": f"{ result_row['category3']}_{result_row['category4']}",
                "category4": result_row["category4"],
                "sku_id": result_row["sku_id"],
                "text_features": result_row["text_features"],
                "name_features": result_row["name_features"],
                "area_price_full": f"{result_row['area_price']}",
                "area_price": f"{city}¥{city_price[city]}",
                "score": round(score, 4),
                "img_url": f'https://azure.summerfarm.net/{result_row["img_url"]}?imageslim=3',
                "json_result": result_row,
            }
        )
    return results


# In[ ]:


from odps_client import get_odps_sql_result_as_df
from datetime import datetime, timedelta

last_n_days = 14

ds_yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y%m%d")
last_n_days_ago = (datetime.now() - timedelta(days=last_n_days)).strftime("%Y%m%d")

top_query = f"""
SELECT  query
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN cust_id END) AS searched_users
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN CONCAT(time,cust_id) END) AS search_cnt
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN CONCAT(time,cust_id) END) AS click_cnt
        ,ROUND(AVG(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS avg_click_index
        ,ROUND(MAX(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS max_click_index
        ,ROUND(MIN(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS min_click_index
        ,ROUND(STDDEV(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS std_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index
FROM    summerfarm_tech.app_log_search_detail_di
WHERE   ds BETWEEN '{last_n_days_ago}' and '{ds_yesterday}'
GROUP BY query
ORDER BY searched_users DESC
;
"""

top_query_df = get_odps_sql_result_as_df(sql=top_query)
top_query_df.head(5)


# In[ ]:


# 热门的top search term
top_query_cnt_to_display = 50
top_query_df["ctr"] = round(
    top_query_df["click_cnt"] * 1.00 / top_query_df["search_cnt"], 4
)
top_20_query_df = top_query_df.head(top_query_cnt_to_display)
top_20_query = [item.to_dict() for _, item in top_20_query_df.iterrows()]

print(
    f"有点击的query的ctr分布:",
    top_query_df[top_query_df["click_cnt"] > 0]["ctr"].quantile(
        [0.10, 0.25, 0.5, 0.75, 0.90, 0.95]
    ),
)

# 低点击率的top search term
top_high_click_index_20_query_df = top_query_df[
    top_query_df["searched_users"] > 10
].sort_values(by=["search_cnt"], ascending=False)

# Fix the boolean indexing by using & instead of or
top_high_click_index_20_query_df = top_high_click_index_20_query_df[
    (top_high_click_index_20_query_df["min_click_index"] > 6) | 
    (top_high_click_index_20_query_df["ctr"] <= 0.1)
].head(top_query_cnt_to_display)

top_high_click_index_20_query = [
    item.to_dict() for _, item in top_high_click_index_20_query_df.iterrows()
]


# In[ ]:


category_prediction_query=f"""
SELECT  query
        ,b.category4
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN cust_id END) AS searched_users
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN CONCAT(time,cust_id) END) AS search_cnt
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN CONCAT(time,cust_id) END) AS click_cnt
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN cust_id END) AS click_users
        ,ROUND(AVG(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS avg_click_index
        ,ROUND(MAX(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS max_click_index
        ,ROUND(MIN(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS min_click_index
        ,ROUND(STDDEV(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS std_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.25) AS p25_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index
        ,COUNT(DISTINCT a.ds) AS days_have_impression
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN a.ds END) AS days_have_click
FROM    summerfarm_tech.app_log_search_detail_di a
LEFT JOIN summerfarm_tech.dim_sku_df b
ON      b.ds = MAX_PT('summerfarm_tech.dim_sku_df')
AND     a.sku_id = b.sku_id
WHERE   a.ds BETWEEN '{last_n_days_ago}' and '{ds_yesterday}'
GROUP BY query
         ,b.category4
HAVING  click_cnt > 0 and click_users>2
ORDER BY searched_users DESC
;
"""

category_prediction_df=get_odps_sql_result_as_df(category_prediction_query)


# In[ ]:


print(
    category_prediction_df["min_click_index"].quantile(
        [0.25, 0.5, 0.75, 0.80, 0.90, 0.95]
    )
)

category_prediction_valid_df = category_prediction_df[
    (category_prediction_df["min_click_index"] <= 6.0)
    & (category_prediction_df["click_cnt"] >= 7)
]

# Group by query and category4, calculate click counts and percentiles
category_prediction_grouped = (
    category_prediction_valid_df.groupby(["query", "category4"])
    .agg({"click_cnt": "sum"})
    .reset_index()
)

# Calculate percentile rank within each query group
category_prediction_grouped["category_rank"] = category_prediction_grouped.groupby(
    "query"
)["click_cnt"].transform(lambda x: x.rank(pct=True))

# Calculate click count ratio within each query group
category_prediction_grouped["category_percentile"] = (
    category_prediction_grouped.groupby("query")["click_cnt"].transform(
        lambda x: x / x.sum()
    )
)

category_prediction_grouped.sort_values(by=["click_cnt"], ascending=False, inplace=True)
category_prediction_grouped["category_rank"] = category_prediction_grouped[
    "category_rank"
].astype(float)
category_prediction_grouped[
    category_prediction_grouped["query"].isin(["芒果", "奶油", "苹果", "梨"])
].head(20)


# In[ ]:


category_prediction_map = {}

for _, row in category_prediction_grouped.iterrows():
    query = row["query"]
    category4 = row["category4"]
    category_rank = row["category_rank"]
    if query not in category_prediction_map:
        category_prediction_map[query] = {}
    category_prediction_map[query][category4] = category_rank

print(len(category_prediction_map))


def refine_items_with_category4(item_list: list[dict], query: str) -> list[dict]:
    if len(item_list) <= 1:
        return item_list
    if query not in category_prediction_map:
        logging.info(f"query not in category_prediction_map, query={query}")
        return item_list
    category_rank_map = category_prediction_map[query]
    if len(category_rank_map) <= 1:
        logging.info(f"query has only one category prediction result. query={query}")

    # Calculate category weighted score for each item
    for item in item_list:
        if "score" not in item:
            logging.error(f"item has no score, item={item}")
            item["score"] = 0.1
        category4 = item.get("category4")
        if category4 and category4 in category_rank_map:
            item["category_weighted_score"] = (
                item["score"] * category_rank_map[category4]
            )
        else:
            item["category_weighted_score"] = item["score"] * 0.1

    # Sort by category_weighted_score and score
    sorted_items = sorted(
        item_list, key=lambda x: (-x["category_weighted_score"], -x["score"])
    )

    return sorted_items


# ## 用来搜索鲜沐的线上结果

# In[ ]:


import hashlib
import requests
from datetime import datetime


def get_md5_encoded_string(phone_number, date, word):
    input_string = f"{phone_number}{date}{word}"
    input_bytes = input_string.encode("utf-8")
    md5_hash = hashlib.md5(input_bytes)
    md5_hex = md5_hash.hexdigest()
    return md5_hex


token_cache = {}


def get_token_for_phone(phone_number: str = "18618107293") -> str:
    today = datetime.now().strftime("%Y%m%d")

    cache_key = f"{phone_number}{today}"
    if cache_key in token_cache:
        return token_cache.get(cache_key)

    word = "login"
    md5_encoded_string = get_md5_encoded_string(phone_number, today, word)
    logging.info(md5_encoded_string)

    url = f"https://h5.summerfarm.net/openid?phone={phone_number}&sign={md5_encoded_string}"
    logging.info(url)
    token = requests.get(url=url, timeout=12000, proxies={}).json()
    logging.info(f"token:{token}")
    try:
        token_cache[cache_key] = token["data"]["token"]
        return token["data"]["token"]
    except Exception as e:
        logging.error(f"获取Token失败:{url}, {token}, {e}")
        raise e


get_token_for_phone()


def search_xianmu_product(query: str, city: str, page_size: int = 6) -> list[dict]:
    token = get_token_for_phone()
    area_no = area_name_to_no_map.get(city, "1001")
    url = (
        f"https://h5.summerfarm.net/product/1/{page_size}?areaNo={area_no}&pdName={query}"
    )
    headers = {"token": f"{token}", "Content-Type": "application/json"}
    response = requests.get(url=url, headers=headers, timeout=12000, proxies={}).json()
    logging.info(f"query:{ query}, city:{city}, page_size:{page_size}, response:{response}")
    product_list = response["data"]["list"]
    if not isinstance(product_list, list) or len(product_list) <= 0:
        return []
    result_list = []
    for product in product_list:
        result = {}
        result["area_price"] = f"{city}¥{product.get('salePrice', '')}"
        result["sku_id"] = f"{product.get('sku', '')}"
        result["spu_name"] = f"{product.get('pdName', '')}"
        keys_to_extract = [
            "category_id",
            "face_price_hide",
            "info",
            "pd_id",
            "pd_name",
            "pddetail",
            "sale_price",
            "sku",
            "sku_name",
            "sub_type",
            "unit",
            "weight",
            "weight_num",
        ]
        json_result = {
            key: product[key.replace("_", "").lower()]
            for key in keys_to_extract
            if key.replace("_", "").lower() in product
        }
        result["img_url"] = (
            f"https://azure.summerfarm.net/{product['picturePath']}?imageslim=3"
        )
        key_value_list = product["keyValueList"]
        brand = ""
        kv_map = {}
        for key_value in key_value_list:
            if "name" in key_value and "productsPropertyValue" in key_value:
                kv_map[key_value["name"]] = key_value["productsPropertyValue"]
                if key_value["name"] == "品牌":
                    brand = key_value["productsPropertyValue"]
        result["name_features"] = f'{product["pdName"]},{brand}'
        result["text_features"] = f'{product["pdName"]},{kv_map}'
        logging.info(f"{query} summerfarm.net search result: {result}")
        result["json_result"] = json_result
        result_list.append(result)

    return result_list


# In[ ]:


import random


random_query_list=[]

for _,row in top_query_df[top_query_df['searched_users']>10].iterrows():
    random_query_list.append({"query":row['query'],"ctr":row['ctr'],"search_cnt":row['search_cnt']})

print(random.choice(random_query_list))


# ## 启动flask app

# In[31]:


def get_prefered_version_stats():
    sql = """SELECT 
             CASE prefered_version
                WHEN 'new_embedding_search' THEN '新搜索'
                WHEN 'old_es_search' THEN '旧版'
                WHEN 'they-are-tied' THEN '都差不多'
                WHEN 'both-are-poor' THEN '一样差'
             END as prefered_version,
             COUNT(*) as total_count,
             COUNT(DISTINCT userId) as unique_users,
             COUNT(DISTINCT query) as unique_queries
             FROM search_arena_record 
             GROUP BY prefered_version"""
    results = get_arena_db_connection_and_execute(sql)
    stats = {
        row[0]: {
            "total_count": row[1],
            "unique_users": row[2],
            "unique_queries": row[3],
        }
        for row in results
    }
    logging.info(f"Prefered Version Stats: {stats}")
    return stats

def get_all_arena_records():
    sql = """SELECT 
             userId, referer_url, user_ip, user_agent, create_time,
             query, city, page_size, prefered_version, comment
             FROM search_arena_record 
             ORDER BY create_time DESC"""
    results = get_arena_db_connection_and_execute(sql)
    records = []
    for row in results:
        record = {
            "userId": row[0],
            "referer_url": row[1],
            "user_ip": row[2], 
            "user_agent": row[3],
            "create_time": row[4],
            "query": row[5],
            "city": row[6],
            "page_size": row[7],
            "prefered_version": row[8],
            "comment": row[9],
        }
        records.append(record)
    logging.info(f"Total {len(records)} records fetched")
    return records


# In[ ]:


import logging
from flask import Flask, request
import random
from flask import make_response
from networkx import modularity_spectrum

app = Flask(__name__)

import hashlib
from datetime import datetime
from urllib.parse import urlparse, parse_qs
from flask import render_template

default_page_size = 6


@app.route("/report")
def report():
    stats = get_prefered_version_stats()
    return render_template("report.html", stats=stats)

@app.route("/records")
def get_records():
    records = get_all_arena_records()
    return {"records": records}


@app.route("/which-is-better", methods=["POST"])
def which_is_better():
    comment = ""
    if request.is_json:
        version = request.json.get("version", "")
        comment = request.json.get("comment", "")
    else:
        version = request.form.get("version", "")
        comment = request.form.get("comment", "")
    if version not in [
        "new_embedding_search",
        "old_es_search",
        "both-are-poor",
        "they-are-tied",
    ]:
        return {"error": f"Invalid version:{version}"}, 400

    referer_url = request.referrer
    user_ip = request.remote_addr
    user_agent = request.user_agent.string
    userId = hashlib.sha256((user_ip + user_agent).encode()).hexdigest()

    logging.info(
        f"user_id:{userId},referer_url:{referer_url}, which-is-better:{version}"
    )

    query = ""
    city = ""
    page_size = default_page_size

    if referer_url:
        parsed_url = urlparse(referer_url)
        query_params = parse_qs(parsed_url.query)
        query = query_params.get("query", [""])[0]
        city = query_params.get("city", [""])[0]
        page_size = query_params.get("page_size", [f"{default_page_size}"])[0]
        try:
            page_size = int(page_size)
        except ValueError:
            logging.error(f"page_size:{page_size} is not a number")
            page_size = default_page_size

    get_arena_db_connection_and_execute(
        "INSERT INTO search_arena_record VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)",
        (
            userId,
            referer_url,
            user_ip,
            user_agent,
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            query,
            city,
            page_size,
            version,
            comment,
        ),
    )

    return {"message": "OK", "version": version}


@app.route("/", methods=["GET", "POST"])
def search():
    query = (
        request.args.get("query", "")
        if request.method == "GET"
        else request.form.get("query", "")
    )
    page_size = (
        request.args.get("page_size", default_page_size, type=int)
        if request.method == "GET"
        else request.form.get("page_size", default_page_size, type=int)
    )
    city = (
        request.args.get("city", "杭州")
        if request.method == "GET"
        else request.form.get("city", "杭州")
    )
    results = []
    results_from_es = []

    is_left_side = False

    if query:
        logging.info(f"Query: {query}")
        results = refine_items_with_category4(
            item_list=search_items_with_query(
                query=query, city=city, page_size=page_size
            ),
            query=query,
        )
        if isinstance(results, list):
            results = results[:page_size]
        else:
            results = []

        is_left_side = random.choice([True, False])
        print(f"is_left_side:{is_left_side}")

        results_from_es = search_xianmu_product(
            query=query, city=city, page_size=page_size
        )

    response = make_response(
        render_template(
            "search_arena.html",
            query=query,
            city=city,
            page_size=page_size,
            top_20_query=top_20_query,
            city_list=city_list,
            top_high_click_index_20_query=top_high_click_index_20_query,
            left_results=results if is_left_side else results_from_es,
            right_results=results_from_es if is_left_side else results,
            is_left_side=is_left_side,
            last_n_days=last_n_days,
            random_next_query=random.choice(random_query_list),
        )
    )
    response.headers.update(
        {
            "Cache-Control": "no-cache, no-store, must-revalidate",
            "Pragma": "no-cache",
            "Expires": "0",
        }
    )
    return response


import argparse

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--port", type=int, default=5600, help="port number")
    args, unknown = parser.parse_known_args()

    # 开启debug模式以支持模板热重载
    # app.jinja_env.auto_reload = True
    # app.config['TEMPLATES_AUTO_RELOAD'] = True
    app.run(debug=True, host="0.0.0.0", port=args.port)


# In[ ]:





# In[ ]:




