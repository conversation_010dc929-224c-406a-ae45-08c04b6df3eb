from venv import logger


def switch_positions(sku_list: list, i: int, j: int):
    sku_list[i], sku_list[j] = sku_list[j], sku_list[i]


def batch_disperse_skus(
    sku_list: list,
    stock_fun=None,
    batch_size=20,
    sales_volume_func=None,
    category_func=None,
) -> list:
    """
    分段重排序SKU列表，将每段内无库存的SKU排到段末，并可选按销量倒序排序。
    支持在销量排序时，按类目分组后组内倒序排序，保持不同类目SKU的原有顺序。

    :param sku_list: 待处理的SKU列表
    :param stock_fun: 判断SKU是否有库存的函数，返回True表示有库存
    :param batch_size: 每段SKU数量
    :param sales_volume_func: 获取SKU销量的函数，返回销量数值
    :param category_func: 获取SKU类目的函数，返回类目标识
    :return: 处理后的SKU列表
    """
    if not sku_list or batch_size <= 0:
        return sku_list

    # 定义一个辅助函数，用于根据销量和类目对子列表进行排序
    def _sort_sub_list(sub_list):
        if not sub_list:
            return []
        
        # 如果同时提供了类目和销量函数，则按类目分组，组内按销量排序
        if category_func and sales_volume_func:
            logger.warning("按类目分组，组内按销量倒序排序")
            return _sort_by_category_and_sales(sub_list, category_func, sales_volume_func)
        # 如果只提供了销量函数，则仅按销量倒序排序
        elif sales_volume_func:
            logger.warning("仅按销量倒序排序")
            sub_list.sort(key=lambda sku: -sales_volume_func(sku))
            return sub_list
        # 其他情况（例如只有类目函数，或都没有提供）不改变顺序
        return sub_list

    result = []
    for i in range(0, len(sku_list), batch_size):
        batch = sku_list[i:i+batch_size]

        try:
            # 如果提供了库存函数，则优先按库存状态分组
            if stock_fun:
                logger.warning("先按库存排序，有库存的在前")
                in_stock = [sku for sku in batch if stock_fun(sku)]
                out_of_stock = [sku for sku in batch if not stock_fun(sku)]
                
                # 对有库存和无库存的列表分别应用后续的排序逻辑
                batch = _sort_sub_list(in_stock) + _sort_sub_list(out_of_stock)
            else:
                # 如果没有库存函数，则对整个批次应用排序逻辑
                batch = _sort_sub_list(batch)

        except Exception as e:
            # 如果排序过程中出现异常，记录错误但不影响整体流程
            print(f"排序过程中出现异常: {e}, 跳过当前批次排序")

        result.extend(batch)
    return result


def _sort_by_category_and_sales(batch: list, category_func, sales_volume_func) -> list:
    """
    按类目分组并在组内按销量倒序排序，保持类目间的原有顺序

    :param batch: 待排序的SKU批次
    :param category_func: 获取SKU类目的函数
    :param sales_volume_func: 获取SKU销量的函数
    :return: 排序后的SKU列表
    """
    if not batch:
        return batch

    # 使用字典记录每个类目及其在原始顺序中的位置
    category_groups = {}
    category_order = []

    for sku in batch:
        try:
            category = category_func(sku)
            # 记录类目及其顺序
            logger.info(f"category:{category}")
            if category not in category_groups:
                category_groups[category] = []
                category_order.append(category)
            category_groups[category].append(sku)
        except Exception as e:
            print(f"获取SKU类目时出现异常: {e}, SKU: {sku}")
            # 如果获取类目失败，将SKU放入默认类目
            default_category = "unknown"
            if default_category not in category_groups:
                category_groups[default_category] = []
                category_order.append(default_category)
            category_groups[default_category].append(sku)

    # 按原有类目顺序重新组装，每个类目内按销量倒序排序
    result = []
    for category in category_order:
        group_skus = category_groups[category]
        try:
            # 组内按销量倒序排序
            group_skus.sort(key=lambda sku: -sales_volume_func(sku))
        except Exception as e:
            print(f"按销量排序时出现异常: {e}, 类目: {category}")
            # 如果排序失败，保持原有顺序
        result.extend(group_skus)

    return result

def disperse_skus_v2(sku_list: list, spu_func, stock_fun=None, interval=5) -> list:
    """
    打散SKU列表，使得相同SPU的SKU之间至少间隔指定的数量。
    此函数尝试将每个SKU与其后面的SKU进行比较，如果不满足间隔条件，则尝试与后续的SKU交换位置。

    :param sku_list: 待打散的SKU列表。
    :param spu_func: 一个函数，接受SKU作为参数，返回其对应的SPU。
    :param interval: 相同SPU的SKU之间应间隔的最小数量。
    """
    len_of_list = len(sku_list)
    if len_of_list <= 6 or interval <= 1:
        print(f"len_of_list:{len_of_list}, interval:{interval}, 无须重排")
        return sku_list
    if stock_fun:
        # 如果传入了stock_fun，则使用这个方法去获取每个SKU的库存是否>0；
        # 然后把列表中，SPU相同的SKU按照是否有库存做局部排序，即同个SPU的SKU，如果有库存，则排到其他没有库存的SKU前面去。
        spu_to_skus = {}
        for sku in sku_list:
            spu = spu_func(sku)
            if spu not in spu_to_skus:
                spu_to_skus[spu] = []
            spu_to_skus[spu].append(sku)

        sorted_sku_list = []
        for spu in spu_to_skus:
            skus = spu_to_skus[spu]
            # 使用 stock_fun 排序，有库存的排在前面
            sorted_skus = sorted(skus, key=lambda sku: not stock_fun(sku))
            sorted_sku_list.extend(sorted_skus)
        sku_list = sorted_sku_list
    for index, sku in enumerate(sku_list):
        # 获取当前SKU的SPU
        current_spu = spu_func(sku)
        # 跳过第一个元素，因为没有前置元素可以比较
        if index <= 0:
            continue
        # 优化：如果当前索引接近列表末尾，提前终止
        if index >= len(sku_list) - 1:
            print(f"可以终止了, index:{index}, len(sku_list):{len(sku_list)}")
            return sku_list

        # 检查当前SKU与前interval个SKU的SPU是否相同
        is_valid_interval = True
        for previours_sku in sku_list[max(0, index - interval) : index]:
            previours_spu = spu_func(previours_sku)
            if current_spu == previours_spu:
                is_valid_interval = False
                break  # 如果找到相同的SPU，则停止检查

        # 如果当前SKU与前interval个SKU的SPU都不同，则跳过
        if is_valid_interval:
            continue
        else:
            # 否则，遍历当前SKU之后的最多3*interval个SKU
            # 且找到的SKU不得在recent_spus内
            recent_spus = set(
                [spu_func(_sku) for _sku in sku_list[max(0, index - interval) : index]]
            )
            switched = False
            for tailing_index, tailing_sku in enumerate(
                sku_list[index + 1 : min(len_of_list, index + 1 + 3 * interval)]
            ):
                # 获取后续SKU的SPU
                tailing_spu = spu_func(tailing_sku)
                # 如果找到一个SPU与当前SKU不同的SKU，则交换它们的位置
                if current_spu != tailing_spu and not tailing_spu in recent_spus:
                    # 交换位置. 注意这里需要使用原始的索引进行操作
                    switch_positions(sku_list, index, index + 1 + tailing_index)
                    switched = True
                    break  # 交换后跳出循环
                else:
                    # print(f"虽然找到了SPU，但是不符合要求:{tailing_spu}")
                    continue
            if not switched:
                print(
                    f"未能找到合适的元素进行交换, sku:{sku}, index:{index}, recent_spus:{recent_spus}"
                )
    return sku_list


if __name__ == "__main__":

    def spu_func_test(sku_name):
        """
        简单的SPU提取函数，假设SKU名字的第一个字母代表SPU
        """
        return sku_name[0]

    # 构造测试SKU列表，包含多个SPU的SKU
    test_sku_list = [
        "A1",
        "A2",
        "A3",
        "B1",
        "B2",
        "C1",
        "A4",
        "D1",
        "D2",
        "A5",
        "E1",
        "F1",
        "A6",
        "G1",
        "H1",
        "A7",
        "I1",
        "J1",
        "A8",
        "K1",
        "A9",
        "L1",
        "M1",
        "A10",
    ]

    print("原始SKU列表:", test_sku_list)

    # 设置interval为3，swap_window为5进行测试
    dispersed_sku_list = disperse_skus_v2(test_sku_list, spu_func_test, interval=3)
    print("打散后的SKU列表 (interval=3):", dispersed_sku_list)

    # 再次测试，设置interval为5，swap_window为3
    dispersed_sku_list_2 = disperse_skus_v2(test_sku_list, spu_func_test, interval=5)
    print("打散后的SKU列表 (interval=5):", dispersed_sku_list_2)

    # 测试 interval=2， 观察打散效果
    dispersed_sku_list_3 = disperse_skus_v2(test_sku_list, spu_func_test, interval=2)
    print("打散后的SKU列表 (interval=2):", dispersed_sku_list_3)
