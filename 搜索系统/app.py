import random
from venv import logger
from flask import Flask, render_template, make_response, request, jsonify
from urllib.parse import urlparse, parse_qs

app = Flask(__name__)

import hashlib
import logging
from datetime import datetime
from search_xianmu_product import search_xianmu_product
from search_xianmu_directly_from_es import search_xianmu_product_directly_from_es, search_custom_score_from_es
from category_prediction import (
    get_query_category_prediction,
    get_random_query,
    get_sku_category,
    get_query_metrics,
    top_20_query,
    middle_20_query,
    top_high_click_index_20_query,
    last_n_days,
)

from disperse_sku_by_its_spu import disperse_skus_v2, batch_disperse_skus

city_list=['杭州', '广州', '上海', '重庆', '成都', '青岛', '深圳', '武汉普冷','南京','苏州','长沙','厦门']


# 定义SPU提取函数
def spu_func(sku):
    return f"pd_id:{sku.get('pd_id')}"  # 提取pd_id


def stock_fun(sku):
    return sku["sufficient_stock"] if "sufficient_stock" in sku else True

# 仅仅展示一个结果集
@app.route("/search")
def search_without_arena():
    query = request.args.get("query", get_random_query()["query"])
    city = request.args.get("city", "杭州")
    page_size = int(request.args.get("page_size", default_page_size))

    # 获取查询指标数据
    query_metrics = get_query_metrics(query=query)
    logging.info(f"query_metrics for {query}: {query_metrics}")

    results = search_xianmu_product(
        query, city, 200 + page_size
    )  # 获取600+page_size个，用以排序；

    # 为每个结果添加类目信息
    for result in results:
        result["category_name"] = get_sku_category(result["sku"])
        
    result_container = [
        {"version": "original_es", "results": results[0:page_size]},
    ]
    random.shuffle(result_container)

    return render_template(
        "search_arena_single.html",
        query=query,
        city=city,
        city_list=city_list,
        page_size=page_size,
        result_container=result_container,
        middle_20_query=middle_20_query,
        top_20_query=top_20_query,
        top_high_click_index_20_query=top_high_click_index_20_query,
        last_n_days=last_n_days,
        random_next_query=get_random_query(),
        query_metrics=query_metrics,
    )

@app.route("/")
def index():
    query = request.args.get("query", get_random_query()["query"])
    city = request.args.get("city", "杭州")
    page_size = int(request.args.get("page_size", default_page_size))
    if page_size > 200:
        logger.error(f"page_size:{page_size} is too large, set to 200")
        page_size = 200

    category_prediction = get_query_category_prediction(query=query)
    if "category" in category_prediction:
        logging.info(f"category_prediction found:{category_prediction}, query:{query}")

    # 获取查询指标数据
    query_metrics = get_query_metrics(query=query)
    logging.info(f"query_metrics for {query}: {query_metrics}")

    results = search_xianmu_product(
        query, city, 200
    )  # 获取200个，用以排序；

    # 使用自定义打分搜索
    custom_score_results = search_custom_score_from_es(
        query=query, city=city, size=200
    )

    # 为每个结果添加类目信息
    for result in results:
        result["category_name"] = get_sku_category(result["sku"])

    for result in custom_score_results:
        result["category_name"] = get_sku_category(result["sku"])

    original_top_result = results[0:page_size].copy()

    def _sort_score_and_gmv(custom_score_gmv_sorted):
        # 方案：按分数分组，组内按GMV排序，最后拼接
        from collections import defaultdict

        # 步骤1：按分数分组
        score_groups = defaultdict(list)
        for item in custom_score_gmv_sorted:
            score = item.get("sort_score", 0)
            if score <= 0:
                print(f"score is less than or equal to 0:{item}")
            score_groups[score].append(item)

        # 步骤2：各分组内先按库存优先，再对有库存部分按GMV降序
        # 这么做的原因：业务上优先展示可售卖的SKU，再在可售卖里用GMV体现商业价值；缺货SKU沉底但保持相对顺序，避免误导用户
        for score in score_groups:
            group_items = score_groups[score]
            in_stock_items = [x for x in group_items if stock_fun(x)]
            out_of_stock_items = [x for x in group_items if not stock_fun(x)]
            # 对有库存的商品按GMV倒序
            in_stock_items.sort(key=lambda x: float(x.get("monthly_gmv", 0)), reverse=True)
            # 无库存的商品沉底，保持原有相对顺序，避免对用户形成“高GMV但不可售”的误导
            score_groups[score] = in_stock_items + out_of_stock_items

        # 步骤3：按分数降序拼接各组
        result_list = []
        # 分数按降序排列：120, 100, 80, 60, 40
        for score in sorted(score_groups.keys(), reverse=True):
            result_list.extend(score_groups[score])

        return result_list

    custom_score_gmv_sorted=_sort_score_and_gmv(custom_score_results.copy())

    # 自定义搜索集合改造：
    # 为什么要这样做：优先展示自定义打分中高分（≥70）的SKU，
    # 同时补充ES原始结果的广覆盖，且以"高分集合"为基准去重，避免重复干扰用户判断。
    def _build_custom_score_search(high_score_sorted, es_results, score_threshold=70):
        # 取高分SKU（保持排序）
        high = [item for item in high_score_sorted if float(item.get("sort_score", 0)) >= score_threshold]
        seen = set()
        merged = []
        # 先放入高分集合
        for item in high:
            sku_id = item.get("sku_id") or item.get("sku", {}).get("sku_id")
            if sku_id and sku_id not in seen:
                seen.add(sku_id)
                merged.append(item)
        # 再补充ES结果中未出现的SKU
        for item in es_results:
            sku_id = item.get("sku_id") or item.get("sku", {}).get("sku_id")
            if sku_id and sku_id not in seen:
                seen.add(sku_id)
                merged.append(item)
        return merged

    custom_score_search_merged = _build_custom_score_search(custom_score_gmv_sorted, results)

    result_container = [
        {"version": "线上ES原生召回", "results": original_top_result},
        {
            "version": "自定义打分、ES原生合并", 
            "results": custom_score_search_merged[0:page_size],
        },
        {
            "version": "自定义打分后按GMV排序",
            "results": custom_score_gmv_sorted[0:page_size],
        },
    ]
    # random.shuffle(result_container)

    return render_template(
        "search_arena_es.html",
        query=query,
        city=city,
        city_list=city_list,
        page_size=page_size,
        result_container=result_container,
        middle_20_query=middle_20_query,
        top_20_query=top_20_query,
        top_high_click_index_20_query=top_high_click_index_20_query,
        last_n_days=last_n_days,
        random_next_query=get_random_query(),
        query_metrics=query_metrics,
    )


default_page_size = 20

from get_arena_db_connection_and_execute import get_arena_db_connection_and_execute


@app.route("/which-is-better", methods=["POST"])
def which_is_better():
    comment = ""
    if request.is_json:
        version = request.json.get("version", "")
        comment = request.json.get("comment", "")
    else:
        version = request.form.get("version", "")
        comment = request.form.get("comment", "")
    if version not in [
        "rewrote_query",
        "refine_items_with_category",
        "original_es",
        "both-are-poor",
        "they-are-tied",
        "custom_score_search",
        "custom_score_gmv_sorted",
        "new_es_recall_no_rerank",
        "new_es_recall_with_rerank",
    ]:
        return {"error": f"Invalid version:{version}"}, 400

    referer_url = request.referrer
    user_ip = request.remote_addr
    user_agent = request.user_agent.string
    userId = hashlib.sha256((user_ip + user_agent).encode()).hexdigest()

    logging.info(
        f"user_id:{userId},referer_url:{referer_url}, which-is-better:{version}"
    )

    query = ""
    city = ""
    page_size = default_page_size

    if referer_url:
        parsed_url = urlparse(referer_url)
        query_params = parse_qs(parsed_url.query)
        query = query_params.get("query", [""])[0]
        city = query_params.get("city", [""])[0]
        page_size = query_params.get("page_size", [f"{default_page_size}"])[0]
        try:
            page_size = int(page_size)
        except ValueError:
            logging.error(f"page_size:{page_size} is not a number")
            page_size = default_page_size

    get_arena_db_connection_and_execute(
        "INSERT INTO search_arena_record VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)",
        (
            userId,
            referer_url,
            user_ip,
            user_agent,
            datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            query,
            city,
            page_size,
            version,
            comment,
        ),
    )

    return {"message": "OK", "version": version}


def get_prefered_version_stats_v2():
    sql = """SELECT 
             CASE prefered_version
                WHEN 'rewrote_query' THEN 'query重写'
                WHEN 'refine_items_with_category' THEN '类目重排序'
                WHEN 'original_es' THEN '原始搜索'
                WHEN 'new_es_recall_no_rerank' THEN '新召回无重排'
                WHEN 'new_es_recall_with_rerank' THEN '新召回有重排'
                WHEN 'custom_score_search' THEN '自定义打分'
                WHEN 'custom_score_gmv_sorted' THEN 'GMV排序'
                WHEN 'they-are-tied' THEN '都不错'
                WHEN 'both-are-poor' THEN '都很差'
             END as prefered_version,
             COUNT(*) as total_count,
             COUNT(DISTINCT userId) as unique_users,
             COUNT(DISTINCT query) as unique_queries
             FROM search_arena_record 
             WHERE prefered_version IN ('rewrote_query', 'refine_items_with_category', 'original_es', 'new_es_recall_no_rerank', 'new_es_recall_with_rerank', 'custom_score_search', 'custom_score_gmv_sorted', 'both-are-poor', 'they-are-tied')
             AND create_time >= '2024-12-11'
             GROUP BY prefered_version"""
    results = get_arena_db_connection_and_execute(sql)
    print(f"results:{results}")
    return results


@app.route("/report-v2")
def report_v2():
    stats = get_prefered_version_stats_v2()
    return render_template("report.html", stats=stats)

@app.route("/api/search-content", methods=["GET"])
def api_search_content():
    """
    REST API接口，100%复现当前搜索页面的数据内容
    包括自定义打分排序和GMV排序结果
    """
    from flask import jsonify
    
    query = request.args.get("query", "")
    city = request.args.get("city", "杭州")
    page_size = int(request.args.get("page_size", default_page_size))
    if page_size > 200:
        page_size = 200

    # 获取类目预测
    category_prediction = get_query_category_prediction(query=query)
    
    # 获取查询指标数据
    query_metrics = get_query_metrics(query=query)

    # 获取原始搜索结果
    results = search_xianmu_product(query, city, 200)

    # 获取自定义打分搜索结果
    custom_score_results = search_custom_score_from_es(
        query=query, city=city, size=200
    )

    # 为每个结果添加类目信息
    for result in results:
        result["category_name"] = get_sku_category(result["sku"])

    for result in custom_score_results:
        result["category_name"] = get_sku_category(result["sku"])

    # GMV排序函数
    def _sort_score_and_gmv(custom_score_gmv_sorted):
        from collections import defaultdict
        
        # 按分数分组
        score_groups = defaultdict(list)
        for item in custom_score_gmv_sorted:
            score = item.get("sort_score", 0)
            score_groups[score].append(item)

        # 各分组内：先有库存优先，再对有库存部分按GMV倒序排序
        # 原因：页面展示优先考虑可售卖性，其次用GMV衡量商业价值；缺货沉底但不再以GMV干预其相对顺序
        for score in score_groups:
            group_items = score_groups[score]
            in_stock_items = [x for x in group_items if stock_fun(x.get("sku", {}))]
            out_of_stock_items = [x for x in group_items if not stock_fun(x.get("sku", {}))]
            in_stock_items.sort(key=lambda x: float(x.get("monthly_gmv", 0)), reverse=True)
            score_groups[score] = in_stock_items + out_of_stock_items

        # 按分数降序拼接各组
        result_list = []
        for score in sorted(score_groups.keys(), reverse=True):
            result_list.extend(score_groups[score])

        return result_list

    custom_score_gmv_sorted = _sort_score_and_gmv(custom_score_results.copy())

    # 自定义搜索集合改造（与页面一致）：
    def _build_custom_score_search(high_score_sorted, es_results, score_threshold=70):
        high = [item for item in high_score_sorted if float(item.get("sort_score", 0)) >= score_threshold]
        seen = set()
        merged = []
        for item in high:
            sku_id = item.get("sku_id") or item.get("sku", {}).get("sku_id")
            if sku_id and sku_id not in seen:
                seen.add(sku_id)
                merged.append(item)
        for item in es_results:
            sku_id = item.get("sku_id") or item.get("sku", {}).get("sku_id")
            if sku_id and sku_id not in seen:
                seen.add(sku_id)
                merged.append(item)
        return merged

    custom_score_search_merged = _build_custom_score_search(custom_score_gmv_sorted, results)

    # 构建结果容器，完全复现search_arena_es.html的数据结构
    result_container = [
        {
            "version": "original_es", 
            "results": results[0:page_size]
        },
        {
            "version": "custom_score_search", 
            "results": custom_score_search_merged[0:page_size]
        },
        {
            "version": "custom_score_gmv_sorted", 
            "results": custom_score_gmv_sorted[0:page_size]
        }
    ]

    # 构建完整的响应数据，包含所有上下文信息
    response_data = {
        "query": query,
        "city": city,
        "page_size": page_size,
        "category_prediction": category_prediction,
        "query_metrics": query_metrics,
        "result_container": result_container,
        # "city_list": city_list,
        # "middle_20_query": middle_20_query,
        # "top_20_query": top_20_query,
        # "top_high_click_index_20_query": top_high_click_index_20_query,
        "last_n_days": last_n_days,
        # "random_next_query": get_random_query()
    }

    return jsonify(response_data)


import argparse

if __name__ == "__main__":
    port = 5800
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--debug", action="store_true", default=False, help="Enable debug mode"
    )
    args, unknown = parser.parse_known_args()
    app.run(debug=args.debug, port=port, host="0.0.0.0")
