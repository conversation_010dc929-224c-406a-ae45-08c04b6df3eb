#!/usr/bin/env python
# coding: utf-8


from datetime import datetime
import logging
import requests


url = (
    "https://open.feishu.cn/open-apis/bot/v2/hook/aaf777eb-819c-4701-bc4b-893bf02addfc"
)


def send_feishu_notice_with_title_and_content(
    markdown_str: str,
    feishu_url=url,
    title="",
    error=False,
):
    feishu_message_obj = {
        "schema": "2.0",
        "header": {
            "template": "red" if error else "blue",
            "title": {
                "content": f"**{title}**",
                "tag": "lark_md",
            },
        },
        "body": {
            "elements": [
                {
                    "tag": "markdown",
                    "content": markdown_str,
                },
                {
                    "tag": "markdown",
                    "content": f"> 数据生成于:{datetime.now().strftime('%Y-%m-%d %H:%M')}\n> ",
                },
            ]
        },
    }
    headers = {"Content-Type": "application/json"}
    data = {"msg_type": "interactive", "card": feishu_message_obj}
    feishu_result = requests.post(
        url=feishu_url, json=data, headers=headers, verify=False, proxies={}
    ).json()
    return feishu_result


from datetime import datetime, timedelta
import sys
import os
import pandas as pd

sys.path.append("../")

# category_prediction_df 获取
from odps_client import get_odps_sql_result_as_df

# 创建缓存目录，用于存储数据
CACHE_DIR = "搜索系统/keyword_perf_cache"
os.makedirs(CACHE_DIR, exist_ok=True)


last_n_days = 14
last_half_n_days = int(last_n_days / 2)
ds_yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y%m%d")
last_n_days_ago = (datetime.now() - timedelta(days=last_n_days)).strftime("%Y%m%d")
last_half_n_days_ago = (datetime.now() - timedelta(last_half_n_days)).strftime("%Y%m%d")

category_prediction_sql = f"""
SELECT  query
        ,ARRAY_JOIN(COLLECT_SET(CONCAT(category4,':',category_percentile)),',') AS category_prediction
FROM    summerfarm_tech.app_xianmu_search_category_prediction_df
WHERE   ds = MAX_PT('summerfarm_tech.app_xianmu_search_category_prediction_df')
GROUP BY query
ORDER BY query;
"""

category_prediction_df = get_odps_sql_result_as_df(sql=category_prediction_sql)
print(category_prediction_df.head(2))

top_query = f"""
SELECT  query
        ,case when ds >= '{last_half_n_days_ago}' then '最近{last_half_n_days}天' else '{last_half_n_days}天以前' end as 日期标签
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN cust_id END) AS searched_users
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN CONCAT(time,cust_id) END) AS search_cnt
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN CONCAT(time,cust_id) END) AS click_cnt
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN cust_id END) AS clicked_user_cnt
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN cust_id END)*1.00/COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN cust_id END) as user_ctr
        ,ROUND(AVG(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS avg_click_index
        ,ROUND(MAX(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS max_click_index
        ,ROUND(MIN(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS min_click_index
        ,ROUND(STDDEV(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS std_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index
FROM    summerfarm_tech.app_log_search_detail_di
WHERE   ds BETWEEN '{last_n_days_ago}' and '{ds_yesterday}'
GROUP BY query,日期标签
ORDER BY searched_users DESC
;
"""

# 尝试从缓存加载 top_query_df，如果失败则从ODPS获取并缓存
top_query_df = get_odps_sql_result_as_df(sql=top_query)

category_sql = """
SELECT  category4_id AS 类目ID
        ,category 类目名称
        ,ARRAY_JOIN(COLLECT_SET(spu_name),',') AS 商品举例
FROM    (
            SELECT  category4_id
                    ,CONCAT(category2,'_',category3,'_',category4) AS category
                    ,spu_name
                    ,RANK() OVER (PARTITION BY category4_id ORDER BY create_date DESC ) AS rnk
            FROM    summerfarm_tech.dim_sku_df
            WHERE   ds = MAX_PT('summerfarm_tech.dim_sku_df')
            AND     spu_name IS NOT NULL
            AND     LENGTH(spu_name) > 1
            AND     category4 IS NOT NULL
            AND     LENGTH(category4) > 1
            AND     category2 not like 'POP%'
            QUALIFY RANK() OVER (PARTITION BY category4_id ORDER BY create_date DESC ) <= 3
        ) 
GROUP BY category4_id
         ,category;
"""

category_df = get_odps_sql_result_as_df(sql=category_sql)
print("category_df:\n", category_df.head(5))
category_as_csv_content = category_df.to_csv(index=False)
print("category_as_csv_content:\n", category_as_csv_content[:100])

top_query_label = f"""
SELECT  query
        ,COUNT(distinct cust_id) as searched_users
        ,SUM(CASE WHEN envent_type = 'impression' and idx='0' THEN 1 ELSE 0 END) as searched_times
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN cust_id END) AS clicked_user_cnt
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN cust_id END)*1.00/COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN cust_id END) as user_ctr
        ,COUNT(CASE    WHEN envent_type = 'click' THEN 1 END)*1.00/COUNT(CASE    WHEN envent_type = 'impression' THEN 1 END) as sku_ctr
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index
FROM    summerfarm_tech.app_log_search_detail_di
WHERE   ds BETWEEN '20250528' and '20250610'
GROUP BY query
order by searched_users desc;
"""

top_query_labeled_df = get_odps_sql_result_as_df(sql=top_query_label)
print(top_query_labeled_df.head(5))


top_query_labeled_df["searched_times"].fillna(1)  # 默认肯定有至少1次搜索
top_query_labeled_df["searched_times"] = top_query_labeled_df["searched_times"].astype(
    int
)

print(
    top_query_labeled_df["searched_users"].quantile([0.1, 0.25, 0.5, 0.75, 0.90, 0.95])
)
print(
    top_query_labeled_df["searched_times"].quantile([0.1, 0.25, 0.5, 0.75, 0.90, 0.95])
)


# 计算不同搜索次数阈值下的累积搜索次数占比
searched_times_counts = top_query_labeled_df["searched_times"].sort_values()
cumulative_times = searched_times_counts.cumsum()
total_times = cumulative_times.iloc[-1]
print(f"total_times:{total_times}")


top_query_labeled_df["搜索频次标签"] = top_query_labeled_df["searched_times"].apply(
    lambda x: "高频搜索词" if x > 100 else "低频搜索词" if x <= 5 else "中频搜索词"
)

# 根据"搜索频次标签"分组，并计算每个标签下的query数量和搜索次数总和
frequency_group = top_query_labeled_df.groupby("搜索频次标签").agg(
    query_count=("query", "count"),  # 计算每个标签下的query数量
    total_searched_times=("searched_times", "sum"),  # 计算每个标签下的搜索次数总和
)

# 计算每个分组的搜索次数占比
frequency_group["search_times_share"] = (
    frequency_group["total_searched_times"] / total_times
)
# 计算每个分组的query数量占比
frequency_group["query_count_share"] = (
    frequency_group["query_count"] / top_query_labeled_df.shape[0]
)
print(frequency_group)


top_query_labeled_df[top_query_labeled_df["搜索频次标签"] == "高频搜索词"][
    "user_ctr"
].quantile([0.1, 0.25, 0.5, 0.75, 0.9])


# 为每个标签创建一个字典来存储对应的DataFrame
dfs_by_label = {}

# 获取所有唯一的搜索频次标签
unique_labels = top_query_labeled_df["搜索频次标签"].unique()

for label in unique_labels:
    # 计算当前标签下user_ctr的10%分位数
    quantile_10 = top_query_labeled_df[top_query_labeled_df["搜索频次标签"] == label][
        "user_ctr"
    ].quantile(0.1)
    # 筛选出当前标签下user_ctr小于等于10%分位数的数据
    df_filtered = top_query_labeled_df[
        (top_query_labeled_df["搜索频次标签"] == label)
        & (top_query_labeled_df["user_ctr"] <= quantile_10)
    ]
    # 将筛选后的DataFrame存储到字典中，键为标签名
    dfs_by_label[label] = df_filtered

# 现在dfs_by_label包含了每个标签对应的DataFrame，你可以通过标签名访问它们
print("计算 low_perf_df...")
high_frequency_df = dfs_by_label["高频搜索词"]

low_perf_df = top_query_labeled_df[
    (top_query_labeled_df["sku_ctr"] <= 0.05)
    & (top_query_labeled_df["p50_click_index"] >= 10.0)
    & (top_query_labeled_df["searched_users"] >= 10)
    & (~top_query_labeled_df["query"].isin(category_prediction_df["query"]))
]
print(f"low_perf_df: {low_perf_df.head(20)}")


system_prompt = """你是一位专业的电商搜索引擎优化分析师，专注于餐饮供应链领域。你的任务是分析点击率低的搜索词，找出问题原因并提供改进策略。

分析对象：轻饮食行业供应链平台（主营烘焙、茶饮、咖啡等轻饮食行业的原料，包括鲜果、乳制品、面粉、西餐原材料等餐饮门店物料）

当用户提供以下信息时：
1. 低点击率的搜索词
2. 该搜索词获得的搜索结果（商品SKU信息,前60个,实际上用户可能会看到比60个还要多的内容,我们仅需要基于这60个来分析）

你需要进行深度分析，并根据分析结果，调用 `record_optimization_suggestions` 函数来提交你的改进策略建议。

分析步骤：
1. **搜索词与结果的匹配度分析**：
   - 深度回顾该搜索词的搜索表现（用户会给出数据，请你完全依据用户的数据来深度解析）。
   - 语义匹配程度。
   - 召回的商品的类别是否符合搜索意图。
   - 搜索词是否存在歧义或多义性。
   - 召回的商品是否库存不足导致点击率低。
   - 是否存在低销量的商品大量挤占了高销量的商品的展示位置。

2. **用户意图解析**：
   - 用户可能的真实需求是什么。
   - 搜索词背后的使用场景分析。
   - 行业特定术语vs通用词汇的使用差异。

3. **具体问题诊断**（选择适用的）：
   - 关键词匹配问题（如：ES词库缺失、同义词匹配缺失）。
   - 商品缺失问题（平台缺少相关商品）。
   - 结果排序问题（相关度算法调整需求, 如：类目权重调整、无库存商品降权、低销量商品降权等）。
   - 季节性或时效性问题。
   - 行业术语与日常用语差异问题。
   - 商品属性标注不完善问题。

4. **提交改进策略**：
   - 根据你的分析，总结出词库优化方案（同义词、近义词等）和类目权重调整方案。
   - **【非常重要】** 你必须调用 `record_optimization_suggestions` 函数来提交这些建议。
     - `query` 参数应为当前分析的搜索词。
     - `synonyms` 参数应为一个包含所有建议同义词的Python列表。
     - `category_weights` 参数应为一个Python字典，键是类目ID（字符串），值是包含 'weight' (float, 2位小数) 和 'reason' (str) 的字典。所有权重之和应为1。
     - ``diagnosis_result` 参数应为你的诊断结果和分析过程(简明扼要，不要超过200字)。

请提供具体、可操作的分析和建议，避免笼统的回答。针对餐饮供应链的特性，考虑B2B客户的专业采购需求与习惯。
"""


import json
import os
import sys
import asyncio

from agents import Agent, function_tool, Runner
from openai.types.responses import ResponseTextDeltaEvent
from agents.extensions.models.litellm_model import LitellmModel


sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from search_xianmu_product import search_xianmu_product


from typing import List
from typing_extensions import TypedDict


# 为 category_weights 定义一个强类型结构，以解决 agents 库因使用 Dict[str, Any] 而导致的严格模式 schema 错误
class CategoryWeightInfo(TypedDict):
    category_id: str
    weight: float
    reason: str


total_final_synonyms_result = []
total_final_category_weights_result = []


@function_tool
def record_optimization_suggestions(
    query: str,
    synonyms: List[str],
    category_weights: List[CategoryWeightInfo],
    diagnosis_result: str,
):
    """
    记录对指定搜索词的优化建议，包括同义词和类目权重。

    参数:
        query (str): 需要优化的原始搜索词。
        synonyms (List[str]): 建议添加的同义词列表。
        category_weights (List[CategoryWeightInfo]): 建议的类目权重调整。格式应为: [{"category_id": "类目ID_1", "weight": 0.8, "reason": "原因1"}, {"category_id": "类目ID_2", "weight": 0.2, "reason": "原因2"}, ...]
        diagnosis_result (str): 你的诊断结果和分析过程(简明扼要，不要超过200字)。
    """
    global total_final_synonyms_result
    global total_final_category_weights_result

    synonyms_result = f"{query},{','.join(synonyms)}"
    total_final_synonyms_result.append(synonyms_result)

    # 格式化类目权重，用于飞书消息展示
    category_weights_lines = [
        f"- {item['category_id']}:{item['weight']}, {item['reason']}"
        for item in category_weights
    ]
    category_weights_str = "\n".join(category_weights_lines)

    # 如果当前query已存在类目预测，则不更新其类目权重
    if query in category_prediction_df["query"].values:
        print(f"'{query}' 已存在类目预测，跳过写入新的类目权重。")
    else:
        # 格式化类目权重，用于生成Redis命令
        _category_weights_parts = [
            f"{item['category_id']} {item['weight']}" for item in category_weights
        ]
        _category_weights_str = " ".join(_category_weights_parts)
        total_final_category_weights_result.append(
            f"HSET search_query_category_percentile_{query} {_category_weights_str}"
        )

    final_markdown_output = f"## 诊断结果\n\n{diagnosis_result}\n\n## 优化建议\n\n### 同义词\n\n{query},{','.join(synonyms)}\n\n### 类目权重\n\n{category_weights_str}"
    print(f"‘{query}’搜索词的AI分析报告:\n{final_markdown_output}")
    # send_feishu_notice_with_title_and_content(
    #     title=f"‘{query}’搜索词的AI分析报告", markdown_str=final_markdown_output
    # )

    return "优化建议已成功记录。"


# 创建使用LiteLLM的Agent实例
agent = Agent(
    name="搜索词优化分析师",
    model=LitellmModel(
        model="openai/qwen3-235b-a22b-no-thinking",
        api_key=os.getenv("XM_FAST_GPT_API_KEY"),
        base_url="https://litellm-test.summerfarm.net/v1",
    ),
    instructions=system_prompt,
    tools=[record_optimization_suggestions],
)


# 全局变量记录已分析的query
analyzed_queries = set()


def analytics_with_r1(
    query: str = "寒天",
    csv_content="",
    category_as_csv_content=category_as_csv_content,
    low_perf_df: pd.DataFrame = pd.DataFrame(),
):
    """
    使用Agent模型分析搜索词表现，并通过工具函数记录优化建议。
    """
    global analyzed_queries

    # 检查是否已分析过该query
    if query in analyzed_queries:
        print(f"Query '{query}' 已分析过，跳过重复分析")
        return "", ""

    analyzed_queries.add(query)

    async def _run_analysis():
        # 准备用户输入
        perf = low_perf_df[low_perf_df["query"] == query].iloc[0].to_dict()
        category_prediction = category_prediction_df[
            category_prediction_df["query"] == query
        ]
        if not category_prediction.empty:
            category_prediction = category_prediction.iloc[0]["category_prediction"]
        else:
            category_prediction = "没有类目预测结果"

        user_prompt = f"""### 搜索词:{query}

### 搜索表现:{json.dumps(perf, ensure_ascii=False, indent=2)}

### 搜索结果:\n{csv_content}

### 类目信息:\n{category_as_csv_content}

### 本搜索词的类目预测(如果有的话，则不需要再次预测，直接使用即可，同时要告知用户):\n{category_prediction}"""

        print(f"开始使用Agent进行分析: {query}")

        final_response_text = ""
        try:
            from agents import ItemHelpers

            # 运行Agent并处理流式响应
            result_stream = Runner.run_streamed(agent, input=user_prompt)
            async for event in result_stream.stream_events():
                # 实时打印模型生成的文本内容
                if (
                    event.type == "raw_response_event"
                    and hasattr(event.data, "delta")
                    and event.data.delta
                ):
                    delta_content = event.data.delta
                    if delta_content:
                        print(delta_content, end="", flush=True)
                # Agent状态更新事件
                elif event.type == "agent_updated_stream_event":
                    print(f"\n[Agent状态更新]: {event.new_agent.name}")
                # 运行项流事件
                elif event.type == "run_item_stream_event":
                    if event.item.type == "tool_call_item":
                        print("\n-- 调用工具 --")
                    elif event.item.type == "tool_call_output_item":
                        # 工具输出通常是给模型看的，内容可能很长，这里只打印提示
                        print(f"\n-- 工具输出已生成 --")
                    elif event.item.type == "message_output_item":
                        # 从 message_output_item 中提取完整的最终响应
                        final_response_text = ItemHelpers.text_message_output(
                            event.item
                        )
                    else:
                        # 忽略其他类型的运行项事件
                        pass
                else:
                    # 打印其他未明确处理的事件类型，用于调试
                    print(f"\n[其他事件]: {event.type}")

            print(f"\n--- Agent分析完成 for '{query}' ---")

            # 返回分析报告文本，用于后续如发送飞书通知等操作
            # 第一个返回值保持与旧函数兼容，但在此实现中不使用
            return "", final_response_text

        except Exception as e:
            import traceback

            print(f"\nAgent执行出错: {e}")
            traceback.print_exc()
            return None, None

    # 在同步函数中运行异步分析过程
    return asyncio.run(_run_analysis())


def analyze_and_report_search_query(query: str, city: str = "杭州"):
    """
    搜索鲜沐产品，分析搜索结果，并发送飞书通知。

    Args:
        query (str): 搜索关键词。
        city (str): 搜索城市。
    """
    # 搜索鲜沐产品
    searched_items = search_xianmu_product(query=query, city=city, page_size=100)
    # 如果搜索结果为空，则跳过处理
    if not searched_items:
        logging.error(f"搜索结果为空,跳过处理: {query}")
        return
    # 将搜索结果转换为pandas DataFrame
    searched_items_df = pd.DataFrame(searched_items)
    # 提取'sku_id', 'spu_name', 'brand', 'properties', 'category', 'area_price'列，并将DataFrame写入CSV文件，包含索引
    csv_content = searched_items_df[
        [
            "sku_id",
            "spu_name",
            "brand",
            "properties",
            "category",
            "category_id",
            "area_price",
            "monthly_sales",
            "sufficient_stock",
        ]
    ].to_csv(index=True)
    final_reasoning_content, final_content = analytics_with_r1(
        query=query, csv_content=csv_content, low_perf_df=low_perf_df
    )


import argparse

# 设置命令行参数解析，以允许指定分析的查询数量
parser = argparse.ArgumentParser(description="在线关键词表现监控脚本")
parser.add_argument(
    "--n_to_run", type=int, default=20, help="需要通过LLM分析效果的关键词数量"
)
args = parser.parse_args()

# 调用函数，对指定数量的query在"杭州"进行搜索分析
print(f"准备分析 {args.n_to_run} 个低效搜索词...")
for index, row in low_perf_df.head(args.n_to_run).iterrows():
    query = row["query"]
    if query not in analyzed_queries:
        analyze_and_report_search_query(query=query)
        analyzed_queries.add(query)
    else:
        print(f"query可能存在类目预测结果或已分析过: {query}")

today_str = datetime.now().strftime("%Y-%m-%d")
# 如果有同义词建议，则发送通知
if total_final_synonyms_result:
    send_feishu_notice_with_title_and_content(
        title=f"{today_str}搜索同义词建议",
        markdown_str="\n".join(total_final_synonyms_result),
    )

# 如果有类目权重更新建议，则发送通知
if total_final_category_weights_result:
    send_feishu_notice_with_title_and_content(
        title=f"{today_str}搜索类目权重更新建议",
        markdown_str="\n".join(total_final_category_weights_result),
    )


top_query_df["user_click_ratio"] = (
    top_query_df["clicked_user_cnt"] / top_query_df["searched_users"]
)

top_query_df[
    (top_query_df["user_click_ratio"] > 0) & (top_query_df["clicked_user_cnt"] > 5)
]["user_click_ratio"].quantile([0.05, 0.1, 0.2, 0.25, 0.5, 0.75, 0.9])


low_click_performence_df_cache_path = os.path.join(
    CACHE_DIR, "low_click_performence_df.csv"
)

print("计算 low_click_performence_df...")
low_click_performence_df = top_query_df.set_index(["query", "日期标签"]).unstack(
    fill_value=0
)

low_click_performence_df = low_click_performence_df.sort_values(
    by=[("searched_users", "最近7天")], ascending=[False]
)
# 筛选出low_perf_df中存在的query
low_click_performence_df = low_click_performence_df[
    low_click_performence_df.index.get_level_values("query").isin(low_perf_df["query"])
]

# 构造markdown表格
markdown_tables = []
for i in range(0, len(low_click_performence_df), 5):
    df_chunk = low_click_performence_df.iloc[i : i + 5]
    table_str = "| 搜索词 | 最近7天搜索人数 | 7天前搜索人数 | 用户点击率（最近7天 vs 7天前） | 平均点击位置（最近7天 vs 7天前）|\n"
    table_str += "|---|---|---|---|---|\n"
    for index, row in df_chunk.iterrows():
        query = index
        searched_users_recent = row[("searched_users", "最近7天")]
        searched_users_past = row[("searched_users", "7天以前")]
        user_click_ratio_recent = row[("user_click_ratio", "最近7天")]
        user_click_ratio_past = row[("user_click_ratio", "7天以前")]
        avg_click_index_recent = row[("avg_click_index", "最近7天")]
        avg_click_index_past = row[("avg_click_index", "7天以前")]
        table_str += f"| {query} | {searched_users_recent} | {searched_users_past} | {user_click_ratio_recent:.2f} vs {user_click_ratio_past:.2f} | {avg_click_index_recent:.2f} vs {avg_click_index_past:.2f} |\n"
    markdown_tables.append(table_str)

final_markdown_output = "\n---\n".join(markdown_tables)
print(final_markdown_output)


grouped_search_cnt = top_query_df.groupby("日期标签")["search_cnt"].sum()
recent_7_days_cnt = grouped_search_cnt["最近7天"]
previous_7_days_cnt = grouped_search_cnt["7天以前"]
diff_ratio = (recent_7_days_cnt) / previous_7_days_cnt

print(f"最近7天 search_cnt: {recent_7_days_cnt}")
print(f"7天以前 search_cnt: {previous_7_days_cnt}")
total_diff = f"最近7天相比7天以前的搜索总次数增幅: {diff_ratio:.2%}"
print(total_diff)

diff_ratio_threshold = (
    diff_ratio + 0.2
)  # 如果比大盘整体上涨了20%，则说明这是新的query。
searched_times_theshold = 70
searched_users_theshold = 14

print("计算 filtered_df_result...")
filtered_df = top_query_df.set_index(["query", "日期标签"]).unstack(fill_value=0)
filtered_df["recent_previous_ratio"] = (
    filtered_df[("search_cnt", "最近7天")] / filtered_df[("search_cnt", "7天以前")]
)
filtered_df_result = filtered_df[
    (filtered_df["recent_previous_ratio"] > diff_ratio_threshold)
    & (filtered_df[("search_cnt", "最近7天")] > searched_times_theshold)
    & (filtered_df[("searched_users", "最近7天")] > searched_users_theshold)
].reset_index()

print(f"大盘增幅阈值: {diff_ratio_threshold:.2%}")
print(f"最近7天search_cnt 相比 7天前 增幅超过阈值的query:")
# filtered_df_result.head(20)

# print(filtered_df_result.columns)

filtered_df_result[("用户点击率", "7天以前")] = (
    filtered_df_result[("clicked_user_cnt", "7天以前")]
    / filtered_df_result[("searched_users", "7天以前")]
).round(2)
filtered_df_result[("用户点击率", "最近7天")] = (
    filtered_df_result[("clicked_user_cnt", "最近7天")]
    / filtered_df_result[("searched_users", "最近7天")]
).round(2)

filtered_df_result = filtered_df_result.sort_values(
    by=[("searched_users", "最近7天"), ("recent_previous_ratio", "")],
    ascending=[False, False],
)

filtered_df_result = filtered_df_result.head(20)
print(f"{filtered_df_result}")

markdown_table_rows = []
rows_per_table = 5

for i in range(0, len(filtered_df_result), rows_per_table):
    table_rows = [
        "| 搜索词 | 最近7天搜索人数 | 搜索人次增幅 | 7天前搜索人数 | 最近7天搜索人次 | 7天前搜索人次 | 用户点击率 (最近7天vs7天以前) |",
        "|---|---|---|---|---|---|---|",
    ]
    for idx, row in filtered_df_result.iloc[i : i + rows_per_table].iterrows():
        table_rows.append(
            f"| {row[('query', '')]} "
            f"| {row[('searched_users', '最近7天')]} "
            f"| {100.0*(row[('recent_previous_ratio', '')]-1):.2f} %"
            f"| {row[('searched_users', '7天以前')]} "
            f"| {row[('search_cnt', '最近7天')]} "
            f"| {row[('search_cnt', '7天以前')]} "
            f"| {row[('用户点击率', '最近7天')]} vs {row[('用户点击率', '7天以前')]} |"
        )
    markdown_table_rows.append("\n".join(table_rows))

markdown_str = "\n---\n".join(markdown_table_rows)  # join tables with a blank line
print(markdown_str)


send_feishu_notice_with_title_and_content(
    title=f"过去7天搜索量增长最快的{len(filtered_df_result)}个词",
    markdown_str=markdown_str,
)

send_feishu_notice_with_title_and_content(
    markdown_str=final_markdown_output,
    title=f"过去7天点击率最差的{len(low_click_performence_df)}个高频搜索词",
)
