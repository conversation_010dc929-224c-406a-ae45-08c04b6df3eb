# 🎯 AI搜索质量评测系统使用指南

## 快速开始

### 📋 必填配置

1. 设置环境变量：
```bash
cp evaluate_search_env.example .env
# 编辑.env文件填入您的API密钥
```

2. 启动搜索服务：
```bash
python3 app.py  # 确保服务运行在端口5800
```

### 🚀 运⾏评测

#### 基础用法:
```bash
# 使用默认CTR阈值0.7
python3 evaluate_search_queries.py

# 使用自定义CTR阈值
python3 evaluate_search_queries.py --user-ctr 0.5

# 使用非常严格的CTR阈值
python3 evaluate_search_queries.py --user-ctr 0.2

# 启用调试模式
python3 evaluate_search_queries.py --user-ctr 0.5 --debug
```

#### 使用启动脚本:
```bash
# 使用默认阈值
./run_evaluation.sh

# 带参数
./run_evaluation.sh 0.6
```

## 📊 参数说明

| 参数 | 默认值 | 说明 | 示例 |
|---|---|---|---|
| `--user-ctr` | 0.7 | 筛选用户CTR的上限阈值 | `--user-ctr 0.8` (分析CTR<80%的词) |
| `--debug` | False | 启用调试日志输出 | `--debug` |

## 🔍 CTR阈值选择建议

| 阈值 | 适用场景 | 期望查询词数量 |
|---|---|---|
| `0.8` | 极高效查询词 | 较少 |
| `0.5-0.7` | 中等表现查询词 *(推荐)* | 中度 |
| `0.3-0.5` | 低CTR待优化查询词 | 较多 |
| `0.1-0.3` | 严重低效查询词 | 很多 |

## 💡 使用技巧

### 🔎 实战案例

1. **分析低CTR优化机会**
```bash
python3 evaluate_search_queries.py --user-ctr 0.3
```

2. **高CTR基准对照**
```bash
python3 evaluate_search_queries.py --user-ctr 0.9
```

3. **制定优化策略**
```bash
# 先运行不同阈值进行对比
python3 evaluate_search_queries.py --user-ctr 0.4
python3 evaluate_search_queries.py --user-ctr 0.6
python3 evaluate_search_queries.py --user-ctr 0.8
```

### 📈 报告解读

- **日志中会出现**: `筛选用户CTR阈值: 0.350`
- **报告标题会包含**: 阈值信息
- **查询统计会显示**: 符合阈值条件的具体查询词数量

## 🐛 常见问题

**Q: 指定阈值后查询词为0怎么办？**
```bash
# 检查当前CTR分布
python test_complete.py
```

**Q: 生成报告文件名太多？**
```bash
# 使用简短阈值
python3 evaluate_search_queries.py --user-ctr 0.5
```

**Q: 如何选择最佳阈值？**
- 从`--user-ctr 0.1`开始广泛测试
- 逐步提高阈值到`--user-ctr 0.9`
- 找到查询词数量200-800的区间最佳