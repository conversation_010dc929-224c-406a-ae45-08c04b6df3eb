{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "import requests\n", "\n", "\n", "url = (\n", "    \"https://open.feishu.cn/open-apis/bot/v2/hook/aaf777eb-819c-4701-bc4b-893bf02addfc\"\n", ")\n", "\n", "\n", "def send_feishu_notice_with_title_and_content(\n", "    markdown_str: str,\n", "    feishu_url=url,\n", "    title=\"\",\n", "    error=False,\n", "):\n", "    feishu_message_obj = {\n", "        \"schema\": \"2.0\",\n", "        \"header\": {\n", "            \"template\": \"red\" if error else \"blue\",\n", "            \"title\": {\n", "                \"content\": f\"**{title}**\",\n", "                \"tag\": \"lark_md\",\n", "            },\n", "        },\n", "        \"body\": {\n", "            \"elements\": [\n", "                {\n", "                    \"tag\": \"markdown\",\n", "                    \"content\": markdown_str,\n", "                },\n", "                {\n", "                    \"tag\": \"markdown\",\n", "                    \"content\": f\"> 数据生成于:{datetime.now().strftime('%Y-%m-%d %H:%M')}\\n> \",\n", "                },\n", "            ]\n", "        },\n", "    }\n", "    headers = {\"Content-Type\": \"application/json\"}\n", "    data = {\"msg_type\": \"interactive\", \"card\": feishu_message_obj}\n", "    feishu_result = requests.post(\n", "        url=feishu_url, json=data, headers=headers, verify=False, proxies={}\n", "    ).json()\n", "    return feishu_result"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-05 10:53:24 - INFO - Instance ID: 20250805025313985gkkfx3iuoio\n", "  Log view: http://logview.odps.aliyun.com/logview/?h=http://service.cn-hangzhou.maxcompute.aliyun.com/api&p=summerfarm_ds_dev&i=20250805025313985gkkfx3iuoio&token=SXo4MkdsL2FDeE8raERxWXQ3Wkdmc2owRFQwPSxPRFBTX09CTzpwNF8yMTMyMTQ5NzU2NjcwNjcwMjQsMTc1Njk1NDQwNCx7IlN0YXRlbWVudCI6W3siQWN0aW9uIjpbIm9kcHM6UmVhZCJdLCJFZmZlY3QiOiJBbGxvdyIsIlJlc291cmNlIjpbImFjczpvZHBzOio6cHJvamVjdHMvc3VtbWVyZmFybV9kc19kZXYvaW5zdGFuY2VzLzIwMjUwODA1MDI1MzEzOTg1Z2trZngzaXVvaW8iXX1dLCJWZXJzaW9uIjoiMSJ9\n", "2025-08-05 10:53:36 - INFO - 20250805025313985gkkfx3iuoio 2025-08-05 10:53:36 M1_job_0:0/1/1[100%]\tR2_1_job_0:0/1/1[100%]\t\n", "2025-08-05 10:53:38 - INFO - Tunnel session created: <InstanceDownloadSession id=202508051053373228f60b0b827f48 project_name=summerfarm_ds_dev instance_id=20250805025313985gkkfx3iuoio>\n", "2025-08-05 10:53:39 - INFO - sql:\n", "\n", "SELECT  cust_id\n", "        ,count(distinct ds) 搜索天数\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'impression' and CAST(idx AS BIGINT)<=1 THEN CONCAT(time,query) END) AS search_cnt\n", "        ,COUNT(distinct query) AS query_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN CONCAT(time,query,idx) END) AS clicked_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,query,idx) END) AS impression_cnt\n", "FROM    summerfarm_tech.app_log_search_detail_di\n", "WHERE   ds BETWEEN '20250628' and '20250729'\n", "GROUP BY cust_id\n", ";\n", "\n", "columns:Index(['cust_id', '搜索天数', 'search_cnt', 'query_cnt', 'clicked_cnt',\n", "       'impression_cnt'],\n", "      dtype='object')\n"]}], "source": ["from datetime import datetime, timedelta\n", "import sys\n", "\n", "sys.path.append(\"../\")\n", "\n", "# category_prediction_df 获取\n", "from odps_client import get_odps_sql_result_as_df\n", "\n", "\n", "last_n_days = 30\n", "last_half_n_days = int(last_n_days / 2)\n", "ds_yesterday = (datetime.now() - timedelta(days=1)).strftime(\"%Y%m%d\")\n", "last_n_days_ago = (datetime.now() - timedelta(days=last_n_days)).strftime(\"%Y%m%d\")\n", "last_half_n_days_ago = (datetime.now() - <PERSON><PERSON><PERSON>(last_half_n_days)).strftime(\"%Y%m%d\")\n", "\n", "# 此处为了还原，写死日期范围：\n", "last_n_days_ago = \"20250628\"\n", "ds_yesterday = \"20250729\"\n", "\n", "user_static = f\"\"\"\n", "SELECT  cust_id\n", "        ,count(distinct ds) 搜索天数\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'impression' and CAST(idx AS BIGINT)<=1 THEN CONCAT(time,query) END) AS search_cnt\n", "        ,COUNT(distinct query) AS query_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN CONCAT(time,query,idx) END) AS clicked_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,query,idx) END) AS impression_cnt\n", "FROM    summerfarm_tech.app_log_search_detail_di\n", "WHERE   ds BETWEEN '{last_n_days_ago}' and '{ds_yesterday}'\n", "GROUP BY cust_id\n", ";\n", "\"\"\"\n", "\n", "user_static_df = get_odps_sql_result_as_df(sql=user_static)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cust_id</th>\n", "      <th>搜索天数</th>\n", "      <th>search_cnt</th>\n", "      <th>query_cnt</th>\n", "      <th>clicked_cnt</th>\n", "      <th>impression_cnt</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>56670.000000</td>\n", "      <td>56671.000000</td>\n", "      <td>56671.000000</td>\n", "      <td>56671.000000</td>\n", "      <td>56671.000000</td>\n", "      <td>56671.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>359425.709546</td>\n", "      <td>4.387235</td>\n", "      <td>11.693759</td>\n", "      <td>6.945245</td>\n", "      <td>10.634822</td>\n", "      <td>101.369625</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>168107.634820</td>\n", "      <td>4.876399</td>\n", "      <td>24.005382</td>\n", "      <td>11.457263</td>\n", "      <td>24.449682</td>\n", "      <td>188.698854</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>2.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>232080.250000</td>\n", "      <td>1.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>1.000000</td>\n", "      <td>14.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>404927.000000</td>\n", "      <td>2.000000</td>\n", "      <td>5.000000</td>\n", "      <td>4.000000</td>\n", "      <td>4.000000</td>\n", "      <td>40.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>508994.500000</td>\n", "      <td>6.000000</td>\n", "      <td>12.000000</td>\n", "      <td>8.000000</td>\n", "      <td>11.000000</td>\n", "      <td>111.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>562050.000000</td>\n", "      <td>32.000000</td>\n", "      <td>801.000000</td>\n", "      <td>375.000000</td>\n", "      <td>1015.000000</td>\n", "      <td>4992.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             cust_id          搜索天数    search_cnt     query_cnt   clicked_cnt  \\\n", "count   56670.000000  56671.000000  56671.000000  56671.000000  56671.000000   \n", "mean   359425.709546      4.387235     11.693759      6.945245     10.634822   \n", "std    168107.634820      4.876399     24.005382     11.457263     24.449682   \n", "min         2.000000      1.000000      0.000000      1.000000      0.000000   \n", "25%    232080.250000      1.000000      2.000000      2.000000      1.000000   \n", "50%    404927.000000      2.000000      5.000000      4.000000      4.000000   \n", "75%    508994.500000      6.000000     12.000000      8.000000     11.000000   \n", "max    562050.000000     32.000000    801.000000    375.000000   1015.000000   \n", "\n", "       impression_cnt  \n", "count    56671.000000  \n", "mean       101.369625  \n", "std        188.698854  \n", "min          0.000000  \n", "25%         14.000000  \n", "50%         40.000000  \n", "75%        111.000000  \n", "max       4992.000000  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["user_static_df.describe()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.25     2.0\n", "0.50     5.0\n", "0.75    12.0\n", "0.90    28.0\n", "0.95    44.0\n", "0.99    99.3\n", "Name: search_cnt, dtype: float64\n", "搜索次数超过:5 的用户数:28872, 人数占比:50.95%, 搜索次数占比:91.53%, 点击次数占比:92.35%\n", "搜索次数超过:10 的用户数:17694, 人数占比:31.22%, 搜索次数占比:80.32%, 点击次数占比:81.8%\n", "搜索次数超过:15 的用户数:12210, 人数占比:21.55%, 搜索次数占比:70.58%, 点击次数占比:72.37%\n", "搜索次数超过:20 的用户数:8936, 人数占比:15.77%, 搜索次数占比:62.25%, 点击次数占比:64.21%\n", "搜索次数超过:25 的用户数:6768, 人数占比:11.94%, 搜索次数占比:55.11%, 点击次数占比:57.11%\n", "搜索次数超过:30 的用户数:5238, 人数占比:9.24%, 搜索次数占比:48.91%, 点击次数占比:50.82%\n", "搜索次数超过:35 的用户数:4137, 人数占比:7.3%, 搜索次数占比:43.62%, 点击次数占比:45.48%\n", "搜索次数超过:40 的用户数:3346, 人数占比:5.9%, 搜索次数占比:39.21%, 点击次数占比:41.01%\n", "搜索次数超过:45 的用户数:2732, 人数占比:4.82%, 搜索次数占比:35.33%, 点击次数占比:37.1%\n", "搜索次数超过:50 的用户数:2278, 人数占比:4.02%, 搜索次数占比:32.12%, 点击次数占比:33.76%\n", "搜索次数超过:55 的用户数:1890, 人数占比:3.34%, 搜索次数占比:29.08%, 点击次数占比:30.66%\n"]}], "source": ["print(user_static_df[\"search_cnt\"].quantile([0.25, 0.5, 0.75, 0.9, 0.95, 0.99]))\n", "total_user_search_cnt = user_static_df[\"search_cnt\"].sum()\n", "for search_cnt in range(5, 60, 5):\n", "    _df = user_static_df[user_static_df[\"search_cnt\"] >= search_cnt]\n", "    print(\n", "        f\"搜索次数超过:{search_cnt} 的用户数:{_df.shape[0]}, 人数占比:{round(_df.shape[0]*100.0/user_static_df.shape[0],2)}%, 搜索次数占比:{round(100.0*_df['search_cnt'].sum()/total_user_search_cnt,2)}%, 点击次数占比:{round(100.0*_df['clicked_cnt'].sum()/user_static_df['clicked_cnt'].sum(),2)}%\"\n", "    )"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>cust_id</th>\n", "      <th>搜索天数</th>\n", "      <th>search_cnt</th>\n", "      <th>query_cnt</th>\n", "      <th>clicked_cnt</th>\n", "      <th>impression_cnt</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>8389.000000</td>\n", "      <td>8390.000000</td>\n", "      <td>8390.000000</td>\n", "      <td>8390.000000</td>\n", "      <td>8390.0</td>\n", "      <td>8390.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>380022.188580</td>\n", "      <td>1.239928</td>\n", "      <td>1.611323</td>\n", "      <td>1.570799</td>\n", "      <td>0.0</td>\n", "      <td>16.393683</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>164924.744872</td>\n", "      <td>0.635129</td>\n", "      <td>1.333948</td>\n", "      <td>1.069157</td>\n", "      <td>0.0</td>\n", "      <td>19.785965</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>26.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>261526.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.0</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>432936.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.0</td>\n", "      <td>10.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>525368.000000</td>\n", "      <td>1.000000</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "      <td>0.0</td>\n", "      <td>20.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>562042.000000</td>\n", "      <td>19.000000</td>\n", "      <td>33.000000</td>\n", "      <td>28.000000</td>\n", "      <td>0.0</td>\n", "      <td>322.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             cust_id         搜索天数   search_cnt    query_cnt  clicked_cnt  \\\n", "count    8389.000000  8390.000000  8390.000000  8390.000000       8390.0   \n", "mean   380022.188580     1.239928     1.611323     1.570799          0.0   \n", "std    164924.744872     0.635129     1.333948     1.069157          0.0   \n", "min        26.000000     1.000000     0.000000     1.000000          0.0   \n", "25%    261526.000000     1.000000     1.000000     1.000000          0.0   \n", "50%    432936.000000     1.000000     1.000000     1.000000          0.0   \n", "75%    525368.000000     1.000000     2.000000     2.000000          0.0   \n", "max    562042.000000    19.000000    33.000000    28.000000          0.0   \n", "\n", "       impression_cnt  \n", "count     8390.000000  \n", "mean        16.393683  \n", "std         19.785965  \n", "min          0.000000  \n", "25%          4.000000  \n", "50%         10.000000  \n", "75%         20.000000  \n", "max        322.000000  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["user_static_df[user_static_df[\"clicked_cnt\"]<=0].describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-05 10:53:51 - INFO - Instance ID: 20250805025339905gh5u1aiodpw5\n", "  Log view: http://logview.odps.aliyun.com/logview/?h=http://service.cn-hangzhou.maxcompute.aliyun.com/api&p=summerfarm_ds_dev&i=20250805025339905gh5u1aiodpw5&token=eC80S0sxclN2T2dleWZ4bnJhMHRXNlNCbk9BPSxPRFBTX09CTzpwNF8yMTMyMTQ5NzU2NjcwNjcwMjQsMTc1Njk1NDQzMSx7IlN0YXRlbWVudCI6W3siQWN0aW9uIjpbIm9kcHM6UmVhZCJdLCJFZmZlY3QiOiJBbGxvdyIsIlJlc291cmNlIjpbImFjczpvZHBzOio6cHJvamVjdHMvc3VtbWVyZmFybV9kc19kZXYvaW5zdGFuY2VzLzIwMjUwODA1MDI1MzM5OTA1Z2g1dTFhaW9kcHc1Il19XSwiVmVyc2lvbiI6IjEifQ==\n", "2025-08-05 10:54:08 - INFO - 20250805025339905gh5u1aiodpw5 2025-08-05 10:54:08 M1_job_0:0/1/1[100%]\tR2_1_job_0:0/1/1[100%]\t\n", "2025-08-05 10:54:09 - INFO - Tunnel session created: <InstanceDownloadSession id=202508051054095330f60b0b831817 project_name=summerfarm_ds_dev instance_id=20250805025339905gh5u1aiodpw5>\n", "2025-08-05 10:54:10 - INFO - sql:\n", "\n", "SELECT  query\n", "        ,COUNT(distinct cust_id) as searched_users\n", "        ,SUM(CASE WHEN envent_type = 'impression' and idx='0' THEN 1 ELSE 0 END) as searched_times\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,query,idx,cust_id) END) AS impression_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN CONCAT(time,query,idx,cust_id) END) AS click_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN CONCAT(time,query,idx,cust_id) END)*1.00/COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,query,idx,cust_id) END) as sku_ctr\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN cust_id END) AS clicked_user_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN cust_id END)*1.00/COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN cust_id END) as user_ctr\n", "        ,PERCENTILE(CASE WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index\n", "        ,PERCENTILE(CASE WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index\n", "        ,PERCENTILE(CASE WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index\n", "FROM    summerfarm_tech.app_log_search_detail_di\n", "WHERE   ds BETWEEN '20250628' and '20250729'\n", "GROUP BY query\n", ";\n", "\n", "columns:Index(['query', 'searched_users', 'searched_times', 'impression_cnt',\n", "       'click_cnt', 'sku_ctr', 'clicked_user_cnt', 'user_ctr',\n", "       'p50_click_index', 'p75_click_index', 'p90_click_index'],\n", "      dtype='object')\n"]}], "source": ["top_query_label = f\"\"\"\n", "SELECT  query\n", "        ,COUNT(distinct cust_id) as searched_users\n", "        ,SUM(CASE WHEN envent_type = 'impression' and idx='0' THEN 1 ELSE 0 END) as searched_times\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,query,idx,cust_id) END) AS impression_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN CONCAT(time,query,idx,cust_id) END) AS click_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN CONCAT(time,query,idx,cust_id) END)*1.00/COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,query,idx,cust_id) END) as sku_ctr\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN cust_id END) AS clicked_user_cnt\n", "        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN cust_id END)*1.00/COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN cust_id END) as user_ctr\n", "        ,PERCENTILE(CASE WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index\n", "        ,PERCENTILE(CASE WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index\n", "        ,PERCENTILE(CASE WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index\n", "FROM    summerfarm_tech.app_log_search_detail_di\n", "WHERE   ds BETWEEN '{last_n_days_ago}' and '{ds_yesterday}'\n", "GROUP BY query\n", "ORDER BY searched_users DESC, searched_times DESC\n", "\"\"\"\n", "\n", "top_query_labeled_df = get_odps_sql_result_as_df(sql=top_query_label)\n", "top_query_labeled_df[\"searched_times\"] = top_query_labeled_df[\"searched_times\"].apply(\n", "    lambda x: x if x is not None and x > 0 else 1\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>25540.000000</td>\n", "      <td>25540.000000</td>\n", "      <td>25540.000000</td>\n", "      <td>25540.000000</td>\n", "      <td>23467.000000</td>\n", "      <td>25540.000000</td>\n", "      <td>25540.000000</td>\n", "      <td>11878.000000</td>\n", "      <td>11878.000000</td>\n", "      <td>11878.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>15.409789</td>\n", "      <td>25.856186</td>\n", "      <td>224.925646</td>\n", "      <td>23.597729</td>\n", "      <td>0.071465</td>\n", "      <td>9.950235</td>\n", "      <td>0.357694</td>\n", "      <td>5.944140</td>\n", "      <td>7.640870</td>\n", "      <td>9.626065</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>152.186819</td>\n", "      <td>344.710015</td>\n", "      <td>3280.697218</td>\n", "      <td>368.103507</td>\n", "      <td>0.120516</td>\n", "      <td>116.074075</td>\n", "      <td>0.421561</td>\n", "      <td>11.584597</td>\n", "      <td>13.366936</td>\n", "      <td>16.071603</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>4.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "      <td>11.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.002833</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>2.000000</td>\n", "      <td>3.000000</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "      <td>34.000000</td>\n", "      <td>2.000000</td>\n", "      <td>0.107143</td>\n", "      <td>1.000000</td>\n", "      <td>0.800000</td>\n", "      <td>6.500000</td>\n", "      <td>9.000000</td>\n", "      <td>12.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>8693.000000</td>\n", "      <td>25697.000000</td>\n", "      <td>258447.000000</td>\n", "      <td>31172.000000</td>\n", "      <td>3.240000</td>\n", "      <td>7107.000000</td>\n", "      <td>1.000000</td>\n", "      <td>321.000000</td>\n", "      <td>321.000000</td>\n", "      <td>321.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       searched_users  searched_times  impression_cnt     click_cnt  \\\n", "count    25540.000000    25540.000000    25540.000000  25540.000000   \n", "mean        15.409789       25.856186      224.925646     23.597729   \n", "std        152.186819      344.710015     3280.697218    368.103507   \n", "min          1.000000        1.000000        0.000000      0.000000   \n", "25%          1.000000        1.000000        4.000000      0.000000   \n", "50%          1.000000        1.000000       11.000000      0.000000   \n", "75%          3.000000        3.000000       34.000000      2.000000   \n", "max       8693.000000    25697.000000   258447.000000  31172.000000   \n", "\n", "            sku_ctr  clicked_user_cnt      user_ctr  p50_click_index  \\\n", "count  23467.000000      25540.000000  25540.000000     11878.000000   \n", "mean       0.071465          9.950235      0.357694         5.944140   \n", "std        0.120516        116.074075      0.421561        11.584597   \n", "min        0.000000          0.000000      0.000000         0.000000   \n", "25%        0.000000          0.000000      0.000000         0.000000   \n", "50%        0.002833          0.000000      0.000000         2.000000   \n", "75%        0.107143          1.000000      0.800000         6.500000   \n", "max        3.240000       7107.000000      1.000000       321.000000   \n", "\n", "       p75_click_index  p90_click_index  \n", "count     11878.000000     11878.000000  \n", "mean          7.640870         9.626065  \n", "std          13.366936        16.071603  \n", "min           0.000000         0.000000  \n", "25%           1.000000         1.000000  \n", "50%           3.000000         4.000000  \n", "75%           9.000000        12.000000  \n", "max         321.000000       321.000000  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["top_query_labeled_df.describe()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/b3/9hcz86fx1_z_8m4121xwbs2h0000gn/T/ipykernel_39537/3633952487.py:2: UserWarning: Boolean Series key will be reindexed to match DataFrame index.\n", "  top_user_ctr_lower_than_0_7_df = top_query_labeled_df.head(400)[\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>searched_users</th>\n", "      <th>searched_times</th>\n", "      <th>impression_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>sku_ctr</th>\n", "      <th>clicked_user_cnt</th>\n", "      <th>user_ctr</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>182.000000</td>\n", "      <td>182.000000</td>\n", "      <td>182.000000</td>\n", "      <td>182.000000</td>\n", "      <td>182.000000</td>\n", "      <td>182.000000</td>\n", "      <td>182.000000</td>\n", "      <td>182.000000</td>\n", "      <td>182.000000</td>\n", "      <td>182.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>485.093407</td>\n", "      <td>798.906593</td>\n", "      <td>8055.818681</td>\n", "      <td>627.758242</td>\n", "      <td>0.087484</td>\n", "      <td>282.060440</td>\n", "      <td>0.552173</td>\n", "      <td>3.046703</td>\n", "      <td>7.483516</td>\n", "      <td>14.863187</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>624.132164</td>\n", "      <td>1258.674914</td>\n", "      <td>16347.039592</td>\n", "      <td>1110.851824</td>\n", "      <td>0.043639</td>\n", "      <td>408.441793</td>\n", "      <td>0.151769</td>\n", "      <td>5.386115</td>\n", "      <td>10.259398</td>\n", "      <td>16.333737</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>147.000000</td>\n", "      <td>159.000000</td>\n", "      <td>773.000000</td>\n", "      <td>17.000000</td>\n", "      <td>0.008498</td>\n", "      <td>13.000000</td>\n", "      <td>0.059091</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>183.500000</td>\n", "      <td>256.500000</td>\n", "      <td>2283.750000</td>\n", "      <td>180.750000</td>\n", "      <td>0.054706</td>\n", "      <td>99.500000</td>\n", "      <td>0.495250</td>\n", "      <td>1.000000</td>\n", "      <td>2.000000</td>\n", "      <td>4.275000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>270.500000</td>\n", "      <td>402.500000</td>\n", "      <td>3862.000000</td>\n", "      <td>299.500000</td>\n", "      <td>0.085817</td>\n", "      <td>140.500000</td>\n", "      <td>0.613532</td>\n", "      <td>1.000000</td>\n", "      <td>4.000000</td>\n", "      <td>9.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>533.500000</td>\n", "      <td>770.250000</td>\n", "      <td>7052.750000</td>\n", "      <td>627.000000</td>\n", "      <td>0.111412</td>\n", "      <td>285.500000</td>\n", "      <td>0.663648</td>\n", "      <td>3.000000</td>\n", "      <td>9.000000</td>\n", "      <td>19.925000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>4966.000000</td>\n", "      <td>11062.000000</td>\n", "      <td>172095.000000</td>\n", "      <td>10072.000000</td>\n", "      <td>0.230593</td>\n", "      <td>3427.000000</td>\n", "      <td>0.698324</td>\n", "      <td>38.000000</td>\n", "      <td>69.250000</td>\n", "      <td>105.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       searched_users  searched_times  impression_cnt     click_cnt  \\\n", "count      182.000000      182.000000      182.000000    182.000000   \n", "mean       485.093407      798.906593     8055.818681    627.758242   \n", "std        624.132164     1258.674914    16347.039592   1110.851824   \n", "min        147.000000      159.000000      773.000000     17.000000   \n", "25%        183.500000      256.500000     2283.750000    180.750000   \n", "50%        270.500000      402.500000     3862.000000    299.500000   \n", "75%        533.500000      770.250000     7052.750000    627.000000   \n", "max       4966.000000    11062.000000   172095.000000  10072.000000   \n", "\n", "          sku_ctr  clicked_user_cnt    user_ctr  p50_click_index  \\\n", "count  182.000000        182.000000  182.000000       182.000000   \n", "mean     0.087484        282.060440    0.552173         3.046703   \n", "std      0.043639        408.441793    0.151769         5.386115   \n", "min      0.008498         13.000000    0.059091         0.000000   \n", "25%      0.054706         99.500000    0.495250         1.000000   \n", "50%      0.085817        140.500000    0.613532         1.000000   \n", "75%      0.111412        285.500000    0.663648         3.000000   \n", "max      0.230593       3427.000000    0.698324        38.000000   \n", "\n", "       p75_click_index  p90_click_index  \n", "count       182.000000       182.000000  \n", "mean          7.483516        14.863187  \n", "std          10.259398        16.333737  \n", "min           0.000000         0.000000  \n", "25%           2.000000         4.275000  \n", "50%           4.000000         9.000000  \n", "75%           9.000000        19.925000  \n", "max          69.250000       105.000000  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["top_query_labeled_df=top_query_labeled_df.sort_values(by=[\"searched_users\",\"searched_times\"], ascending=[False,False])\n", "top_user_ctr_lower_than_0_7_df = top_query_labeled_df.head(400)[\n", "    top_query_labeled_df[\"user_ctr\"] < 0.7\n", "]\n", "top_user_ctr_lower_than_0_7_df[\"link\"]=top_user_ctr_lower_than_0_7_df['query'].apply(lambda x: f\"http://xianmuai.s7.tunnelfrp.com/search-arena/?query={x}&page_size=40&city=杭州\")\n", "top_user_ctr_lower_than_0_7_df.to_csv(f\"./top400搜索词中user_ctr低于0.7_{datetime.now().strftime('%Y%m%d')}.csv\",index=False)\n", "top_user_ctr_lower_than_0_7_df.describe()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["top_query_labeled_df[\"searched_times\"].fillna(\n", "    1, inplace=True\n", ")  # 默认肯定有至少1次搜索, inplace=True to modify the DataFrame directly\n", "top_query_labeled_df[\"searched_times\"] = top_query_labeled_df[\"searched_times\"].astype(\n", "    int\n", ")\n", "\n", "print(\n", "    top_query_labeled_df[\"searched_users\"].quantile([0.1, 0.25, 0.5, 0.75, 0.90, 0.95])\n", ")\n", "print(\n", "    top_query_labeled_df[\"searched_times\"].quantile([0.1, 0.25, 0.5, 0.75, 0.90, 0.95])\n", ")\n", "\n", "import matplotlib.pyplot as plt\n", "import numpy as np  # 导入 numpy\n", "\n", "# 设置中文字体显示 - Mac系统\n", "plt.rcParams[\"font.sans-serif\"] = [\n", "    \"Arial Unicode MS\"\n", "]  # Mac系统使用Arial Unicode MS字体\n", "plt.rcParams[\"axes.unicode_minus\"] = False  # 用来正常显示负号\n", "\n", "quantiles = [5, 25, 50, 75, 90, 95, 99]  # 修改分位数值\n", "fig, axes = plt.subplots(2, 1, figsize=(12,8))\n", "\n", "# Cumulative distribution of searched_users\n", "searched_users_counts = top_query_labeled_df[\"searched_users\"].sort_values()\n", "cumulative_users = np.arange(1, len(searched_users_counts) + 1) / len(\n", "    searched_users_counts\n", ")  # 使用 numpy 创建累积分布\n", "total_users = cumulative_users[-1]  #  直接取最后一个元素\n", "axes[0].semilogx(\n", "    searched_users_counts.values, cumulative_users, \"b-\", label=\"累积分布\"\n", ")  # 使用 semilogx 绘制对数坐标轴\n", "\n", "for p in quantiles:  # 遍历新的分位数\n", "    value = np.percentile(searched_users_counts, p)\n", "    axes[0].axvline(x=value, linestyle=\"--\", alpha=0.3)\n", "    axes[0].text(value, 0.5, f\"P{p}={int(value)}\", rotation=90)  # 添加分位数文本\n", "\n", "axes[0].set_xlabel(\"Searched Users (对数尺度)\")  # 修改 x 轴标签，并添加中文注释\n", "axes[0].set_ylabel(\"累积比例\")  # 修改 y 轴标签\n", "axes[0].set_title(\"Searched Users 的累积分布图\")  # 修改标题，并添加中文注释\n", "axes[0].grid(True, alpha=0.3)  # 添加网格\n", "axes[0].legend()  # 显示图例\n", "\n", "\n", "# 添加 searched_users 的统计信息\n", "stats_text_users = f\"\"\"\n", "统计信息:\n", "平均值: {int(top_query_labeled_df['searched_users'].mean())}\n", "中位数: {int(top_query_labeled_df['searched_users'].median())}\n", "最大值: {int(top_query_labeled_df['searched_users'].max())}\n", "最小值: {int(top_query_labeled_df['searched_users'].min())}\n", "\"\"\"\n", "axes[0].text(\n", "    0.02,\n", "    0.98,\n", "    stats_text_users,\n", "    transform=axes[0].transAxes,\n", "    verticalalignment=\"top\",\n", "    bbox=dict(boxstyle=\"round\", facecolor=\"white\", alpha=0.8),\n", ")\n", "\n", "# 打印 searched_users 的具体分位数值\n", "print(\"\\nSearched Users 分位数统计:\")  # 添加中文注释\n", "print(\n", "    top_query_labeled_df[\"searched_users\"].describe(\n", "        percentiles=[q / 100 for q in quantiles]\n", "    )\n", ")  # 使用 describe 打印分位数\n", "\n", "\n", "# Cumulative distribution of searched_times\n", "searched_times_counts = top_query_labeled_df[\"searched_times\"].sort_values()\n", "cumulative_times = np.arange(1, len(searched_times_counts) + 1) / len(\n", "    searched_times_counts\n", ")  # 使用 numpy 创建累积分布\n", "total_times = cumulative_times[-1]  # 直接取最后一个元素\n", "print(f\"total_times:{total_times}\")\n", "\n", "\n", "axes[1].semilogx(\n", "    searched_times_counts.values, cumulative_times, \"b-\", label=\"累积分布\"\n", ")  # 使用 semilogx 绘制对数坐标轴\n", "for p in quantiles:  # 遍历分位数\n", "    value = np.percentile(searched_times_counts, p)\n", "    axes[1].axvline(x=value, linestyle=\"--\", alpha=0.3)\n", "    axes[1].text(value, 0.5, f\"P{p}={int(value)}\", rotation=90)  # 添加分位数文本\n", "\n", "axes[1].set_xlabel(\"Searched Times (对数尺度)\")  # 修改 x 轴标签，并添加中文注释\n", "axes[1].set_ylabel(\"累积比例\")  # 修改 y 轴标签\n", "axes[1].set_title(\"Searched Times 的累积分布图\")  # 修改标题，并添加中文注释\n", "axes[1].grid(True, alpha=0.3)  # 添加网格\n", "axes[1].legend()  # 显示图例\n", "\n", "\n", "# 添加 searched_times 的统计信息\n", "stats_text_times = f\"\"\"\n", "统计信息:\n", "平均值: {int(top_query_labeled_df['searched_times'].mean())}\n", "中位数: {int(top_query_labeled_df['searched_times'].median())}\n", "最大值: {int(top_query_labeled_df['searched_times'].max())}\n", "最小值: {int(top_query_labeled_df['searched_times'].min())}\n", "\"\"\"\n", "axes[1].text(\n", "    0.02,\n", "    0.98,\n", "    stats_text_times,\n", "    transform=axes[1].transAxes,\n", "    verticalalignment=\"top\",\n", "    bbox=dict(boxstyle=\"round\", facecolor=\"white\", alpha=0.8),\n", ")\n", "\n", "# 打印 searched_times 的具体分位数值\n", "print(\"\\nSearched Times 分位数统计:\")  # 添加中文注释\n", "print(\n", "    top_query_labeled_df[\"searched_times\"].describe(\n", "        percentiles=[q / 100 for q in quantiles]\n", "    )\n", ")  # 使用 describe 打印分位数\n", "\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["total_search_times = top_query_labeled_df[\"searched_times\"].sum()\n", "for i in range(10, 300, 10):\n", "    top_n = top_query_labeled_df[top_query_labeled_df[\"searched_times\"] >= i]\n", "    top_n_sum = top_n[\"searched_times\"].sum()\n", "    print(f\"搜索次数大于等于{i} 的词的总搜索次数占比: {top_n_sum / total_search_times:.2%}\")\n", "print(f\"total_search_times:{total_search_times}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["top_query_labeled_df[\"searched_times\"].describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["top_query_labeled_df[\"searched_times\"].quantile([0.25,0.5,0.75,.90,0.95,0.99,0.995,0.999])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["top_query_labeled_df[\"搜索频次标签\"] = top_query_labeled_df[\"searched_times\"].apply(\n", "    lambda x: \"高频搜索词(>230)\" if x > 230 else \"低频搜索词(<10)\" if x < 10 else \"中频搜索词\"\n", ")\n", "\n", "# 根据\"搜索频次标签\"分组，并计算每个标签下的query数量和搜索次数总和\n", "frequency_group = top_query_labeled_df.groupby(\"搜索频次标签\").agg(\n", "    query_count=(\"query\", \"count\"),  # 计算每个标签下的query数量\n", "    total_searched_times=(\"searched_times\", \"sum\"),  # 计算每个标签下的搜索次数总和\n", ")\n", "\n", "# 计算每个分组的搜索次数占比\n", "frequency_group['search_times_share'] = frequency_group['total_searched_times'] / total_search_times\n", "# 计算每个分组的query数量占比\n", "frequency_group['query_count_share'] = frequency_group['query_count'] / top_query_labeled_df.shape[0]\n", "frequency_group\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["high_frequency_query_df=top_query_labeled_df[top_query_labeled_df[\"搜索频次标签\"]=='高频搜索词(>230)']\n", "high_frequency_query_df=high_frequency_query_df.sort_values(by=[\"searched_users\",\"searched_times\"], ascending=[False,False])\n", "high_frequency_query_df.to_csv(f\"./高频搜索词_{datetime.now().strftime('%Y%m%d')}.csv\",index=False)\n", "high_frequency_query_df.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    high_frequency_query_df[\"p50_click_index\"].quantile(\n", "        [0.1, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99]\n", "    )\n", ")\n", "\n", "print(\n", "    high_frequency_query_df[\"p75_click_index\"].quantile(\n", "        [0.1, 0.25, 0.5, 0.75, 0.9, 0.95, 0.99]\n", "    )\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 筛选出p50点击索引大于等于11的query\n", "poor_query_df = high_frequency_query_df[\n", "    high_frequency_query_df[\"p50_click_index\"] >= 11\n", "].copy()\n", "\n", "# 对poor_query_df的几列进行四舍五入操作，并直接在原数据上更新\n", "# 使用.loc[:, cols] 来避免 SettingWithCopyWarning\n", "cols_to_round = [\"sku_ctr\", \"user_ctr\", \"p75_click_index\", \"p90_click_index\"]\n", "rounding_levels = [3, 2, 0, 0]  # 为每列指定不同的舍入精度\n", "\n", "for col, level in zip(cols_to_round, rounding_levels):\n", "    poor_query_df.loc[:, col] = poor_query_df[col].round(level)\n", "poor_query_df[\"link\"]=poor_query_df['query'].apply(lambda x: f\"http://xianmuai.s7.tunnelfrp.com/search-arena/?query={x}&page_size=40&city=杭州\")\n", "poor_query_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["poor_high_frequency_query_df = high_frequency_query_df[\n", "    (high_frequency_query_df[\"p50_click_index\"] >= 17.0)\n", "    | (poor_query_df[\"p75_click_index\"] > 35)\n", "]\n", "\n", "poor_high_frequency_query_df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 为每个标签创建一个字典来存储对应的DataFrame\n", "dfs_by_label = {}\n", "\n", "# 获取所有唯一的搜索频次标签\n", "unique_labels = top_query_labeled_df[\"搜索频次标签\"].unique()\n", "\n", "for label in unique_labels:\n", "    # 计算当前标签下user_ctr的10%分位数\n", "    label_df = top_query_labeled_df[top_query_labeled_df[\"搜索频次标签\"] == label]\n", "    label_df.to_csv(f\"商城搜索词列表_{label}.csv\", index=False)\n", "    quantile_10 = label_df[\"sku_ctr\"].quantile(0.1)\n", "    # 筛选出当前标签下sku_ctr小于等于10%分位数的数据\n", "    df_filtered = label_df[(label_df[\"sku_ctr\"] <= quantile_10)]\n", "    # 将筛选后的DataFrame存储到字典中，键为标签名\n", "    dfs_by_label[label] = df_filtered\n", "\n", "# 现在dfs_by_label包含了每个标签对应的DataFrame，你可以通过标签名访问它们\n", "# 例如, 要访问高频搜索词的DataFrame:\n", "high_frequency_df = dfs_by_label[\"高频搜索词(>230)\"]\n", "low_perf_df = high_frequency_df[high_frequency_df[\"p50_click_index\"] > 5]\n", "low_perf_df\n", "# 要访问所有标签的df，可以这样遍历\n", "for label, df in dfs_by_label.items():\n", "    df = df.sort_values(by=\"impression_cnt\", ascending=False)\n", "    display(df.head(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["medium_df = top_query_labeled_df[top_query_labeled_df[\"搜索频次标签\"] == \"中频搜索词\"]\n", "medium_df.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(medium_df[medium_df['user_ctr']<0.1].describe())\n", "medium_df[medium_df['user_ctr']<0.1].sort_values(by=['searched_times'], ascending=False).head(50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["medium_df[medium_df[\"user_ctr\"] < 0.4].sort_values(\n", "    by=[\"searched_times\"], ascending=False\n", ").head(20)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["poor_medium_frequency_query_df = medium_df[\n", "    (medium_df[\"p50_click_index\"] >= 17.0)\n", "    | (medium_df[\"p75_click_index\"] > 35)\n", "]\n", "\n", "poor_medium_frequency_query_df=poor_medium_frequency_query_df.sort_values(by=\"searched_users\", ascending=False)\n", "poor_medium_frequency_query_df[\"link\"]=poor_medium_frequency_query_df['query'].apply(lambda x: f\"http://xianmuai.s7.tunnelfrp.com/search-arena/?query={x}&page_size=40&city=杭州\")\n", "poor_medium_frequency_query_df.head(50)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["low_frequency_df = top_query_labeled_df[top_query_labeled_df[\"搜索频次标签\"] == \"低频搜索词(<10)\"]\n", "low_frequency_df.describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(low_frequency_df[low_frequency_df[\"user_ctr\"] < 0.01].describe())\n", "\n", "low_frequency_df[low_frequency_df[\"user_ctr\"] < 0.01].sort_values(\n", "    by=[\"searched_times\", \"searched_users\", \"impression_cnt\"], ascending=False\n", ")[\n", "    [\n", "        \"query\",\n", "        \"searched_users\",\n", "        \"searched_times\",\n", "        \"impression_cnt\",\n", "        \"click_cnt\",\n", "        \"sku_ctr\",\n", "        \"clicked_user_cnt\",\n", "        \"user_ctr\",\n", "        \"搜索频次标签\",\n", "    ]\n", "].head(\n", "    50\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["high_frequency_query_df.sort_values(by='searched_users', ascending=False).head(20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["high_frequency_query_df[high_frequency_query_df['query']=='啵啵']"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["medium_df.sort_values(by='searched_users', ascending=False).head(20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system_prompt=\"\"\"你是一位专业的电商搜索引擎优化分析师，专注于餐饮供应链领域。你的任务是分析点击率低的搜索词，找出问题原因并提供改进策略。\n", "\n", "分析对象：轻饮食行业供应链平台（主营烘焙、茶饮、咖啡等轻饮食行业的原料，包括鲜果、乳制品、面粉、西餐原材料等餐饮门店物料）\n", "\n", "当用户提供以下信息时：\n", "1. 低点击率的搜索词\n", "2. 该搜索词获得的搜索结果（商品SKU信息,前60个,实际上用户可能会看到比60个还要多的内容,我们仅需要基于这60个来分析）\n", "\n", "你需要进行深度分析并提供以下内容：\n", "1. 搜索词与结果的匹配度分析：\n", "   - 深度回顾该搜索词的搜索表现（用户会给出数据，请你完全依据用户的数据来深度解析）\n", "   - 语义匹配程度\n", "   - 商品类别是否符合搜索意图\n", "   - 搜索词是否存在歧义或多义性\n", "\n", "2. 搜索结果质量评估：\n", "   - 结果数量是否充足\n", "   - 商品描述是否清晰准确\n", "   - 价格竞争力分析\n", "   - 是否有库存信息显示\n", "\n", "3. 用户意图解析：\n", "   - 用户可能的真实需求是什么\n", "   - 搜索词背后的使用场景分析\n", "   - 行业特定术语vs通用词汇的使用差异\n", "\n", "4. 具体问题诊断（选择适用的）：\n", "   - 关键词匹配问题（如：ES词库缺失、同义词匹配缺失）\n", "   - 商品缺失问题（平台缺少相关商品）\n", "   - 结果排序问题（相关度算法调整需求）\n", "   - 季节性或时效性问题\n", "   - 行业术语与日常用语差异问题\n", "   - 商品属性标注不完善问题\n", "\n", "5. 改进策略建议（针对性提供）：\n", "   - 词库优化方案（添加同义词、近义词、行业术语映射等）\n", "   - 商品上新建议（具体指出缺失的SKU类型）\n", "   - 搜索结果排序算法调整建议\n", "   - 商品信息优化建议（标题、描述、规格等）\n", "   - 用户引导方式建议（搜索提示、热门搜索等）\n", "\n", "请提供具体、可操作的分析和建议，避免笼统的回答。针对餐饮供应链的特性，考虑B2B客户的专业采购需求与习惯。\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import json\n", "import os\n", "from search_xianmu_product import search_xianmu_product\n", "\n", "\n", "def analytics_with_r1(\n", "    query: str = \"寒天\",\n", "    csv_content=\"\",\n", "    low_perf_df: pd.DataFrame = pd.DataFrame(),\n", "):\n", "    perf = low_perf_df[low_perf_df[\"query\"] == query].iloc[0].to_dict()\n", "    data = {\n", "        \"model\": \"ep-20250206185410-zzb56\",\n", "        \"messages\": [\n", "            {\n", "                \"role\": \"system\",\n", "                \"content\": system_prompt,\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": f\"搜索词:{query}\\n搜索表现:{perf}\\n\\n\\n搜索结果:\\n{csv_content}\",\n", "            },\n", "        ],\n", "        \"stream\": True,\n", "    }\n", "\n", "    try:\n", "        response = requests.post(\n", "            \"https://ark.cn-beijing.volces.com/api/v3/chat/completions\",\n", "            headers={\n", "                \"Content-Type\": \"application/json\",\n", "                \"Authorization\": f\"Bearer {os.getenv('ARK_API_KEY')}\",\n", "            },\n", "            json=data,\n", "            stream=True,\n", "        )\n", "    except requests.RequestException as e:\n", "        print(f\"Request failed: {e}\")\n", "        return\n", "\n", "    final_content = \"\"\n", "    final_reasoning_content = \"\"\n", "    if response.status_code == 200:\n", "        for line in response.iter_lines():\n", "            if line:\n", "                line_content = line.decode(\"utf-8\").strip()\n", "                if line_content.startswith(\"data: \"):\n", "                    try:\n", "                        json_content = line_content[6:]  # Remove \"data: \" prefix\n", "                        if len(json_content) <= 2 or \"[DONE]\" == json_content:\n", "                            # JSON content should be at least {}\n", "                            print(f\"接收到了非JSON数据:{line_content}, 将跳过..\")\n", "                            continue\n", "                        delta_content = json.loads(json_content)\n", "                        if delta_content:\n", "                            choices = delta_content.get(\"choices\")\n", "                            if choices and len(choices) > 0:\n", "                                delta = choices[0].get(\"delta\")\n", "                                if delta:\n", "                                    reasoning_content = delta.get(\n", "                                        \"reasoning_content\", \"\"\n", "                                    )\n", "                                    content = delta.get(\"content\", \"\")\n", "                                    # 如果存在 reasoning_content，则将其添加到 final_reasoning_content 中\n", "                                    if reasoning_content:\n", "                                        final_reasoning_content += reasoning_content\n", "                                        # 使用 end='' 参数来避免 print 函数换行\n", "                                        print(reasoning_content, end=\"\")\n", "                                    # 如果存在 content，则将其添加到 final_content 中\n", "                                    elif content:\n", "                                        final_content += content\n", "                                        print(content, end=\"\")\n", "                    except json.JSONDecodeError as e:\n", "                        print(f\"JSONDecodeError: {e} - Data: {line_content}\")\n", "                        continue  # Skip to the next line if JSON decoding fails\n", "        print(f\"Yielding final content: {final_content}\")\n", "    else:\n", "        print(f\"Error: {response.status_code} - {response.text}\")\n", "        return None, None\n", "\n", "    return (final_reasoning_content, final_content)\n", "\n", "\n", "def analyze_and_report_search_query(query: str, city: str = \"杭州\"):\n", "    \"\"\"\n", "    搜索鲜沐产品，分析搜索结果，并发送飞书通知。\n", "\n", "    Args:\n", "        query (str): 搜索关键词。\n", "        city (str): 搜索城市。\n", "    \"\"\"\n", "    # 搜索鲜沐产品\n", "    searched_items = search_xianmu_product(query=query, city=city, page_size=60)\n", "    # 将搜索结果转换为pandas DataFrame\n", "    searched_items_df = pd.DataFrame(searched_items)\n", "    # 提取'sku_id', 'spu_name', 'brand', 'properties', 'category', 'area_price'列，并将DataFrame写入CSV文件，包含索引\n", "    csv_content = searched_items_df[\n", "        [\"sku_id\", \"spu_name\", \"brand\", \"properties\", \"category\", \"area_price\"]\n", "    ].to_csv(index=True)\n", "    final_reasoning_content, final_content = analytics_with_r1(\n", "        query=query, csv_content=csv_content, low_perf_df=low_perf_df\n", "    )\n", "    send_feishu_notice_with_title_and_content(\n", "        title=f\"‘{query}’搜索词的R1分析报告\", markdown_str=final_content\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 调用函数，执行对每个query在\"杭州\"的搜索分析\n", "# for index, row in low_perf_df.iterrows():\n", "#     analyze_and_report_search_query(query=row[\"query\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}