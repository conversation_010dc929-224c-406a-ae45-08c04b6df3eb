{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## PG链接"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import psycopg2\n", "import logging\n", "import argparse\n", "\n", "# Add argument parsing for pg_host\n", "parser = argparse.ArgumentParser()\n", "parser.add_argument(\"--pg_host\", default=\"127.0.0.1\", help=\"PostgreSQL host address\")\n", "args, unknown = parser.parse_known_args()\n", "pg_host = args.pg_host\n", "\n", "\n", "def get_arena_db_connection_and_execute(\n", "    sql: str, args: tuple = (), pg_host: str = pg_host\n", "):\n", "    conn = psycopg2.connect(\n", "        host=pg_host,\n", "        port=5432,\n", "        database=\"search_arena\",\n", "        user=\"llmproxy\",\n", "        password=\"dbpassword9090\",\n", "    )\n", "    try:\n", "        cur = conn.cursor()\n", "        cur.execute(sql, args)\n", "        \n", "        # Only commit for INSERT, UPDATE, DELETE operations\n", "        if sql.strip().upper().startswith(('INSERT', 'UPDATE', 'DELETE')):\n", "            conn.commit()\n", "            logging.info(f\"sql: {sql}, args: {args}, rows affected: {cur.rowcount}\")\n", "        \n", "        return cur.fetchall() if cur.description else cur.rowcount\n", "    finally:\n", "        cur.close()\n", "        conn.close()\n", "\n", "\n", "get_arena_db_connection_and_execute(\n", "    \"\"\"CREATE TABLE IF NOT EXISTS search_arena_record (\n", "        userId TEXT, referer_url TEXT, user_ip TEXT, user_agent TEXT, create_time TEXT, \n", "        query TEXT, city TEXT, page_size INTEGER, prefered_version TEXT, comment TEXT)\"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import sys\n", "import sqlite3\n", "import os\n", "import pandas as pd\n", "\n", "# Add the scripts directory to the sys.path\n", "sys.path.append(\"../\")\n", "\n", "from odps_client import get_odps_sql_result_as_df\n", "\n", "sku_dim_query = \"\"\"\n", "SELECT  a.sku_id\n", "        ,a.spu_id\n", "        ,a.spu_name\n", "        ,b.sku_name\n", "        ,a.spu_no\n", "        ,a.disc\n", "        ,a.store_method\n", "        ,a.sku_type\n", "        ,a.category1\n", "        ,a.category2\n", "        ,a.category3\n", "        ,a.category4\n", "        ,a.sku_spec\n", "        ,a.origin\n", "        ,a.sku_brand\n", "        ,a.temp<PERSON>\n", "        ,a.other_properties\n", "        ,a.sub_type\n", "        ,a.ds\n", "        ,price.area_price\n", "        ,COALESCE(b.sku_pic,c.picture_path,'404.jpg') AS img_url\n", "        ,d.total_gmv as current_month_gmv\n", "FROM    summerfarm_tech.dim_sku_df a\n", "INNER JOIN summerfarm_tech.ods_inventory_df b\n", "ON      b.ds = MAX_PT('summerfarm_tech.ods_inventory_df')\n", "AND     a.sku_id = b.sku\n", "INNER JOIN summerfarm_tech.ods_products_df c\n", "ON      c.ds = MAX_PT('summerfarm_tech.ods_products_df')\n", "AND     a.spu_id = c.pd_id\n", "INNER JOIN  (\n", "                SELECT  aa.sku\n", "                        ,ARRAY_JOIN(COLLECT_SET(CONCAT(bb.area_name,'¥',aa.price)),',') AS area_price\n", "                FROM    summerfarm_tech.ods_area_sku_df aa\n", "                INNER JOIN summerfarm_tech.ods_area_df bb\n", "                ON      aa.area_no = bb.area_no\n", "                AND     bb.ds = MAX_PT('summerfarm_tech.ods_area_df')\n", "                WHERE   aa.ds = MAX_PT('summerfarm_tech.ods_area_sku_df')\n", "                AND     bb.area_name IN ('上海','杭州','深圳','广州','苏州','重庆','成都','宁波','武汉普冷','南京','青岛','长沙普冷')\n", "                AND     aa.on_sale = 1\n", "                AND     bb.status = 1\n", "                GROUP BY aa.sku\n", "            ) price\n", "ON      a.sku_id = price.sku\n", "LEFT JOIN   (\n", "                SELECT  sku_id\n", "                        ,SUM(gmv) total_gmv\n", "                        ,COUNT(1) total_count\n", "                        ,SUM(sales_volume) total_sales_volume\n", "                FROM    summerfarm_tech.app_crm_sku_month_gmv_di\n", "                WHERE   ds = MAX_PT('summerfarm_tech.app_crm_sku_month_gmv_di')\n", "                GROUP BY sku_id\n", "            ) d\n", "ON      a.sku_id = d.sku_id\n", "WHERE   a.ds = MAX_PT('summerfarm_tech.dim_sku_df')\n", "AND     a.sub_type != 4\n", "AND     a.outdated != 1\n", "ORDER BY d.total_gmv DESC\n", ";\n", "\"\"\"\n", "sku_dim_df = get_odps_sql_result_as_df(sku_dim_query)\n", "\n", "db_path = os.path.expanduser('~/sqlite/xianmu_sku_dim_df.db')\n", "os.makedirs(os.path.dirname(db_path), exist_ok=True)\n", "\n", "conn = sqlite3.connect(db_path)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hangzhou_df=sku_dim_df[sku_dim_df['area_price'].str.contains('杭州¥')]\n", "\n", "print(len(hangzhou_df), len(sku_dim_df))\n", "hangzhou_df[hangzhou_df['category4']=='芒果'][['spu_name','sku_id','category4','area_price','current_month_gmv']]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["area_df = get_odps_sql_result_as_df(\n", "    \"\"\"select area_no, area_name from summerfarm_tech.ods_area_df \n", "    where ds=max_pt('summerfarm_tech.ods_area_df')\"\"\"\n", ")\n", "area_name_to_no_map = {}\n", "for index, row in area_df.iterrows():\n", "    area_name_to_no_map[row[\"area_name\"]] = row[\"area_no\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(sku_dim_df.columns)\n", "# Convert complex columns to string or JSON to avoid binding errors\n", "columns_to_convert = [\n", "    \"other_properties\",\n", "]\n", "for col in columns_to_convert:\n", "    sku_dim_df[col] = sku_dim_df[col].fillna(\"\").astype(str)\n", "\n", "# Select only columns that can be easily serialized\n", "columns_to_save = [\n", "    col\n", "    for col in sku_dim_df.columns\n", "    if sku_dim_df[col].dtype in [\"int64\", \"float64\", \"object\"]\n", "]\n", "sku_dim_df['current_month_gmv']=sku_dim_df['current_month_gmv'].astype(float)\n", "sku_dim_df.to_sql(\"sku_dim_df\", conn, if_exists=\"replace\", index=False)\n", "\n", "city_list = set()\n", "for _, row in sku_dim_df.iterrows():\n", "    area_price = row[\"area_price\"]\n", "    area_price = area_price.split(\",\")\n", "    for price in area_price:\n", "        city_list.add(price.split(\"¥\")[0])\n", "\n", "print(f\"sku_dim_df.columns:{sku_dim_df.columns}, city_list:{city_list}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "import time\n", "import json\n", "from typing import List\n", "import numpy as np\n", "import faiss\n", "from tqdm import tqdm\n", "\n", "\n", "from openai import AzureOpenAI\n", "import httpx\n", "\n", "client = AzureOpenAI(\n", "    # https://esat-us.openai.azure.com/openai/deployments/text-embedding-3-small/embeddings?api-version=2023-05-15\n", "    api_version=\"2023-07-01-preview\",\n", "    azure_endpoint=\"https://esat-us.openai.azure.com\",\n", "    api_key=os.getenv(\"AZURE_API_KEY_XM\", \"please set:AZURE_API_KEY_XM\"),\n", "    http_client=httpx.Client(proxies={\"http://\": None, \"https://\": None}),\n", ")\n", "\n", "embedding_model = os.getenv(\"AZURE_EMBEDDING_MODEL\", \"text-embedding-3-small\")\n", "\n", "\n", "def get_embedding_directly_from_azure(input: str) -> list:\n", "    embbed = client.embeddings.create(model=embedding_model, input=input)\n", "    return embbed.to_dict().get(\"data\", [{}])[0].get(\"embedding\")\n", "\n", "\n", "def get_embedding(input_text: str) -> str:\n", "    result = get_arena_db_connection_and_execute(\n", "        sql=\"SELECT embedding FROM embeddings WHERE input_text = %s\", args=(input_text,)\n", "    )\n", "\n", "    if result:\n", "        embedding = result[0]\n", "        embedding = embedding[0] if isinstance(embedding, tuple) else embedding\n", "        logging.info(\n", "            f\"Found, return the embedding of input_text:{input_text}, {embedding[:20]}\"\n", "        )\n", "    else:\n", "        logging.info(\n", "            f\"Not found, call the OpenAI API to get the embedding:{input_text}\"\n", "        )\n", "        embedding = str(get_embedding_directly_from_azure(input_text))\n", "\n", "        # Insert the new input text and embedding into the database\n", "        get_arena_db_connection_and_execute(\n", "            \"INSERT INTO embeddings (input_text, embedding) VALUES (%s, %s)\",\n", "            (input_text, embedding),\n", "        )\n", "    return embedding\n", "\n", "\n", "def get_text_embeddings(text: str, from_local: bool = False) -> np.ndarray:\n", "    \"\"\"获取文本嵌入向量\n", "    Args:\n", "        text: 输入文本\n", "        batch_size: 批处理大小\n", "    Returns:\n", "        embeddings: 文本嵌入向量\n", "    \"\"\"\n", "    start_time = time.time()\n", "\n", "    embeddings = get_embedding(text)\n", "    \n", "    # If embeddings is a tuple, get the first element\n", "    if isinstance(embeddings, tuple):\n", "        embeddings = embeddings[0]\n", "    # Convert string embeddings to numpy array\n", "    if isinstance(embeddings, str):\n", "        embeddings = json.loads(embeddings)\n", "    embeddings = np.array(embeddings, dtype=np.float32)\n", "\n", "    logging.info(\n", "        f\"total time cost: {time.time() - start_time}ms, embeddings:{embeddings[0:20]}\"\n", "    )\n", "    return embeddings\n", "\n", "\n", "def batch_get_embeddings(texts: List[str], batch_size: int = 32) -> List[np.ndarray]:\n", "    \"\"\"批量获取文本嵌入向量\n", "    Args:\n", "        texts: 文本列表\n", "        batch_size: 批处理大小\n", "    Returns:\n", "        embeddings_list: 嵌入向量列表\n", "    \"\"\"\n", "    embeddings_list = []\n", "    for i in tqdm(range(0, len(texts), batch_size)):\n", "        batch_texts = texts[i : i + batch_size]\n", "        batch_embeddings = [get_text_embeddings(text) for text in batch_texts]\n", "        embeddings_list.extend(batch_embeddings)\n", "    return embeddings_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1. 数据预处理和特征工程\n", "# 组合多个字段作为文本特征,增加字段权重\n", "sku_dim_df_clean_100 = sku_dim_df.head(50000)\n", "sku_dim_df_clean_100[\"sku_brand\"] = sku_dim_df_clean_100[\"sku_brand\"].fillna(\"\")\n", "sku_dim_df_clean_100[\"sku_brand\"] = sku_dim_df_clean_100[\"sku_brand\"].replace(\"无\", \"\")\n", "\n", "sku_dim_df_clean_100[\"text_features\"] = sku_dim_df_clean_100.apply(\n", "    lambda row: f'{row[\"spu_name\"]},{row[\"category4\"]},{row[\"other_properties\"]}',\n", "    # 增加商品名称权重\n", "    # 增加四级级类目\n", "    axis=1,\n", ")\n", "\n", "sku_dim_df_clean_100[\"name_features\"] = sku_dim_df_clean_100.apply(\n", "    lambda row: f'{row[\"spu_name\"]},{row[\"sku_brand\"]}', axis=1\n", ")\n", "\n", "# 2. 批量获取嵌入向量\n", "embeddings = batch_get_embeddings(sku_dim_df_clean_100[\"text_features\"].tolist())\n", "name_embeddings = batch_get_embeddings(sku_dim_df_clean_100[\"name_features\"].tolist())\n", "\n", "embeddings_array = np.array(embeddings, dtype=np.float32)  # 确保数据类型为float32\n", "name_embeddings_array = np.array(\n", "    name_embeddings, dtype=np.float32\n", ")  # 确保数据类型为float32\n", "\n", "# 3. 构建FAISS索引\n", "# 使用IVFFlat索引提高搜索效率\n", "# 设置合适的聚类中心数量,一般建议为数据量的平方根\n", "# nlist = int(np.sqrt(len(embeddings_array)))\n", "nlist = sku_dim_df_clean_100[\"spu_id\"].unique().shape[0]\n", "quantizer = faiss.IndexFlatL2(embeddings_array.shape[1])\n", "index = faiss.IndexIVFFlat(\n", "    quantizer, embeddings_array.shape[1], nlist, faiss.METRIC_INNER_PRODUCT\n", ")  # 改用内积相似度\n", "\n", "name_quantizer = faiss.IndexFlatL2(name_embeddings_array.shape[1])\n", "name_index = faiss.IndexIVFFlat(\n", "    name_quantizer, name_embeddings_array.shape[1], nlist, faiss.METRIC_INNER_PRODUCT\n", ")  # 改用内积相似度\n", "\n", "# 归一化向量\n", "embeddings_array = embeddings_array.astype(np.float32)  # 再次确保类型\n", "faiss.normalize_L2(embeddings_array)\n", "\n", "name_embeddings_array = name_embeddings_array.astype(np.float32)  # 再次确保类型\n", "faiss.normalize_L2(name_embeddings_array)\n", "\n", "index.train(embeddings_array)\n", "index.add(embeddings_array)\n", "\n", "name_index.train(name_embeddings_array)\n", "name_index.add(name_embeddings_array)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def search_similar_skus(query: str, k: int = 5, nprobe: int = 2):\n", "    \"\"\"搜索相似SKU\n", "    Args:\n", "        query: 查询文本\n", "        k: 返回结果数量\n", "        nprobe: 搜索聚类中心数量\n", "    Returns:\n", "        distances: 距离分数\n", "        indices: 相似商品索引\n", "    \"\"\"\n", "    query_embedding = np.array(get_text_embeddings(query), dtype=np.float32).reshape(\n", "        1, -1\n", "    )\n", "    # 归一化查询向量\n", "    faiss.normalize_L2(query_embedding)\n", "\n", "    # 设置搜索聚类中心数量\n", "    index.nprobe = nprobe\n", "    name_index.nprobe = nprobe\n", "\n", "    # 分别搜索两个索引\n", "    D1, I1 = index.search(query_embedding, k)\n", "    D2, I2 = name_index.search(query_embedding, k)\n", "\n", "    # 合并结果并计算总分\n", "    results = []\n", "    for i in range(len(I1[0])):\n", "        results.append((I1[0][i], D1[0][i], D2[0][i]))\n", "    for i in range(len(I2[0])):\n", "        if I2[0][i] not in [x[0] for x in results]:\n", "            results.append((I2[0][i], D1[0][i], D2[0][i]))\n", "\n", "    # 按总分排序\n", "    results.sort(key=lambda x: x[1] + x[2], reverse=True)\n", "\n", "    # 取前k个结果\n", "    results = results[:k]\n", "\n", "    # 转换为原始格式\n", "    D = np.array([[x[1] + x[2] for x in results]])\n", "    I = np.array([[x[0] for x in results]])\n", "\n", "    return D, I"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["import logging\n", "\n", "\n", "category4_cnt = len(sku_dim_df_clean_100[\"category4\"].unique())\n", "\n", "\n", "def search_items_with_query(\n", "    query: str,\n", "    city: str = None,\n", "    page_size: int = 20,\n", "    nprobe: int = category4_cnt * 2,\n", ") -> list[dict]:\n", "    \"\"\"使用向量搜索查找相似商品\n", "\n", "    根据输入的查询文本,在商品库中搜索相似的商品。支持按城市筛选价格。\n", "\n", "    Args:\n", "        query (str): 查询文本\n", "        city (str, optional): 城市名称,用于筛选该城市的价格。默认为 None\n", "        page_size (int, optional): 返回结果数量。默认为 20\n", "        nprobe (int, optional): FAISS索引搜索聚类中心数量。默认为商品总数的一半\n", "\n", "    Returns:\n", "        list[dict]: 搜索结果列表,每个结果包含以下字段:\n", "            - spu_name (str): 商品名称\n", "            - category (str): 商品类目(三级类目_四级类目)\n", "            - category4 (str): 四级类目\n", "            - sku_id (str): SKU ID\n", "            - text_features (str): 商品文本特征\n", "            - name_features (str): 商品名称特征\n", "            - area_price_full (str): 所有城市价格\n", "            - area_price (str): 指定城市价格\n", "            - score (float): 相似度分数\n", "            - img_url (str): 商品图片URL\n", "            - json_result (dict): 完整商品信息\n", "\n", "    Examples:\n", "        >>> results = search_items_with_query(\"苹果\", city=\"上海\")\n", "        >>> print(results[0][\"spu_name\"])\n", "        '红富士苹果'\n", "    \"\"\"\n", "    D, I = search_similar_skus(\n", "        query,\n", "        k=min(max(page_size * 4, 100), int(len(sku_dim_df_clean_100) / 40)),\n", "        nprobe=nprobe,\n", "    )\n", "\n", "    results = []\n", "\n", "    # Format results\n", "    for idx, (score, index) in enumerate(zip(D[0], I[0])):\n", "        result_row = sku_dim_df_clean_100.iloc[index].to_dict()\n", "        result_row[\"score\"] = round(score, 4)\n", "        logging.info(f\"{query}: {result_row}\")\n", "        area_price = result_row[\"area_price\"]\n", "        city_price = {}\n", "        for city_info in area_price.split(\",\"):\n", "            city_in_map, price = city_info.split(\"¥\")\n", "            city_price[city_in_map] = float(price.replace(\",\", \"\"))\n", "        if city and city not in city_price:\n", "            logging.warning(f\"{city} not in area_price:{area_price}\")\n", "            continue\n", "        results.append(\n", "            {\n", "                \"spu_name\": result_row[\"spu_name\"],\n", "                \"category\": f\"{ result_row['category3']}_{result_row['category4']}\",\n", "                \"category4\": result_row[\"category4\"],\n", "                \"sku_id\": result_row[\"sku_id\"],\n", "                \"text_features\": result_row[\"text_features\"],\n", "                \"name_features\": result_row[\"name_features\"],\n", "                \"area_price_full\": f\"{result_row['area_price']}\",\n", "                \"area_price\": f\"{city}¥{city_price[city]}\",\n", "                \"score\": round(score, 4),\n", "                \"img_url\": f'https://azure.summerfarm.net/{result_row[\"img_url\"]}?imageslim=3',\n", "                \"json_result\": result_row,\n", "            }\n", "        )\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from odps_client import get_odps_sql_result_as_df\n", "from datetime import datetime, timedelta\n", "\n", "last_n_days = 14\n", "\n", "ds_yesterday = (datetime.now() - timedelta(days=1)).strftime(\"%Y%m%d\")\n", "last_n_days_ago = (datetime.now() - timedelta(days=last_n_days)).strftime(\"%Y%m%d\")\n", "\n", "top_query = f\"\"\"\n", "SELECT  query\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN cust_id END) AS searched_users\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN CONCAT(time,cust_id) END) AS search_cnt\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN CONCAT(time,cust_id) END) AS click_cnt\n", "        ,ROUND(AVG(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS avg_click_index\n", "        ,ROUND(MAX(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS max_click_index\n", "        ,ROUND(MIN(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS min_click_index\n", "        ,ROUND(STDDEV(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS std_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index\n", "FROM    summerfarm_tech.app_log_search_detail_di\n", "WHERE   ds BETWEEN '{last_n_days_ago}' and '{ds_yesterday}'\n", "GROUP BY query\n", "ORDER BY searched_users DESC\n", ";\n", "\"\"\"\n", "\n", "top_query_df = get_odps_sql_result_as_df(sql=top_query)\n", "top_query_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 热门的top search term\n", "top_query_cnt_to_display = 50\n", "top_query_df[\"ctr\"] = round(\n", "    top_query_df[\"click_cnt\"] * 1.00 / top_query_df[\"search_cnt\"], 4\n", ")\n", "top_20_query_df = top_query_df.head(top_query_cnt_to_display)\n", "top_20_query = [item.to_dict() for _, item in top_20_query_df.iterrows()]\n", "\n", "print(\n", "    f\"有点击的query的ctr分布:\",\n", "    top_query_df[top_query_df[\"click_cnt\"] > 0][\"ctr\"].quantile(\n", "        [0.10, 0.25, 0.5, 0.75, 0.90, 0.95]\n", "    ),\n", ")\n", "\n", "# 低点击率的top search term\n", "top_high_click_index_20_query_df = top_query_df[\n", "    top_query_df[\"searched_users\"] > 10\n", "].sort_values(by=[\"search_cnt\"], ascending=False)\n", "\n", "# Fix the boolean indexing by using & instead of or\n", "top_high_click_index_20_query_df = top_high_click_index_20_query_df[\n", "    (top_high_click_index_20_query_df[\"min_click_index\"] > 6) | \n", "    (top_high_click_index_20_query_df[\"ctr\"] <= 0.1)\n", "].head(top_query_cnt_to_display)\n", "\n", "top_high_click_index_20_query = [\n", "    item.to_dict() for _, item in top_high_click_index_20_query_df.iterrows()\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["category_prediction_query=f\"\"\"\n", "SELECT  query\n", "        ,b.category4\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN cust_id END) AS searched_users\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN CONCAT(time,cust_id) END) AS search_cnt\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN CONCAT(time,cust_id) END) AS click_cnt\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN cust_id END) AS click_users\n", "        ,ROUND(AVG(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS avg_click_index\n", "        ,ROUND(MAX(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS max_click_index\n", "        ,ROUND(MIN(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS min_click_index\n", "        ,ROUND(STDDEV(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS std_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.25) AS p25_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index\n", "        ,COUNT(DISTINCT a.ds) AS days_have_impression\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN a.ds END) AS days_have_click\n", "FROM    summerfarm_tech.app_log_search_detail_di a\n", "LEFT JOIN summerfarm_tech.dim_sku_df b\n", "ON      b.ds = MAX_PT('summerfarm_tech.dim_sku_df')\n", "AND     a.sku_id = b.sku_id\n", "WHERE   a.ds BETWEEN '{last_n_days_ago}' and '{ds_yesterday}'\n", "GROUP BY query\n", "         ,b.category4\n", "HAVING  click_cnt > 0 and click_users>2\n", "ORDER BY searched_users DESC\n", ";\n", "\"\"\"\n", "\n", "category_prediction_df=get_odps_sql_result_as_df(category_prediction_query)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    category_prediction_df[\"min_click_index\"].quantile(\n", "        [0.25, 0.5, 0.75, 0.80, 0.90, 0.95]\n", "    )\n", ")\n", "\n", "category_prediction_valid_df = category_prediction_df[\n", "    (category_prediction_df[\"min_click_index\"] <= 6.0)\n", "    & (category_prediction_df[\"click_cnt\"] >= 7)\n", "]\n", "\n", "# Group by query and category4, calculate click counts and percentiles\n", "category_prediction_grouped = (\n", "    category_prediction_valid_df.groupby([\"query\", \"category4\"])\n", "    .agg({\"click_cnt\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "# Calculate percentile rank within each query group\n", "category_prediction_grouped[\"category_rank\"] = category_prediction_grouped.groupby(\n", "    \"query\"\n", ")[\"click_cnt\"].transform(lambda x: x.rank(pct=True))\n", "\n", "# Calculate click count ratio within each query group\n", "category_prediction_grouped[\"category_percentile\"] = (\n", "    category_prediction_grouped.groupby(\"query\")[\"click_cnt\"].transform(\n", "        lambda x: x / x.sum()\n", "    )\n", ")\n", "\n", "category_prediction_grouped.sort_values(by=[\"click_cnt\"], ascending=False, inplace=True)\n", "category_prediction_grouped[\"category_rank\"] = category_prediction_grouped[\n", "    \"category_rank\"\n", "].astype(float)\n", "category_prediction_grouped[\n", "    category_prediction_grouped[\"query\"].isin([\"芒果\", \"奶油\", \"苹果\", \"梨\"])\n", "].head(20)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["category_prediction_map = {}\n", "\n", "for _, row in category_prediction_grouped.iterrows():\n", "    query = row[\"query\"]\n", "    category4 = row[\"category4\"]\n", "    category_rank = row[\"category_rank\"]\n", "    if query not in category_prediction_map:\n", "        category_prediction_map[query] = {}\n", "    category_prediction_map[query][category4] = category_rank\n", "\n", "print(len(category_prediction_map))\n", "\n", "\n", "def refine_items_with_category4(item_list: list[dict], query: str) -> list[dict]:\n", "    if len(item_list) <= 1:\n", "        return item_list\n", "    if query not in category_prediction_map:\n", "        logging.info(f\"query not in category_prediction_map, query={query}\")\n", "        return item_list\n", "    category_rank_map = category_prediction_map[query]\n", "    if len(category_rank_map) <= 1:\n", "        logging.info(f\"query has only one category prediction result. query={query}\")\n", "\n", "    # Calculate category weighted score for each item\n", "    for item in item_list:\n", "        if \"score\" not in item:\n", "            logging.error(f\"item has no score, item={item}\")\n", "            item[\"score\"] = 0.1\n", "        category4 = item.get(\"category4\")\n", "        if category4 and category4 in category_rank_map:\n", "            item[\"category_weighted_score\"] = (\n", "                item[\"score\"] * category_rank_map[category4]\n", "            )\n", "        else:\n", "            item[\"category_weighted_score\"] = item[\"score\"] * 0.1\n", "\n", "    # Sort by category_weighted_score and score\n", "    sorted_items = sorted(\n", "        item_list, key=lambda x: (-x[\"category_weighted_score\"], -x[\"score\"])\n", "    )\n", "\n", "    return sorted_items"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 用来搜索鲜沐的线上结果"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import hashlib\n", "import requests\n", "from datetime import datetime\n", "\n", "\n", "def get_md5_encoded_string(phone_number, date, word):\n", "    input_string = f\"{phone_number}{date}{word}\"\n", "    input_bytes = input_string.encode(\"utf-8\")\n", "    md5_hash = hashlib.md5(input_bytes)\n", "    md5_hex = md5_hash.hexdigest()\n", "    return md5_hex\n", "\n", "\n", "token_cache = {}\n", "\n", "\n", "def get_token_for_phone(phone_number: str = \"18618107293\") -> str:\n", "    today = datetime.now().strftime(\"%Y%m%d\")\n", "\n", "    cache_key = f\"{phone_number}{today}\"\n", "    if cache_key in token_cache:\n", "        return token_cache.get(cache_key)\n", "\n", "    word = \"login\"\n", "    md5_encoded_string = get_md5_encoded_string(phone_number, today, word)\n", "    logging.info(md5_encoded_string)\n", "\n", "    url = f\"https://h5.summerfarm.net/openid?phone={phone_number}&sign={md5_encoded_string}\"\n", "    logging.info(url)\n", "    token = requests.get(url=url, timeout=12000, proxies={}).json()\n", "    logging.info(f\"token:{token}\")\n", "    try:\n", "        token_cache[cache_key] = token[\"data\"][\"token\"]\n", "        return token[\"data\"][\"token\"]\n", "    except Exception as e:\n", "        logging.error(f\"获取Token失败:{url}, {token}, {e}\")\n", "        raise e\n", "\n", "\n", "get_token_for_phone()\n", "\n", "\n", "def search_xianmu_product(query: str, city: str, page_size: int = 6) -> list[dict]:\n", "    token = get_token_for_phone()\n", "    area_no = area_name_to_no_map.get(city, \"1001\")\n", "    url = (\n", "        f\"https://h5.summerfarm.net/product/1/{page_size}?areaNo={area_no}&pdName={query}\"\n", "    )\n", "    headers = {\"token\": f\"{token}\", \"Content-Type\": \"application/json\"}\n", "    response = requests.get(url=url, headers=headers, timeout=12000, proxies={}).json()\n", "    logging.info(f\"query:{ query}, city:{city}, page_size:{page_size}, response:{response}\")\n", "    product_list = response[\"data\"][\"list\"]\n", "    if not isinstance(product_list, list) or len(product_list) <= 0:\n", "        return []\n", "    result_list = []\n", "    for product in product_list:\n", "        result = {}\n", "        result[\"area_price\"] = f\"{city}¥{product.get('salePrice', '')}\"\n", "        result[\"sku_id\"] = f\"{product.get('sku', '')}\"\n", "        result[\"spu_name\"] = f\"{product.get('pdName', '')}\"\n", "        keys_to_extract = [\n", "            \"category_id\",\n", "            \"face_price_hide\",\n", "            \"info\",\n", "            \"pd_id\",\n", "            \"pd_name\",\n", "            \"pddetail\",\n", "            \"sale_price\",\n", "            \"sku\",\n", "            \"sku_name\",\n", "            \"sub_type\",\n", "            \"unit\",\n", "            \"weight\",\n", "            \"weight_num\",\n", "        ]\n", "        json_result = {\n", "            key: product[key.replace(\"_\", \"\").lower()]\n", "            for key in keys_to_extract\n", "            if key.replace(\"_\", \"\").lower() in product\n", "        }\n", "        result[\"img_url\"] = (\n", "            f\"https://azure.summerfarm.net/{product['picturePath']}?imageslim=3\"\n", "        )\n", "        key_value_list = product[\"keyValueList\"]\n", "        brand = \"\"\n", "        kv_map = {}\n", "        for key_value in key_value_list:\n", "            if \"name\" in key_value and \"productsPropertyValue\" in key_value:\n", "                kv_map[key_value[\"name\"]] = key_value[\"productsPropertyValue\"]\n", "                if key_value[\"name\"] == \"品牌\":\n", "                    brand = key_value[\"productsPropertyValue\"]\n", "        result[\"name_features\"] = f'{product[\"pdName\"]},{brand}'\n", "        result[\"text_features\"] = f'{product[\"pdName\"]},{kv_map}'\n", "        logging.info(f\"{query} summerfarm.net search result: {result}\")\n", "        result[\"json_result\"] = json_result\n", "        result_list.append(result)\n", "\n", "    return result_list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "\n", "random_query_list=[]\n", "\n", "for _,row in top_query_df[top_query_df['searched_users']>10].iterrows():\n", "    random_query_list.append({\"query\":row['query'],\"ctr\":row['ctr'],\"search_cnt\":row['search_cnt']})\n", "\n", "print(random.choice(random_query_list))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 启动flask app"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["def get_prefered_version_stats():\n", "    sql = \"\"\"SELECT \n", "             CASE prefered_version\n", "                WHEN 'new_embedding_search' THEN '新搜索'\n", "                WHEN 'old_es_search' THEN '旧版'\n", "                WHEN 'they-are-tied' THEN '都差不多'\n", "                WHEN 'both-are-poor' THEN '一样差'\n", "             END as prefered_version,\n", "             COUNT(*) as total_count,\n", "             COUNT(DISTINCT userId) as unique_users,\n", "             COUNT(DISTINCT query) as unique_queries\n", "             FROM search_arena_record \n", "             GROUP BY prefered_version\"\"\"\n", "    results = get_arena_db_connection_and_execute(sql)\n", "    stats = {\n", "        row[0]: {\n", "            \"total_count\": row[1],\n", "            \"unique_users\": row[2],\n", "            \"unique_queries\": row[3],\n", "        }\n", "        for row in results\n", "    }\n", "    logging.info(f\"Prefered Version Stats: {stats}\")\n", "    return stats\n", "\n", "def get_all_arena_records():\n", "    sql = \"\"\"SELECT \n", "             userId, referer_url, user_ip, user_agent, create_time,\n", "             query, city, page_size, prefered_version, comment\n", "             FROM search_arena_record \n", "             ORDER BY create_time DESC\"\"\"\n", "    results = get_arena_db_connection_and_execute(sql)\n", "    records = []\n", "    for row in results:\n", "        record = {\n", "            \"userId\": row[0],\n", "            \"referer_url\": row[1],\n", "            \"user_ip\": row[2], \n", "            \"user_agent\": row[3],\n", "            \"create_time\": row[4],\n", "            \"query\": row[5],\n", "            \"city\": row[6],\n", "            \"page_size\": row[7],\n", "            \"prefered_version\": row[8],\n", "            \"comment\": row[9],\n", "        }\n", "        records.append(record)\n", "    logging.info(f\"Total {len(records)} records fetched\")\n", "    return records"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import logging\n", "from flask import Flask, request\n", "import random\n", "from flask import make_response\n", "from networkx import modularity_spectrum\n", "\n", "app = Flask(__name__)\n", "\n", "import hashlib\n", "from datetime import datetime\n", "from urllib.parse import urlparse, parse_qs\n", "from flask import render_template\n", "\n", "default_page_size = 6\n", "\n", "\n", "@app.route(\"/report\")\n", "def report():\n", "    stats = get_prefered_version_stats()\n", "    return render_template(\"report.html\", stats=stats)\n", "\n", "@app.route(\"/records\")\n", "def get_records():\n", "    records = get_all_arena_records()\n", "    return {\"records\": records}\n", "\n", "\n", "@app.route(\"/which-is-better\", methods=[\"POST\"])\n", "def which_is_better():\n", "    comment = \"\"\n", "    if request.is_json:\n", "        version = request.json.get(\"version\", \"\")\n", "        comment = request.json.get(\"comment\", \"\")\n", "    else:\n", "        version = request.form.get(\"version\", \"\")\n", "        comment = request.form.get(\"comment\", \"\")\n", "    if version not in [\n", "        \"new_embedding_search\",\n", "        \"old_es_search\",\n", "        \"both-are-poor\",\n", "        \"they-are-tied\",\n", "    ]:\n", "        return {\"error\": f\"Invalid version:{version}\"}, 400\n", "\n", "    referer_url = request.referrer\n", "    user_ip = request.remote_addr\n", "    user_agent = request.user_agent.string\n", "    userId = hashlib.sha256((user_ip + user_agent).encode()).hexdigest()\n", "\n", "    logging.info(\n", "        f\"user_id:{userId},referer_url:{referer_url}, which-is-better:{version}\"\n", "    )\n", "\n", "    query = \"\"\n", "    city = \"\"\n", "    page_size = default_page_size\n", "\n", "    if referer_url:\n", "        parsed_url = urlparse(referer_url)\n", "        query_params = parse_qs(parsed_url.query)\n", "        query = query_params.get(\"query\", [\"\"])[0]\n", "        city = query_params.get(\"city\", [\"\"])[0]\n", "        page_size = query_params.get(\"page_size\", [f\"{default_page_size}\"])[0]\n", "        try:\n", "            page_size = int(page_size)\n", "        except ValueError:\n", "            logging.error(f\"page_size:{page_size} is not a number\")\n", "            page_size = default_page_size\n", "\n", "    get_arena_db_connection_and_execute(\n", "        \"INSERT INTO search_arena_record VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)\",\n", "        (\n", "            userId,\n", "            referer_url,\n", "            user_ip,\n", "            user_agent,\n", "            datetime.now().strftime(\"%Y-%m-%d %H:%M:%S\"),\n", "            query,\n", "            city,\n", "            page_size,\n", "            version,\n", "            comment,\n", "        ),\n", "    )\n", "\n", "    return {\"message\": \"OK\", \"version\": version}\n", "\n", "\n", "@app.route(\"/\", methods=[\"GET\", \"POST\"])\n", "def search():\n", "    query = (\n", "        request.args.get(\"query\", \"\")\n", "        if request.method == \"GET\"\n", "        else request.form.get(\"query\", \"\")\n", "    )\n", "    page_size = (\n", "        request.args.get(\"page_size\", default_page_size, type=int)\n", "        if request.method == \"GET\"\n", "        else request.form.get(\"page_size\", default_page_size, type=int)\n", "    )\n", "    city = (\n", "        request.args.get(\"city\", \"杭州\")\n", "        if request.method == \"GET\"\n", "        else request.form.get(\"city\", \"杭州\")\n", "    )\n", "    results = []\n", "    results_from_es = []\n", "\n", "    is_left_side = False\n", "\n", "    if query:\n", "        logging.info(f\"Query: {query}\")\n", "        results = refine_items_with_category4(\n", "            item_list=search_items_with_query(\n", "                query=query, city=city, page_size=page_size\n", "            ),\n", "            query=query,\n", "        )\n", "        if isinstance(results, list):\n", "            results = results[:page_size]\n", "        else:\n", "            results = []\n", "\n", "        is_left_side = random.choice([True, False])\n", "        print(f\"is_left_side:{is_left_side}\")\n", "\n", "        results_from_es = search_xianmu_product(\n", "            query=query, city=city, page_size=page_size\n", "        )\n", "\n", "    response = make_response(\n", "        render_template(\n", "            \"search_arena.html\",\n", "            query=query,\n", "            city=city,\n", "            page_size=page_size,\n", "            top_20_query=top_20_query,\n", "            city_list=city_list,\n", "            top_high_click_index_20_query=top_high_click_index_20_query,\n", "            left_results=results if is_left_side else results_from_es,\n", "            right_results=results_from_es if is_left_side else results,\n", "            is_left_side=is_left_side,\n", "            last_n_days=last_n_days,\n", "            random_next_query=random.choice(random_query_list),\n", "        )\n", "    )\n", "    response.headers.update(\n", "        {\n", "            \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n", "            \"Pragma\": \"no-cache\",\n", "            \"Expires\": \"0\",\n", "        }\n", "    )\n", "    return response\n", "\n", "\n", "import argparse\n", "\n", "if __name__ == \"__main__\":\n", "    parser = argparse.ArgumentParser()\n", "    parser.add_argument(\"--port\", type=int, default=5600, help=\"port number\")\n", "    args, unknown = parser.parse_known_args()\n", "\n", "    # 开启debug模式以支持模板热重载\n", "    # app.jinja_env.auto_reload = True\n", "    # app.config['TEMPLATES_AUTO_RELOAD'] = True\n", "    app.run(debug=True, host=\"0.0.0.0\", port=args.port)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 2}