#!/bin/bash

# AI搜索评测系统启动脚本

echo "🔍 AI搜索质量评测系统"

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查依赖
if [ ! -f "requirements-eval.txt" ]; then
    echo "❌ requirements-eval.txt 文件缺失"
    exit 1
fi

# 安装依赖
echo "📦 检查并安装依赖..."
pip3 install -r requirements-eval.txt

# 检查环境变量
if [ ! -f ".env" ]; then
    echo "⚠️  未找到 .env 文件，使用示例文件..."
    if [ -f "evaluate_search_env.example" ]; then
        echo "请将 evaluate_search_env.example 重命名为 .env 并填入实际配置"
        cp evaluate_search_env.example .env
    fi
fi

# 检查搜索服务是否运行
curl -s 'http://localhost:5800/api/search-content?query=测试' > /dev/null
if [ $? -ne 0 ]; then
    echo "⚠️  搜索服务未运行，请先启动 flask 服务"
    echo "启动命令: python3 app.py"
    exit 1
fi

# 解析参数
USER_CTR=${1:-0.7}

echo "🎯 运行AI评测任务..."
echo "📊 CTR筛选阈值: $USER_CTR"

# 运行评测
python3 evaluate_search_queries.py --user-ctr "$USER_CTR"

echo "✅ 评测任务完成！查看生成的 markdown 报告文件"
echo "📖 查看完整使用指南: cat USAGE.md"
