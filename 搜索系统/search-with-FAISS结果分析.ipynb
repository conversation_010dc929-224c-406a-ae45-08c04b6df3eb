{"cells": [{"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [], "source": ["import requests\n", "import pandas as pd\n", "\n", "url = \"http://************:5500/records\"\n", "\n", "records = requests.get(url).json()[\"records\"]\n", "\n", "records_df = pd.DataFrame(records)\n", "records_df.head(2)\n", "\n", "records_df.to_csv(\"./全部用户反馈.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-12-06 12:17:03 - INFO - Tunnel session created: <InstanceDownloadSession id=202412061217030331f60b06efea5c project_name=summerfarm_ds_dev instance_id=20241206041648315gjypvq5ahcr4>\n", "2024-12-06 12:17:04 - INFO - sql:\n", "\n", "SELECT  query\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN cust_id END) AS searched_users\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN CONCAT(time,cust_id) END) AS search_cnt\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN CONCAT(time,cust_id) END) AS click_cnt\n", "        ,ROUND(AVG(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS avg_click_index\n", "        ,ROUND(MAX(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS max_click_index\n", "        ,ROUND(MIN(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS min_click_index\n", "        ,ROUND(STDDEV(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS std_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index\n", "FROM    summerfarm_tech.app_log_search_detail_di\n", "WHERE   ds BETWEEN '20241122' and '20241205'\n", "GROUP BY query\n", "ORDER BY searched_users DESC\n", ";\n", "\n", "columns:Index(['query', 'searched_users', 'search_cnt', 'click_cnt', 'avg_click_index',\n", "       'max_click_index', 'min_click_index', 'std_click_index',\n", "       'p50_click_index', 'p75_click_index', 'p90_click_index'],\n", "      dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>searched_users</th>\n", "      <th>search_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>avg_click_index</th>\n", "      <th>max_click_index</th>\n", "      <th>min_click_index</th>\n", "      <th>std_click_index</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>草莓</td>\n", "      <td>7888</td>\n", "      <td>82062</td>\n", "      <td>25759</td>\n", "      <td>5.7</td>\n", "      <td>112.0</td>\n", "      <td>0.0</td>\n", "      <td>7.4</td>\n", "      <td>4.0</td>\n", "      <td>7.0</td>\n", "      <td>13.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>芒果</td>\n", "      <td>6789</td>\n", "      <td>69025</td>\n", "      <td>17838</td>\n", "      <td>7.0</td>\n", "      <td>116.0</td>\n", "      <td>0.0</td>\n", "      <td>7.6</td>\n", "      <td>5.0</td>\n", "      <td>10.0</td>\n", "      <td>15.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>牛奶</td>\n", "      <td>4528</td>\n", "      <td>36549</td>\n", "      <td>10094</td>\n", "      <td>14.3</td>\n", "      <td>172.0</td>\n", "      <td>0.0</td>\n", "      <td>13.3</td>\n", "      <td>10.0</td>\n", "      <td>20.0</td>\n", "      <td>29.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>安佳</td>\n", "      <td>3973</td>\n", "      <td>19402</td>\n", "      <td>6461</td>\n", "      <td>6.2</td>\n", "      <td>104.0</td>\n", "      <td>0.0</td>\n", "      <td>7.3</td>\n", "      <td>4.0</td>\n", "      <td>8.0</td>\n", "      <td>16.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>蓝莓</td>\n", "      <td>3380</td>\n", "      <td>15967</td>\n", "      <td>6326</td>\n", "      <td>2.6</td>\n", "      <td>93.0</td>\n", "      <td>0.0</td>\n", "      <td>4.8</td>\n", "      <td>2.0</td>\n", "      <td>3.0</td>\n", "      <td>5.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  query  searched_users  search_cnt  click_cnt  avg_click_index  \\\n", "0    草莓            7888       82062      25759              5.7   \n", "1    芒果            6789       69025      17838              7.0   \n", "2    牛奶            4528       36549      10094             14.3   \n", "3    安佳            3973       19402       6461              6.2   \n", "4    蓝莓            3380       15967       6326              2.6   \n", "\n", "   max_click_index  min_click_index  std_click_index  p50_click_index  \\\n", "0            112.0              0.0              7.4              4.0   \n", "1            116.0              0.0              7.6              5.0   \n", "2            172.0              0.0             13.3             10.0   \n", "3            104.0              0.0              7.3              4.0   \n", "4             93.0              0.0              4.8              2.0   \n", "\n", "   p75_click_index  p90_click_index  \n", "0              7.0             13.0  \n", "1             10.0             15.0  \n", "2             20.0             29.0  \n", "3              8.0             16.0  \n", "4              3.0              5.0  "]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "\n", "# Add the scripts directory to the sys.path\n", "sys.path.append(\"../\")\n", "\n", "from odps_client import get_odps_sql_result_as_df\n", "from datetime import datetime, timedelta\n", "\n", "last_n_days = 14\n", "\n", "ds_yesterday = (datetime.now() - timedelta(days=1)).strftime(\"%Y%m%d\")\n", "last_n_days_ago = (datetime.now() - timedelta(days=last_n_days)).strftime(\"%Y%m%d\")\n", "\n", "top_query = f\"\"\"\n", "SELECT  query\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN cust_id END) AS searched_users\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN CONCAT(time,cust_id) END) AS search_cnt\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN CONCAT(time,cust_id) END) AS click_cnt\n", "        ,ROUND(AVG(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS avg_click_index\n", "        ,ROUND(MAX(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS max_click_index\n", "        ,ROUND(MIN(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS min_click_index\n", "        ,ROUND(STDDEV(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS std_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index\n", "FROM    summerfarm_tech.app_log_search_detail_di\n", "WHERE   ds BETWEEN '{last_n_days_ago}' and '{ds_yesterday}'\n", "GROUP BY query\n", "ORDER BY searched_users DESC\n", ";\n", "\"\"\"\n", "\n", "top_query_df = get_odps_sql_result_as_df(sql=top_query)\n", "top_query_df.head(5)"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "分位数统计:\n", "count    12604.000000\n", "mean        74.735243\n", "std       1149.856023\n", "min          0.000000\n", "25%          2.000000\n", "50%          4.000000\n", "75%         13.000000\n", "90%         54.000000\n", "95%        141.000000\n", "99%       1010.940000\n", "99.5%     1969.475000\n", "99.9%     7340.654000\n", "max      82062.000000\n", "Name: search_cnt, dtype: float64\n"]}], "source": ["import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import numpy as np\n", "\n", "# 设置中文字体显示 - Mac系统\n", "plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']  # Mac系统使用Arial Unicode MS字体\n", "plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号\n", "\n", "# 创建图形\n", "plt.figure(figsize=(12, 6))\n", "\n", "# 计算累积分布\n", "search_cnt_sorted = np.sort(top_query_df['search_cnt'].values)\n", "cumulative = np.arange(1, len(search_cnt_sorted) + 1) / len(search_cnt_sorted)\n", "\n", "# 使用对数坐标轴绘制累积分布图\n", "plt.semilogx(search_cnt_sorted, cumulative, 'b-', label='累积分布')\n", "\n", "# 添加一些统计信息\n", "percentiles = [25, 50, 75, 90, 95, 99, 99.9, 99.99]\n", "for p in percentiles:\n", "    value = np.percentile(search_cnt_sorted, p)\n", "    plt.axvline(x=value, linestyle='--', alpha=0.3)\n", "    plt.text(value, 0.5, f'P{p}={int(value)}', rotation=90)\n", "\n", "# 设置图表属性\n", "plt.xlabel('搜索次数 (search_cnt) - 对数尺度')\n", "plt.ylabel('累积比例')\n", "plt.title('搜索次数的累积分布图(对数尺度)')\n", "plt.grid(True, alpha=0.3)\n", "plt.legend()\n", "\n", "# 添加一些基本统计信息\n", "stats_text = f\"\"\"\n", "统计信息:\n", "平均值: {int(top_query_df['search_cnt'].mean())}\n", "中位数: {int(top_query_df['search_cnt'].median())}\n", "最大值: {int(top_query_df['search_cnt'].max())}\n", "最小值: {int(top_query_df['search_cnt'].min())}\n", "\"\"\"\n", "plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, \n", "         verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "\n", "plt.show()\n", "\n", "# 打印具体的分位数值\n", "print(\"\\n分位数统计:\")\n", "print(top_query_df['search_cnt'].describe(percentiles=[.25, .5, .75, .9, .95, .99, .995, .999]))"]}, {"cell_type": "code", "execution_count": 95, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["unique_queries:12604\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>query搜索次数</th>\n", "      <th>query搜索次数占比%</th>\n", "      <th>累计搜索次数占比%</th>\n", "      <th>累积query个数</th>\n", "      <th>query个数占比%</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>50.0%</th>\n", "      <td>安德鲁</td>\n", "      <td>3287</td>\n", "      <td>0.35</td>\n", "      <td>50.15</td>\n", "      <td>40</td>\n", "      <td>0.32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75.0%</th>\n", "      <td>低筋</td>\n", "      <td>472</td>\n", "      <td>0.05</td>\n", "      <td>75.02</td>\n", "      <td>252</td>\n", "      <td>2.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90.0%</th>\n", "      <td>桔</td>\n", "      <td>64</td>\n", "      <td>0.01</td>\n", "      <td>90.00</td>\n", "      <td>1131</td>\n", "      <td>8.97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>95.0%</th>\n", "      <td>低</td>\n", "      <td>20</td>\n", "      <td>0.00</td>\n", "      <td>95.00</td>\n", "      <td>2480</td>\n", "      <td>19.68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99.0%</th>\n", "      <td>丸菱</td>\n", "      <td>3</td>\n", "      <td>0.00</td>\n", "      <td>99.00</td>\n", "      <td>8246</td>\n", "      <td>65.42</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99.5%</th>\n", "      <td>普通干</td>\n", "      <td>2</td>\n", "      <td>0.00</td>\n", "      <td>99.50</td>\n", "      <td>9958</td>\n", "      <td>79.01</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      query  query搜索次数  query搜索次数占比%  累计搜索次数占比%  累积query个数  query个数占比%\n", "50.0%   安德鲁       3287          0.35      50.15         40        0.32\n", "75.0%    低筋        472          0.05      75.02        252        2.00\n", "90.0%     桔         64          0.01      90.00       1131        8.97\n", "95.0%     低         20          0.00      95.00       2480       19.68\n", "99.0%    丸菱          3          0.00      99.00       8246       65.42\n", "99.5%   普通干          2          0.00      99.50       9958       79.01"]}, "metadata": {}, "output_type": "display_data"}], "source": ["total_search_cnt = top_query_df[\"search_cnt\"].sum()\n", "top_query_df.sort_values(by=[\"search_cnt\"], ascending=False, inplace=True)\n", "top_query_df[\"search_cnt_%\"] = top_query_df[\"search_cnt\"] * 1.00 / total_search_cnt\n", "# 计算累计百分比\n", "top_query_df[\"cumsum_%\"] = top_query_df[\"search_cnt_%\"].cumsum()\n", "\n", "# 创建一个用于存储累计搜索占比结果的列表\n", "cumulative_search_results = []\n", "\n", "# 计算不同累计百分比对应的记录\n", "percentiles = [0.5, 0.75, 0.9, 0.95, 0.99, 0.995]\n", "unique_queries = top_query_df['query'].nunique()  # 计算去重后的query个数\n", "print(f\"unique_queries:{unique_queries}\")\n", "\n", "for p in percentiles:\n", "    idx = (top_query_df[\"cumsum_%\"] >= p).idxmax()\n", "    result = {\n", "        \"query\": top_query_df.loc[idx, \"query\"],\n", "        \"query搜索次数\": top_query_df.loc[idx, \"search_cnt\"],\n", "        \"query搜索次数占比%\": round(top_query_df.loc[idx, \"search_cnt_%\"] * 100,2),\n", "        \"累计搜索次数占比%\": round(top_query_df.loc[idx, \"cumsum_%\"] * 100,2),\n", "        \"累积query个数\": len(top_query_df[top_query_df[\"search_cnt\"] >= top_query_df.loc[idx, \"search_cnt\"]]),\n", "        \"query个数占比%\": round(len(top_query_df[top_query_df[\"search_cnt\"] >= top_query_df.loc[idx, \"search_cnt\"]]) * 100.0 / unique_queries,2)\n", "    }\n", "    cumulative_search_results.append(result)\n", "\n", "# 将结果转换为DataFrame并展示\n", "cumulative_search_df = pd.DataFrame(cumulative_search_results)\n", "cumulative_search_df.index = [f\"{p*100}%\" for p in percentiles]\n", "display(cumulative_search_df)"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "累计点击占比达到80.0%时的记录:\n", "query              蓝莓果酱\n", "click_cnt           117\n", "click_cnt_%    0.000407\n", "cumsum_%       0.800077\n", "Name: 275, dtype: object\n", "\n", "累计点击占比达到90.0%时的记录:\n", "query              荔枝罐头\n", "click_cnt            32\n", "click_cnt_%    0.000111\n", "cumsum_%       0.900094\n", "Name: 1082, dtype: object\n", "\n", "累计点击占比达到95.0%时的记录:\n", "query          伯爵法式羊角专用粉T45\n", "click_cnt                10\n", "click_cnt_%        0.000035\n", "cumsum_%           0.950011\n", "Name: 1379, dtype: object\n", "\n", "累计点击占比达到99.0%时的记录:\n", "query                妙可\n", "click_cnt             2\n", "click_cnt_%    0.000007\n", "cumsum_%       0.990001\n", "Name: 1450, dtype: object\n", "\n", "累计点击占比达到99.5%时的记录:\n", "query              碧根骨碎\n", "click_cnt             1\n", "click_cnt_%    0.000003\n", "cumsum_%          0.995\n", "Name: 10064, dtype: object\n"]}], "source": ["total_click_cnt = top_query_df[\"click_cnt\"].sum()\n", "top_query_click_cumsum_df = top_query_df.sort_values(by=\"click_cnt\", ascending=False)[\n", "    [\"query\", \"search_cnt\", \"click_cnt\", \"search_cnt_%\"]\n", "]\n", "\n", "top_query_click_cumsum_df[\"click_cnt_%\"] = top_query_click_cumsum_df[\"click_cnt\"] * 1.00 / total_click_cnt\n", "# 计算累计百分比\n", "top_query_click_cumsum_df[\"cumsum_%\"] = top_query_click_cumsum_df[\"click_cnt_%\"].cumsum()\n", "\n", "# 打印不同累计百分比对应的记录\n", "percentiles = [0.8, 0.9, 0.95, 0.99, 0.995]\n", "for p in percentiles:\n", "    idx = (top_query_click_cumsum_df[\"cumsum_%\"] >= p).idxmax()\n", "    print(f\"\\n累计点击占比达到{p*100}%时的记录:\")\n", "    print(top_query_click_cumsum_df.loc[idx, [\"query\", \"click_cnt\", \"click_cnt_%\", \"cumsum_%\"]])"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["有点击的query的ctr分布: 0.10    0.1186\n", "0.25    0.2000\n", "0.50    0.3333\n", "0.75    0.5122\n", "0.90    1.0000\n", "0.95    1.0000\n", "Name: ctr, dtype: float64\n"]}], "source": ["# 热门的top search term\n", "top_query_cnt_to_display = 50\n", "top_query_df[\"ctr\"] = round(\n", "    top_query_df[\"click_cnt\"] * 1.00 / top_query_df[\"search_cnt\"], 4\n", ")\n", "top_20_query_df = top_query_df.head(top_query_cnt_to_display)\n", "top_20_query = [item.to_dict() for _, item in top_20_query_df.iterrows()]\n", "\n", "print(\n", "    f\"有点击的query的ctr分布:\",\n", "    top_query_df[top_query_df[\"click_cnt\"] > 0][\"ctr\"].quantile(\n", "        [0.10, 0.25, 0.5, 0.75, 0.90, 0.95]\n", "    ),\n", ")\n", "\n", "# 低点击率的top search term\n", "top_high_click_index_20_query_df = top_query_df[\n", "    top_query_df[\"searched_users\"] > 10\n", "].sort_values(by=[\"search_cnt\"], ascending=False)\n", "\n", "# Fix the boolean indexing by using & instead of or\n", "top_high_click_index_20_query_df = top_high_click_index_20_query_df[\n", "    (top_high_click_index_20_query_df[\"min_click_index\"] > 6) | \n", "    (top_high_click_index_20_query_df[\"ctr\"] <= 0.1)\n", "].head(top_query_cnt_to_display)\n", "\n", "top_high_click_index_20_query = [\n", "    item.to_dict() for _, item in top_high_click_index_20_query_df.iterrows()\n", "]"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-12-06 12:17:31 - INFO - Tunnel session created: <InstanceDownloadSession id=202412061217319f31f60b06f0fb5d project_name=summerfarm_ds_dev instance_id=20241206041704866g9pfu8cnyn2>\n", "2024-12-06 12:17:31 - INFO - sql:\n", "\n", "SELECT  query\n", "        ,b.category4\n", "        ,b.category3\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN cust_id END) AS searched_users\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN CONCAT(time,cust_id) END) AS search_cnt\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN CONCAT(time,cust_id) END) AS click_cnt\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN cust_id END) AS click_users\n", "        ,ROUND(AVG(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS avg_click_index\n", "        ,ROUND(MAX(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS max_click_index\n", "        ,ROUND(MIN(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS min_click_index\n", "        ,ROUND(STDDEV(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS std_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.25) AS p25_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index\n", "        ,COUNT(DISTINCT a.ds) AS days_have_impression\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN a.ds END) AS days_have_click\n", "FROM    summerfarm_tech.app_log_search_detail_di a\n", "LEFT JOIN summerfarm_tech.dim_sku_df b\n", "ON      b.ds = MAX_PT('summerfarm_tech.dim_sku_df')\n", "AND     a.sku_id = b.sku_id\n", "WHERE   a.ds BETWEEN '20241122' and '20241205'\n", "GROUP BY query\n", "         ,b.category4\n", "         ,b.category3\n", "HAVING  click_cnt > 0 and click_users>2\n", "ORDER BY searched_users DESC\n", ";\n", "\n", "columns:Index(['query', 'category4', 'category3', 'searched_users', 'search_cnt',\n", "       'click_cnt', 'click_users', 'avg_click_index', 'max_click_index',\n", "       'min_click_index', 'std_click_index', 'p25_click_index',\n", "       'p50_click_index', 'p75_click_index', 'p90_click_index',\n", "       'days_have_impression', 'days_have_click'],\n", "      dtype='object')\n"]}], "source": ["category_prediction_query=f\"\"\"\n", "SELECT  query\n", "        ,b.category4\n", "        ,b.category3\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN cust_id END) AS searched_users\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN CONCAT(time,cust_id) END) AS search_cnt\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN CONCAT(time,cust_id) END) AS click_cnt\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN cust_id END) AS click_users\n", "        ,ROUND(AVG(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS avg_click_index\n", "        ,ROUND(MAX(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS max_click_index\n", "        ,ROUND(MIN(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS min_click_index\n", "        ,ROUND(STDDEV(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS std_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.25) AS p25_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index\n", "        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index\n", "        ,COUNT(DISTINCT a.ds) AS days_have_impression\n", "        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN a.ds END) AS days_have_click\n", "FROM    summerfarm_tech.app_log_search_detail_di a\n", "LEFT JOIN summerfarm_tech.dim_sku_df b\n", "ON      b.ds = MAX_PT('summerfarm_tech.dim_sku_df')\n", "AND     a.sku_id = b.sku_id\n", "WHERE   a.ds BETWEEN '{last_n_days_ago}' and '{ds_yesterday}'\n", "GROUP BY query\n", "         ,b.category4\n", "         ,b.category3\n", "HAVING  click_cnt > 0 and click_users>2\n", "ORDER BY searched_users DESC\n", ";\n", "\"\"\"\n", "\n", "category_prediction_df=get_odps_sql_result_as_df(category_prediction_query)"]}, {"cell_type": "code", "execution_count": 98, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.25     0.0\n", "0.50     0.0\n", "0.75     4.0\n", "0.80     5.0\n", "0.90    11.0\n", "0.95    19.0\n", "Name: min_click_index, dtype: float64\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/b3/9hcz86fx1_z_8m4121xwbs2h0000gn/T/ipykernel_5051/1127280811.py:39: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  category_prediction_sample_df.sort_values(\n", "/var/folders/b3/9hcz86fx1_z_8m4121xwbs2h0000gn/T/ipykernel_5051/1127280811.py:42: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  category_prediction_sample_df[\"category_percentile\"] = round(\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>category4</th>\n", "      <th>category3</th>\n", "      <th>click_cnt</th>\n", "      <th>category_rank</th>\n", "      <th>category_percentile</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1726</th>\n", "      <td>苹果</td>\n", "      <td>苹果</td>\n", "      <td>仁果类</td>\n", "      <td>617</td>\n", "      <td>1.000000</td>\n", "      <td>91.41</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1724</th>\n", "      <td>苹果</td>\n", "      <td>梨</td>\n", "      <td>仁果类</td>\n", "      <td>29</td>\n", "      <td>0.800000</td>\n", "      <td>4.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1725</th>\n", "      <td>苹果</td>\n", "      <td>浓缩果汁</td>\n", "      <td>果汁原料</td>\n", "      <td>10</td>\n", "      <td>0.500000</td>\n", "      <td>1.48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1723</th>\n", "      <td>苹果</td>\n", "      <td>果茶酱</td>\n", "      <td>水果风味制品</td>\n", "      <td>10</td>\n", "      <td>0.500000</td>\n", "      <td>1.48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1722</th>\n", "      <td>苹果</td>\n", "      <td>果汁</td>\n", "      <td>果蔬汁</td>\n", "      <td>9</td>\n", "      <td>0.200000</td>\n", "      <td>1.33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1678</th>\n", "      <td>芒果</td>\n", "      <td>芒果</td>\n", "      <td>核果类</td>\n", "      <td>16483</td>\n", "      <td>1.000000</td>\n", "      <td>93.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1673</th>\n", "      <td>芒果</td>\n", "      <td>冷冻果肉</td>\n", "      <td>冷冻水果</td>\n", "      <td>363</td>\n", "      <td>0.857143</td>\n", "      <td>2.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1674</th>\n", "      <td>芒果</td>\n", "      <td>果汁原浆</td>\n", "      <td>果汁原料</td>\n", "      <td>333</td>\n", "      <td>0.714286</td>\n", "      <td>1.90</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1675</th>\n", "      <td>芒果</td>\n", "      <td>果泥丨果茸</td>\n", "      <td>水果风味制品</td>\n", "      <td>180</td>\n", "      <td>0.571429</td>\n", "      <td>1.03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1676</th>\n", "      <td>芒果</td>\n", "      <td>果茶酱</td>\n", "      <td>水果风味制品</td>\n", "      <td>86</td>\n", "      <td>0.428571</td>\n", "      <td>0.49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1677</th>\n", "      <td>芒果</td>\n", "      <td>柠檬</td>\n", "      <td>柑果类</td>\n", "      <td>58</td>\n", "      <td>0.285714</td>\n", "      <td>0.33</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1672</th>\n", "      <td>芒果</td>\n", "      <td>冷冻果泥</td>\n", "      <td>冷冻水果</td>\n", "      <td>50</td>\n", "      <td>0.142857</td>\n", "      <td>0.28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1017</th>\n", "      <td>梨</td>\n", "      <td>梨</td>\n", "      <td>仁果类</td>\n", "      <td>1142</td>\n", "      <td>1.000000</td>\n", "      <td>100.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>710</th>\n", "      <td>山茶花</td>\n", "      <td>高筋面粉</td>\n", "      <td>面粉丨小麦粉</td>\n", "      <td>1011</td>\n", "      <td>1.000000</td>\n", "      <td>97.59</td>\n", "    </tr>\n", "    <tr>\n", "      <th>709</th>\n", "      <td>山茶花</td>\n", "      <td>绿茶</td>\n", "      <td>茶叶</td>\n", "      <td>10</td>\n", "      <td>0.750000</td>\n", "      <td>0.97</td>\n", "    </tr>\n", "    <tr>\n", "      <th>708</th>\n", "      <td>山茶花</td>\n", "      <td>红茶</td>\n", "      <td>茶叶</td>\n", "      <td>8</td>\n", "      <td>0.500000</td>\n", "      <td>0.77</td>\n", "    </tr>\n", "    <tr>\n", "      <th>707</th>\n", "      <td>山茶花</td>\n", "      <td>乌龙茶包</td>\n", "      <td>袋泡茶</td>\n", "      <td>7</td>\n", "      <td>0.250000</td>\n", "      <td>0.68</td>\n", "    </tr>\n", "    <tr>\n", "      <th>527</th>\n", "      <td>奶油</td>\n", "      <td>搅打型稀奶油</td>\n", "      <td>稀奶油</td>\n", "      <td>4949</td>\n", "      <td>1.000000</td>\n", "      <td>98.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>526</th>\n", "      <td>奶油</td>\n", "      <td>喷射型稀奶油</td>\n", "      <td>稀奶油</td>\n", "      <td>88</td>\n", "      <td>0.500000</td>\n", "      <td>1.75</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     query category4 category3  click_cnt  category_rank  category_percentile\n", "1726    苹果        苹果       仁果类        617       1.000000                91.41\n", "1724    苹果         梨       仁果类         29       0.800000                 4.30\n", "1725    苹果      浓缩果汁      果汁原料         10       0.500000                 1.48\n", "1723    苹果       果茶酱    水果风味制品         10       0.500000                 1.48\n", "1722    苹果        果汁       果蔬汁          9       0.200000                 1.33\n", "1678    芒果        芒果       核果类      16483       1.000000                93.90\n", "1673    芒果      冷冻果肉      冷冻水果        363       0.857143                 2.07\n", "1674    芒果      果汁原浆      果汁原料        333       0.714286                 1.90\n", "1675    芒果     果泥丨果茸    水果风味制品        180       0.571429                 1.03\n", "1676    芒果       果茶酱    水果风味制品         86       0.428571                 0.49\n", "1677    芒果        柠檬       柑果类         58       0.285714                 0.33\n", "1672    芒果      冷冻果泥      冷冻水果         50       0.142857                 0.28\n", "1017     梨         梨       仁果类       1142       1.000000               100.00\n", "710    山茶花      高筋面粉    面粉丨小麦粉       1011       1.000000                97.59\n", "709    山茶花        绿茶        茶叶         10       0.750000                 0.97\n", "708    山茶花        红茶        茶叶          8       0.500000                 0.77\n", "707    山茶花      乌龙茶包       袋泡茶          7       0.250000                 0.68\n", "527     奶油    搅打型稀奶油       稀奶油       4949       1.000000                98.25\n", "526     奶油    喷射型稀奶油       稀奶油         88       0.500000                 1.75"]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\n", "    category_prediction_df[\"min_click_index\"].quantile(\n", "        [0.25, 0.5, 0.75, 0.80, 0.90, 0.95]\n", "    )\n", ")\n", "\n", "category_prediction_valid_df = category_prediction_df[\n", "    (category_prediction_df[\"min_click_index\"] <= 6.0)\n", "    & (category_prediction_df[\"click_cnt\"] >= 7)\n", "]\n", "\n", "# Group by query and category4, calculate click counts and percentiles\n", "category_prediction_grouped = (\n", "    category_prediction_valid_df.groupby([\"query\", \"category4\", \"category3\"])\n", "    .agg({\"click_cnt\": \"sum\"})\n", "    .reset_index()\n", ")\n", "\n", "# Calculate percentile rank within each query group\n", "category_prediction_grouped[\"category_rank\"] = category_prediction_grouped.groupby(\n", "    \"query\"\n", ")[\"click_cnt\"].transform(lambda x: x.rank(pct=True))\n", "\n", "# Calculate click count ratio within each query group\n", "category_prediction_grouped[\"category_percentile\"] = (\n", "    category_prediction_grouped.groupby(\"query\")[\"click_cnt\"].transform(\n", "        lambda x: x / x.sum()\n", "    )\n", ")\n", "\n", "category_prediction_grouped.sort_values(by=[\"click_cnt\"], ascending=False, inplace=True)\n", "category_prediction_grouped[\"category_rank\"] = category_prediction_grouped[\n", "    \"category_rank\"\n", "].astype(float)\n", "\n", "category_prediction_sample_df = category_prediction_grouped[\n", "    category_prediction_grouped[\"query\"].isin([\"芒果\", \"奶油\", \"苹果\", \"梨\", \"山茶花\"])\n", "]\n", "category_prediction_sample_df.sort_values(\n", "    by=[\"query\", \"category_percentile\"], ascending=False, inplace=True\n", ")\n", "category_prediction_sample_df[\"category_percentile\"] = round(\n", "    category_prediction_sample_df[\"category_percentile\"] * 100.0, 2\n", ")\n", "category_prediction_sample_df"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1619\n"]}], "source": ["category_prediction_map = {}\n", "\n", "for _, row in category_prediction_grouped.iterrows():\n", "    query = row[\"query\"]\n", "    category4 = row[\"category4\"]\n", "    category_rank = row[\"category_rank\"]\n", "    if query not in category_prediction_map:\n", "        category_prediction_map[query] = {}\n", "    category_prediction_map[query][category4] = category_rank\n", "\n", "print(len(category_prediction_map))"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>category4</th>\n", "      <th>category3</th>\n", "      <th>click_cnt</th>\n", "      <th>category_rank</th>\n", "      <th>category_percentile</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1751</th>\n", "      <td>草莓</td>\n", "      <td>草莓</td>\n", "      <td>浆果类</td>\n", "      <td>23582</td>\n", "      <td>1.0</td>\n", "      <td>0.922036</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1678</th>\n", "      <td>芒果</td>\n", "      <td>芒果</td>\n", "      <td>核果类</td>\n", "      <td>16483</td>\n", "      <td>1.0</td>\n", "      <td>0.939042</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1320</th>\n", "      <td>牛奶</td>\n", "      <td>常温牛奶</td>\n", "      <td>液体乳</td>\n", "      <td>9084</td>\n", "      <td>1.0</td>\n", "      <td>0.919526</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1828</th>\n", "      <td>蓝莓</td>\n", "      <td>蓝莓</td>\n", "      <td>浆果类</td>\n", "      <td>5902</td>\n", "      <td>1.0</td>\n", "      <td>0.939360</td>\n", "    </tr>\n", "    <tr>\n", "      <th>963</th>\n", "      <td>柠檬</td>\n", "      <td>柠檬</td>\n", "      <td>柑果类</td>\n", "      <td>5393</td>\n", "      <td>1.0</td>\n", "      <td>0.988997</td>\n", "    </tr>\n", "    <tr>\n", "      <th>579</th>\n", "      <td>安佳</td>\n", "      <td>搅打型稀奶油</td>\n", "      <td>稀奶油</td>\n", "      <td>5155</td>\n", "      <td>1.0</td>\n", "      <td>0.803210</td>\n", "    </tr>\n", "    <tr>\n", "      <th>527</th>\n", "      <td>奶油</td>\n", "      <td>搅打型稀奶油</td>\n", "      <td>稀奶油</td>\n", "      <td>4949</td>\n", "      <td>1.0</td>\n", "      <td>0.982529</td>\n", "    </tr>\n", "    <tr>\n", "      <th>620</th>\n", "      <td>安佳淡奶油</td>\n", "      <td>搅打型稀奶油</td>\n", "      <td>稀奶油</td>\n", "      <td>4092</td>\n", "      <td>1.0</td>\n", "      <td>0.998292</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1330</th>\n", "      <td>牛油果</td>\n", "      <td>牛油果</td>\n", "      <td>核果类</td>\n", "      <td>3300</td>\n", "      <td>1.0</td>\n", "      <td>0.924629</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1994</th>\n", "      <td>铁塔</td>\n", "      <td>搅打型稀奶油</td>\n", "      <td>稀奶油</td>\n", "      <td>2872</td>\n", "      <td>1.0</td>\n", "      <td>0.951939</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1259</th>\n", "      <td>火龙果</td>\n", "      <td>火龙果</td>\n", "      <td>浆果类</td>\n", "      <td>2817</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1204</th>\n", "      <td>淡奶油</td>\n", "      <td>搅打型稀奶油</td>\n", "      <td>稀奶油</td>\n", "      <td>2584</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2207</th>\n", "      <td>黄油</td>\n", "      <td>无盐黄油</td>\n", "      <td>黄油</td>\n", "      <td>2333</td>\n", "      <td>1.0</td>\n", "      <td>0.573500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1100</th>\n", "      <td>橙子</td>\n", "      <td>橙</td>\n", "      <td>柑果类</td>\n", "      <td>2188</td>\n", "      <td>1.0</td>\n", "      <td>0.983371</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1665</th>\n", "      <td>芋泥</td>\n", "      <td>冷冻熟蔬菜制品</td>\n", "      <td>冷冻蔬菜</td>\n", "      <td>2160</td>\n", "      <td>1.0</td>\n", "      <td>0.964716</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1873</th>\n", "      <td>蛋糕</td>\n", "      <td>熟制冷冻面团</td>\n", "      <td>冷冻面团</td>\n", "      <td>2080</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1561</th>\n", "      <td>纯牛奶</td>\n", "      <td>常温牛奶</td>\n", "      <td>液体乳</td>\n", "      <td>1954</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>508</th>\n", "      <td>奥利奥</td>\n", "      <td>饼干碎</td>\n", "      <td>饼干</td>\n", "      <td>1841</td>\n", "      <td>1.0</td>\n", "      <td>0.957358</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2123</th>\n", "      <td>马斯卡彭</td>\n", "      <td>马斯卡彭</td>\n", "      <td>奶酪丨芝士</td>\n", "      <td>1712</td>\n", "      <td>1.0</td>\n", "      <td>0.899160</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1657</th>\n", "      <td>芋圆</td>\n", "      <td>芋圆</td>\n", "      <td>粉圆类配料</td>\n", "      <td>1681</td>\n", "      <td>1.0</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      query category4 category3  click_cnt  category_rank  category_percentile\n", "1751     草莓        草莓       浆果类      23582            1.0             0.922036\n", "1678     芒果        芒果       核果类      16483            1.0             0.939042\n", "1320     牛奶      常温牛奶       液体乳       9084            1.0             0.919526\n", "1828     蓝莓        蓝莓       浆果类       5902            1.0             0.939360\n", "963      柠檬        柠檬       柑果类       5393            1.0             0.988997\n", "579      安佳    搅打型稀奶油       稀奶油       5155            1.0             0.803210\n", "527      奶油    搅打型稀奶油       稀奶油       4949            1.0             0.982529\n", "620   安佳淡奶油    搅打型稀奶油       稀奶油       4092            1.0             0.998292\n", "1330    牛油果       牛油果       核果类       3300            1.0             0.924629\n", "1994     铁塔    搅打型稀奶油       稀奶油       2872            1.0             0.951939\n", "1259    火龙果       火龙果       浆果类       2817            1.0             1.000000\n", "1204    淡奶油    搅打型稀奶油       稀奶油       2584            1.0             1.000000\n", "2207     黄油      无盐黄油        黄油       2333            1.0             0.573500\n", "1100     橙子         橙       柑果类       2188            1.0             0.983371\n", "1665     芋泥   冷冻熟蔬菜制品      冷冻蔬菜       2160            1.0             0.964716\n", "1873     蛋糕    熟制冷冻面团      冷冻面团       2080            1.0             1.000000\n", "1561    纯牛奶      常温牛奶       液体乳       1954            1.0             1.000000\n", "508     奥利奥       饼干碎        饼干       1841            1.0             0.957358\n", "2123   马斯卡彭      马斯卡彭     奶酪丨芝士       1712            1.0             0.899160\n", "1657     芋圆        芋圆     粉圆类配料       1681            1.0             1.000000"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["category_prediction_grouped_result = category_prediction_grouped[\n", "    category_prediction_grouped[\"category_percentile\"] > 0.5\n", "]\n", "\n", "category_prediction_grouped_result.head(20)"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>city</th>\n", "      <th>comment</th>\n", "      <th>create_time</th>\n", "      <th>page_size</th>\n", "      <th>prefered_version</th>\n", "      <th>query</th>\n", "      <th>referer_url</th>\n", "      <th>userId</th>\n", "      <th>user_agent</th>\n", "      <th>user_ip</th>\n", "      <th>category4</th>\n", "      <th>category3</th>\n", "      <th>click_cnt</th>\n", "      <th>category_rank</th>\n", "      <th>category_percentile</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-27 16:06:10</td>\n", "      <td>30</td>\n", "      <td>old_es_search</td>\n", "      <td>圆月</td>\n", "      <td>http://************:5500/?query=%E5%9C%86%E6%9C%88&amp;page_size=30&amp;city=%E6%9D%AD%E5%B7%9E</td>\n", "      <td>d90a757470c512646faa6b3f4a527caaf5b17b317ef2755adf52248e43df314f</td>\n", "      <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</td>\n", "      <td>*************</td>\n", "      <td>熟制冷冻面团</td>\n", "      <td>冷冻面团</td>\n", "      <td>7.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-27 15:53:33</td>\n", "      <td>30</td>\n", "      <td>new_embedding_search</td>\n", "      <td>蛋糕</td>\n", "      <td>http://************:5500/?query=%E8%9B%8B%E7%B3%95&amp;page_size=30&amp;city=%E6%9D%AD%E5%B7%9E</td>\n", "      <td>d90a757470c512646faa6b3f4a527caaf5b17b317ef2755adf52248e43df314f</td>\n", "      <td>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</td>\n", "      <td>*************</td>\n", "      <td>熟制冷冻面团</td>\n", "      <td>冷冻面团</td>\n", "      <td>2080.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  city comment          create_time  page_size      prefered_version query  \\\n", "0   杭州          2024-11-27 16:06:10         30         old_es_search    圆月   \n", "1   杭州          2024-11-27 15:53:33         30  new_embedding_search    蛋糕   \n", "\n", "                                                                               referer_url  \\\n", "0  http://************:5500/?query=%E5%9C%86%E6%9C%88&page_size=30&city=%E6%9D%AD%E5%B7%9E   \n", "1  http://************:5500/?query=%E8%9B%8B%E7%B3%95&page_size=30&city=%E6%9D%AD%E5%B7%9E   \n", "\n", "                                                             userId  \\\n", "0  d90a757470c512646faa6b3f4a527caaf5b17b317ef2755adf52248e43df314f   \n", "1  d90a757470c512646faa6b3f4a527caaf5b17b317ef2755adf52248e43df314f   \n", "\n", "                                                                                                                      user_agent  \\\n", "0  Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********   \n", "1  Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********   \n", "\n", "         user_ip category4 category3  click_cnt  category_rank  \\\n", "0  *************    熟制冷冻面团      冷冻面团        7.0            1.0   \n", "1  *************    熟制冷冻面团      冷冻面团     2080.0            1.0   \n", "\n", "   category_percentile  \n", "0                  1.0  \n", "1                  1.0  "]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["records_with_category_df = records_df.merge(\n", "    category_prediction_grouped_result, on=\"query\", how=\"left\", suffixes=(\"\", \"_y\")\n", ")\n", "records_with_category_df.head(2)"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["records_with_category_df = records_with_category_df.merge(\n", "    top_query_df, on=\"query\", how=\"left\", suffixes=(\"\", \"_y\")\n", ")\n", "records_with_category_df[\n", "    [\n", "        \"city\",\n", "        \"query\",\n", "        \"prefered_version\",\n", "        \"userId\",\n", "        \"category4\",\n", "        \"search_cnt\",\n", "        \"avg_click_index\",\n", "        \"p75_click_index\",\n", "        \"ctr\",\n", "    ]\n", "].head(20)\n", "\n", "\n", "def map_version(x):\n", "    if x == \"old_es_search\":\n", "        return \"旧版本\"\n", "    elif x == \"new_embedding_search\":\n", "        return \"新版本\"\n", "    elif x == \"they-are-tied\":\n", "        return \"差不多\"\n", "    elif x == \"both-are-poor\":\n", "        return \"都很差\"\n", "    return x\n", "\n", "\n", "records_with_category_df[\"prefered_version\"] = records_with_category_df[\n", "    \"prefered_version\"\n", "].apply(map_version)"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>search_cnt</th>\n", "      <th>avg_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>ctr</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>125.000000</td>\n", "      <td>124.000000</td>\n", "      <td>124.000000</td>\n", "      <td>125.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1627.024000</td>\n", "      <td>7.158065</td>\n", "      <td>9.217742</td>\n", "      <td>0.306993</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>7906.537463</td>\n", "      <td>6.992703</td>\n", "      <td>11.264826</td>\n", "      <td>0.160264</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>13.000000</td>\n", "      <td>0.100000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>63.000000</td>\n", "      <td>1.900000</td>\n", "      <td>2.000000</td>\n", "      <td>0.179800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>143.000000</td>\n", "      <td>5.100000</td>\n", "      <td>5.000000</td>\n", "      <td>0.307700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>425.000000</td>\n", "      <td>11.025000</td>\n", "      <td>15.000000</td>\n", "      <td>0.416700</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>82062.000000</td>\n", "      <td>47.300000</td>\n", "      <td>93.250000</td>\n", "      <td>0.707800</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         search_cnt  avg_click_index  p75_click_index         ctr\n", "count    125.000000       124.000000       124.000000  125.000000\n", "mean    1627.024000         7.158065         9.217742    0.306993\n", "std     7906.537463         6.992703        11.264826    0.160264\n", "min       13.000000         0.100000         0.000000    0.000000\n", "25%       63.000000         1.900000         2.000000    0.179800\n", "50%      143.000000         5.100000         5.000000    0.307700\n", "75%      425.000000        11.025000        15.000000    0.416700\n", "max    82062.000000        47.300000        93.250000    0.707800"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["colums_to_show=['city','query','prefered_version','userId','category4','search_cnt','avg_click_index','p75_click_index','ctr']\n", "records_with_category_df[records_with_category_df['prefered_version']=='旧版本'][colums_to_show].describe()"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>search_cnt</th>\n", "      <th>avg_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>ctr</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>178.000000</td>\n", "      <td>177.000000</td>\n", "      <td>177.000000</td>\n", "      <td>178.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>1091.730337</td>\n", "      <td>10.816949</td>\n", "      <td>15.121469</td>\n", "      <td>0.281028</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3319.113987</td>\n", "      <td>13.864148</td>\n", "      <td>24.601455</td>\n", "      <td>0.166277</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>60.250000</td>\n", "      <td>2.300000</td>\n", "      <td>3.000000</td>\n", "      <td>0.121800</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>144.500000</td>\n", "      <td>6.000000</td>\n", "      <td>7.000000</td>\n", "      <td>0.269750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>369.000000</td>\n", "      <td>14.900000</td>\n", "      <td>20.000000</td>\n", "      <td>0.417600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>26814.000000</td>\n", "      <td>111.900000</td>\n", "      <td>243.250000</td>\n", "      <td>0.760000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         search_cnt  avg_click_index  p75_click_index         ctr\n", "count    178.000000       177.000000       177.000000  178.000000\n", "mean    1091.730337        10.816949        15.121469    0.281028\n", "std     3319.113987        13.864148        24.601455    0.166277\n", "min        1.000000         0.000000         0.000000    0.000000\n", "25%       60.250000         2.300000         3.000000    0.121800\n", "50%      144.500000         6.000000         7.000000    0.269750\n", "75%      369.000000        14.900000        20.000000    0.417600\n", "max    26814.000000       111.900000       243.250000    0.760000"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["records_with_category_df[records_with_category_df['prefered_version']=='新版本'][colums_to_show].describe()"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>category3</th>\n", "      <th>prefered_version</th>\n", "      <th>选择人次</th>\n", "      <th>query个数</th>\n", "      <th>query搜索总次数</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>None</td>\n", "      <td>新版本</td>\n", "      <td>51</td>\n", "      <td>45</td>\n", "      <td>6461</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>None</td>\n", "      <td>旧版本</td>\n", "      <td>26</td>\n", "      <td>24</td>\n", "      <td>9111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>None</td>\n", "      <td>都很差</td>\n", "      <td>22</td>\n", "      <td>19</td>\n", "      <td>3702</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>None</td>\n", "      <td>差不多</td>\n", "      <td>20</td>\n", "      <td>19</td>\n", "      <td>1620</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>稀奶油</td>\n", "      <td>新版本</td>\n", "      <td>16</td>\n", "      <td>12</td>\n", "      <td>80028</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>浆果类</td>\n", "      <td>差不多</td>\n", "      <td>13</td>\n", "      <td>10</td>\n", "      <td>218999</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>冷冻面团</td>\n", "      <td>新版本</td>\n", "      <td>12</td>\n", "      <td>10</td>\n", "      <td>40925</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>稀奶油</td>\n", "      <td>旧版本</td>\n", "      <td>10</td>\n", "      <td>10</td>\n", "      <td>15779</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>奶酪丨芝士</td>\n", "      <td>旧版本</td>\n", "      <td>9</td>\n", "      <td>9</td>\n", "      <td>7396</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>面粉丨小麦粉</td>\n", "      <td>新版本</td>\n", "      <td>9</td>\n", "      <td>7</td>\n", "      <td>4011</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>浆果类</td>\n", "      <td>新版本</td>\n", "      <td>8</td>\n", "      <td>8</td>\n", "      <td>10881</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>浆果类</td>\n", "      <td>旧版本</td>\n", "      <td>8</td>\n", "      <td>7</td>\n", "      <td>83131</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>奶酪丨芝士</td>\n", "      <td>差不多</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "      <td>16975</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>果汁原料</td>\n", "      <td>新版本</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>3356</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>柑果类</td>\n", "      <td>新版本</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>4560</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>水果风味制品</td>\n", "      <td>新版本</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>8311</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>稀奶油</td>\n", "      <td>差不多</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "      <td>16431</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>冷冻水果</td>\n", "      <td>新版本</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>6198</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>可可豆制品</td>\n", "      <td>旧版本</td>\n", "      <td>5</td>\n", "      <td>4</td>\n", "      <td>4988</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>柑果类</td>\n", "      <td>差不多</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>29369</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>面粉丨小麦粉</td>\n", "      <td>旧版本</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>1777</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>冷冻蔬菜</td>\n", "      <td>新版本</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>5673</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>奶酪丨芝士</td>\n", "      <td>新版本</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>3775</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>果冻类配料</td>\n", "      <td>新版本</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>2495</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>柑果类</td>\n", "      <td>旧版本</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>17907</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>核果类</td>\n", "      <td>新版本</td>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>703</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>水果风味制品</td>\n", "      <td>旧版本</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>1710</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>液体乳</td>\n", "      <td>差不多</td>\n", "      <td>4</td>\n", "      <td>3</td>\n", "      <td>36868</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>液体乳</td>\n", "      <td>旧版本</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>8567</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>炼乳</td>\n", "      <td>新版本</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>820</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   category3 prefered_version  选择人次  query个数  query搜索总次数\n", "0       None              新版本    51       45        6461\n", "1       None              旧版本    26       24        9111\n", "2       None              都很差    22       19        3702\n", "3       None              差不多    20       19        1620\n", "4        稀奶油              新版本    16       12       80028\n", "5        浆果类              差不多    13       10      218999\n", "6       冷冻面团              新版本    12       10       40925\n", "7        稀奶油              旧版本    10       10       15779\n", "8      奶酪丨芝士              旧版本     9        9        7396\n", "9     面粉丨小麦粉              新版本     9        7        4011\n", "10       浆果类              新版本     8        8       10881\n", "11       浆果类              旧版本     8        7       83131\n", "12     奶酪丨芝士              差不多     6        5       16975\n", "13      果汁原料              新版本     6        6        3356\n", "14       柑果类              新版本     6        6        4560\n", "15    水果风味制品              新版本     6        6        8311\n", "16       稀奶油              差不多     6        5       16431\n", "17      冷冻水果              新版本     5        5        6198\n", "18     可可豆制品              旧版本     5        4        4988\n", "19       柑果类              差不多     5        5       29369\n", "20    面粉丨小麦粉              旧版本     5        5        1777\n", "21      冷冻蔬菜              新版本     4        4        5673\n", "22     奶酪丨芝士              新版本     4        4        3775\n", "23     果冻类配料              新版本     4        4        2495\n", "24       柑果类              旧版本     4        4       17907\n", "25       核果类              新版本     4        3         703\n", "26    水果风味制品              旧版本     4        4        1710\n", "27       液体乳              差不多     4        3       36868\n", "28       液体乳              旧版本     4        4        8567\n", "29        炼乳              新版本     4        4         820"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandasql\n", "\n", "static_df=pandasql.sqldf(\"\"\"select category3,prefered_version,count(0)`选择人次`,count(distinct query) `query个数`,sum(search_cnt) `query搜索总次数`\n", "                         from records_with_category_df group by 1,2 order by `选择人次` desc\"\"\")\n", "static_df.head(30)"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>city</th>\n", "      <th>版本</th>\n", "      <th>选择人次</th>\n", "      <th>query个数</th>\n", "      <th>用户数</th>\n", "      <th>query在鲜沐商城的总搜索次数</th>\n", "      <th>query在鲜沐商城的总点击数</th>\n", "      <th>query在鲜沐商城的平均CTR</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>杭州</td>\n", "      <td>新版本</td>\n", "      <td>120</td>\n", "      <td>113</td>\n", "      <td>9</td>\n", "      <td>88633</td>\n", "      <td>21478.0</td>\n", "      <td>0.242325</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>杭州</td>\n", "      <td>旧版本</td>\n", "      <td>83</td>\n", "      <td>77</td>\n", "      <td>9</td>\n", "      <td>123826</td>\n", "      <td>35919.0</td>\n", "      <td>0.290076</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>杭州</td>\n", "      <td>差不多</td>\n", "      <td>56</td>\n", "      <td>54</td>\n", "      <td>6</td>\n", "      <td>125818</td>\n", "      <td>39503.0</td>\n", "      <td>0.313969</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>杭州</td>\n", "      <td>都很差</td>\n", "      <td>23</td>\n", "      <td>22</td>\n", "      <td>3</td>\n", "      <td>3108</td>\n", "      <td>500.0</td>\n", "      <td>0.160875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>广州</td>\n", "      <td>新版本</td>\n", "      <td>41</td>\n", "      <td>41</td>\n", "      <td>1</td>\n", "      <td>100604</td>\n", "      <td>23384.0</td>\n", "      <td>0.232436</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>广州</td>\n", "      <td>差不多</td>\n", "      <td>38</td>\n", "      <td>38</td>\n", "      <td>1</td>\n", "      <td>331494</td>\n", "      <td>95220.0</td>\n", "      <td>0.287245</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>广州</td>\n", "      <td>旧版本</td>\n", "      <td>21</td>\n", "      <td>21</td>\n", "      <td>1</td>\n", "      <td>72120</td>\n", "      <td>11133.0</td>\n", "      <td>0.154368</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>广州</td>\n", "      <td>都很差</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>1346</td>\n", "      <td>10.0</td>\n", "      <td>0.007429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>上海</td>\n", "      <td>旧版本</td>\n", "      <td>21</td>\n", "      <td>21</td>\n", "      <td>2</td>\n", "      <td>7432</td>\n", "      <td>3105.0</td>\n", "      <td>0.417788</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>上海</td>\n", "      <td>新版本</td>\n", "      <td>17</td>\n", "      <td>17</td>\n", "      <td>2</td>\n", "      <td>5091</td>\n", "      <td>1446.0</td>\n", "      <td>0.284031</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>上海</td>\n", "      <td>差不多</td>\n", "      <td>12</td>\n", "      <td>12</td>\n", "      <td>2</td>\n", "      <td>3785</td>\n", "      <td>1161.0</td>\n", "      <td>0.306737</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>上海</td>\n", "      <td>都很差</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>1269</td>\n", "      <td>111.0</td>\n", "      <td>0.087470</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   city   版本  选择人次  query个数  用户数  query在鲜沐商城的总搜索次数  query在鲜沐商城的总点击数  \\\n", "0    杭州  新版本   120      113    9             88633          21478.0   \n", "1    杭州  旧版本    83       77    9            123826          35919.0   \n", "2    杭州  差不多    56       54    6            125818          39503.0   \n", "5    杭州  都很差    23       22    3              3108            500.0   \n", "3    广州  新版本    41       41    1            100604          23384.0   \n", "4    广州  差不多    38       38    1            331494          95220.0   \n", "7    广州  旧版本    21       21    1             72120          11133.0   \n", "11   广州  都很差     5        5    1              1346             10.0   \n", "6    上海  旧版本    21       21    2              7432           3105.0   \n", "8    上海  新版本    17       17    2              5091           1446.0   \n", "9    上海  差不多    12       12    2              3785           1161.0   \n", "10   上海  都很差     6        6    1              1269            111.0   \n", "\n", "    query在鲜沐商城的平均CTR  \n", "0           0.242325  \n", "1           0.290076  \n", "2           0.313969  \n", "5           0.160875  \n", "3           0.232436  \n", "4           0.287245  \n", "7           0.154368  \n", "11          0.007429  \n", "6           0.417788  \n", "8           0.284031  \n", "9           0.306737  \n", "10          0.087470  "]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["static_df = pandasql.sqldf(\n", "    \"\"\"select city,prefered_version as 版本,count(0) 选择人次,count(distinct query) query个数,count(distinct userId) 用户数\n", "    ,sum(search_cnt) query在鲜沐商城的总搜索次数\n", "    ,sum(click_cnt) query在鲜沐商城的总点击数\n", "    ,sum(click_cnt)/sum(search_cnt) as query在鲜沐商城的平均CTR\n", "    from records_with_category_df group by 1,2 order by 选择人次 desc\"\"\"\n", ")\n", "static_df.sort_values(by=[\"city\", \"选择人次\"], ascending=False, inplace=True)\n", "static_df"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>版本</th>\n", "      <th>选择人次</th>\n", "      <th>query个数</th>\n", "      <th>选择用户数</th>\n", "      <th>query总搜索次数</th>\n", "      <th>query总点击数</th>\n", "      <th>query平均CTR</th>\n", "      <th>选择人次%</th>\n", "      <th>搜索次数%</th>\n", "      <th>点击次数%</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>新版本</td>\n", "      <td>178</td>\n", "      <td>162</td>\n", "      <td>11</td>\n", "      <td>194328</td>\n", "      <td>46308.0</td>\n", "      <td>0.238298</td>\n", "      <td>40.2</td>\n", "      <td>22.5</td>\n", "      <td>19.9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>旧版本</td>\n", "      <td>125</td>\n", "      <td>117</td>\n", "      <td>11</td>\n", "      <td>203378</td>\n", "      <td>50157.0</td>\n", "      <td>0.246620</td>\n", "      <td>28.2</td>\n", "      <td>23.5</td>\n", "      <td>21.5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>差不多</td>\n", "      <td>106</td>\n", "      <td>96</td>\n", "      <td>8</td>\n", "      <td>461097</td>\n", "      <td>135884.0</td>\n", "      <td>0.294697</td>\n", "      <td>23.9</td>\n", "      <td>53.3</td>\n", "      <td>58.3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>都很差</td>\n", "      <td>34</td>\n", "      <td>31</td>\n", "      <td>4</td>\n", "      <td>5723</td>\n", "      <td>621.0</td>\n", "      <td>0.108510</td>\n", "      <td>7.7</td>\n", "      <td>0.7</td>\n", "      <td>0.3</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    版本  选择人次  query个数  选择用户数  query总搜索次数  query总点击数  query平均CTR  选择人次%  搜索次数%  \\\n", "0  新版本   178      162     11      194328    46308.0    0.238298   40.2   22.5   \n", "1  旧<PERSON>本   125      117     11      203378    50157.0    0.246620   28.2   23.5   \n", "2  差不多   106       96      8      461097   135884.0    0.294697   23.9   53.3   \n", "3  都很差    34       31      4        5723      621.0    0.108510    7.7    0.7   \n", "\n", "   点击次数%  \n", "0   19.9  \n", "1   21.5  \n", "2   58.3  \n", "3    0.3  "]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["static_df = pandasql.sqldf(\n", "    \"\"\"select prefered_version 版本,count(0)选择人次,count(distinct query) query个数\n", "            ,count(distinct userId) 选择用户数\n", "            ,sum(search_cnt) query总搜索次数\n", "            ,sum(click_cnt) query总点击数\n", "            ,sum(click_cnt)/sum(search_cnt) as query平均CTR\n", "            from records_with_category_df group by 1 order by 选择人次 desc\"\"\"\n", ")\n", "total_cnt=static_df['选择人次'].sum()\n", "total_search_cnt=static_df['query总搜索次数'].sum()\n", "total_click_cnt=static_df['query总点击数'].sum()\n", "\n", "static_df['选择人次%']=round(static_df['选择人次']/total_cnt*100,1)\n", "static_df['搜索次数%']=round(static_df['query总搜索次数']/total_search_cnt*100,1)\n", "static_df['点击次数%']=round(static_df['query总点击数']/total_click_cnt*100,1)\n", "\n", "static_df"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>城市</th>\n", "      <th>吐槽</th>\n", "      <th>时间</th>\n", "      <th>页码大小</th>\n", "      <th>版本</th>\n", "      <th>query</th>\n", "      <th>搜索次数</th>\n", "      <th>query的ctr</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>424</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-22 11:58:11</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>草莓</td>\n", "      <td>82062</td>\n", "      <td>0.3139</td>\n", "    </tr>\n", "    <tr>\n", "      <th>363</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:09:45</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>黄油</td>\n", "      <td>27627</td>\n", "      <td>0.1623</td>\n", "    </tr>\n", "    <tr>\n", "      <th>68</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:44:15</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>柠檬</td>\n", "      <td>15926</td>\n", "      <td>0.3473</td>\n", "    </tr>\n", "    <tr>\n", "      <th>355</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:10:26</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>淡奶油</td>\n", "      <td>12347</td>\n", "      <td>0.2172</td>\n", "    </tr>\n", "    <tr>\n", "      <th>337</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:11:43</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>纯牛奶</td>\n", "      <td>7571</td>\n", "      <td>0.2685</td>\n", "    </tr>\n", "    <tr>\n", "      <th>323</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:12:46</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>芝士</td>\n", "      <td>4740</td>\n", "      <td>0.1443</td>\n", "    </tr>\n", "    <tr>\n", "      <th>309</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:13:43</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>芝士片</td>\n", "      <td>3844</td>\n", "      <td>0.1751</td>\n", "    </tr>\n", "    <tr>\n", "      <th>303</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:14:10</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>可可粉</td>\n", "      <td>3620</td>\n", "      <td>0.2006</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:40:45</td>\n", "      <td>24</td>\n", "      <td>旧版本</td>\n", "      <td>奥利奥</td>\n", "      <td>3595</td>\n", "      <td>0.5402</td>\n", "    </tr>\n", "    <tr>\n", "      <th>235</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:14:50</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>鸡蛋</td>\n", "      <td>3380</td>\n", "      <td>0.3962</td>\n", "    </tr>\n", "    <tr>\n", "      <th>190</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:23:41</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>鸡蛋</td>\n", "      <td>3380</td>\n", "      <td>0.3962</td>\n", "    </tr>\n", "    <tr>\n", "      <th>316</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:13:15</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>奶粉</td>\n", "      <td>2417</td>\n", "      <td>0.2098</td>\n", "    </tr>\n", "    <tr>\n", "      <th>154</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:31:07</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>芝士碎</td>\n", "      <td>1740</td>\n", "      <td>0.2339</td>\n", "    </tr>\n", "    <tr>\n", "      <th>421</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:02:07</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>车厘子</td>\n", "      <td>1696</td>\n", "      <td>0.0991</td>\n", "    </tr>\n", "    <tr>\n", "      <th>331</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:12:14</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>金桔</td>\n", "      <td>1638</td>\n", "      <td>0.3578</td>\n", "    </tr>\n", "    <tr>\n", "      <th>313</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:13:29</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>气泡水</td>\n", "      <td>1543</td>\n", "      <td>0.4634</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:50:34</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>动物奶油</td>\n", "      <td>1540</td>\n", "      <td>0.1935</td>\n", "    </tr>\n", "    <tr>\n", "      <th>238</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:14:03</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>马苏里拉</td>\n", "      <td>1511</td>\n", "      <td>0.2383</td>\n", "    </tr>\n", "    <tr>\n", "      <th>301</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:14:23</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>山茶花</td>\n", "      <td>1468</td>\n", "      <td>0.7078</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:27:27</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>巴斯克</td>\n", "      <td>1352</td>\n", "      <td>0.3521</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:52:02</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>砂糖</td>\n", "      <td>1314</td>\n", "      <td>0.4422</td>\n", "    </tr>\n", "    <tr>\n", "      <th>306</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:13:54</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>TS韩国幼砂糖</td>\n", "      <td>1103</td>\n", "      <td>0.5830</td>\n", "    </tr>\n", "    <tr>\n", "      <th>143</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:32:37</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>总统</td>\n", "      <td>1057</td>\n", "      <td>0.4588</td>\n", "    </tr>\n", "    <tr>\n", "      <th>177</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:26:41</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>澄善草莓果酱</td>\n", "      <td>1014</td>\n", "      <td>0.3047</td>\n", "    </tr>\n", "    <tr>\n", "      <th>78</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:42:34</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>红茶</td>\n", "      <td>889</td>\n", "      <td>0.3408</td>\n", "    </tr>\n", "    <tr>\n", "      <th>258</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 11:41:16</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>雀巢纯牛奶</td>\n", "      <td>685</td>\n", "      <td>0.4657</td>\n", "    </tr>\n", "    <tr>\n", "      <th>274</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:40:28</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>柠檬汁</td>\n", "      <td>593</td>\n", "      <td>0.3895</td>\n", "    </tr>\n", "    <tr>\n", "      <th>213</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:19:41</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>血糯米</td>\n", "      <td>570</td>\n", "      <td>0.5789</td>\n", "    </tr>\n", "    <tr>\n", "      <th>417</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:02:31</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>玉米淀粉</td>\n", "      <td>556</td>\n", "      <td>0.1169</td>\n", "    </tr>\n", "    <tr>\n", "      <th>174</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:27:00</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>红心火龙果</td>\n", "      <td>473</td>\n", "      <td>0.3890</td>\n", "    </tr>\n", "    <tr>\n", "      <th>413</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:02:53</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>脆啵啵</td>\n", "      <td>454</td>\n", "      <td>0.0771</td>\n", "    </tr>\n", "    <tr>\n", "      <th>218</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:18:43</td>\n", "      <td>18</td>\n", "      <td>旧版本</td>\n", "      <td>巧克力豆</td>\n", "      <td>425</td>\n", "      <td>0.2000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>79</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:42:30</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>巧克力豆</td>\n", "      <td>425</td>\n", "      <td>0.2000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>410</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:03:07</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>面包</td>\n", "      <td>389</td>\n", "      <td>0.0771</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:21:56</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>可可</td>\n", "      <td>357</td>\n", "      <td>0.2325</td>\n", "    </tr>\n", "    <tr>\n", "      <th>412</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:02:58</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>樱桃</td>\n", "      <td>353</td>\n", "      <td>0.1048</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:53:05</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>肯迪雅淡奶油</td>\n", "      <td>310</td>\n", "      <td>0.5194</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:51:28</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>澄善蓝莓果馅</td>\n", "      <td>291</td>\n", "      <td>0.3402</td>\n", "    </tr>\n", "    <tr>\n", "      <th>108</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:37:40</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>榴莲千层</td>\n", "      <td>290</td>\n", "      <td>0.3690</td>\n", "    </tr>\n", "    <tr>\n", "      <th>147</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:32:02</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>无盐黄油</td>\n", "      <td>286</td>\n", "      <td>0.2308</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:21:37</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>无盐黄油</td>\n", "      <td>286</td>\n", "      <td>0.2308</td>\n", "    </tr>\n", "    <tr>\n", "      <th>112</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:37:17</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>荔枝</td>\n", "      <td>273</td>\n", "      <td>0.4908</td>\n", "    </tr>\n", "    <tr>\n", "      <th>405</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:05:21</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>啵啵</td>\n", "      <td>253</td>\n", "      <td>0.0593</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:53:52</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>大米粉</td>\n", "      <td>237</td>\n", "      <td>0.3418</td>\n", "    </tr>\n", "    <tr>\n", "      <th>356</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:10:25</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>奶咖</td>\n", "      <td>235</td>\n", "      <td>0.0894</td>\n", "    </tr>\n", "    <tr>\n", "      <th>113</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:37:04</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>提子</td>\n", "      <td>231</td>\n", "      <td>0.2381</td>\n", "    </tr>\n", "    <tr>\n", "      <th>434</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-20 14:28:42</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>新希望</td>\n", "      <td>221</td>\n", "      <td>0.5973</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:39:16</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>乔艺</td>\n", "      <td>211</td>\n", "      <td>0.4882</td>\n", "    </tr>\n", "    <tr>\n", "      <th>194</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:23:13</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>椰蓉</td>\n", "      <td>210</td>\n", "      <td>0.3143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>171</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:27:27</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>小金桔</td>\n", "      <td>209</td>\n", "      <td>0.4689</td>\n", "    </tr>\n", "    <tr>\n", "      <th>168</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:28:04</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>蛋黄</td>\n", "      <td>201</td>\n", "      <td>0.2537</td>\n", "    </tr>\n", "    <tr>\n", "      <th>397</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:06:19</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>焦糖</td>\n", "      <td>196</td>\n", "      <td>0.1888</td>\n", "    </tr>\n", "    <tr>\n", "      <th>370</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:09:17</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>焦糖</td>\n", "      <td>196</td>\n", "      <td>0.1888</td>\n", "    </tr>\n", "    <tr>\n", "      <th>185</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:24:33</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>蜂蜜</td>\n", "      <td>186</td>\n", "      <td>0.2366</td>\n", "    </tr>\n", "    <tr>\n", "      <th>308</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:13:47</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>姜</td>\n", "      <td>174</td>\n", "      <td>0.0460</td>\n", "    </tr>\n", "    <tr>\n", "      <th>240</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:11:56</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>纽麦福</td>\n", "      <td>168</td>\n", "      <td>0.5833</td>\n", "    </tr>\n", "    <tr>\n", "      <th>158</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:30:35</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>白黄油</td>\n", "      <td>167</td>\n", "      <td>0.0898</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:23:16</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>菲诺</td>\n", "      <td>166</td>\n", "      <td>0.1386</td>\n", "    </tr>\n", "    <tr>\n", "      <th>327</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:12:27</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>蛋糕盒</td>\n", "      <td>165</td>\n", "      <td>0.1212</td>\n", "    </tr>\n", "    <tr>\n", "      <th>93</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:40:27</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>黑巧克力</td>\n", "      <td>161</td>\n", "      <td>0.2422</td>\n", "    </tr>\n", "    <tr>\n", "      <th>189</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:23:49</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>梨子</td>\n", "      <td>154</td>\n", "      <td>0.3312</td>\n", "    </tr>\n", "    <tr>\n", "      <th>262</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 11:25:37</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>坚果</td>\n", "      <td>153</td>\n", "      <td>0.1242</td>\n", "    </tr>\n", "    <tr>\n", "      <th>186</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:24:11</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>皇后</td>\n", "      <td>143</td>\n", "      <td>0.2028</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:47:06</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>芒果块</td>\n", "      <td>142</td>\n", "      <td>0.2676</td>\n", "    </tr>\n", "    <tr>\n", "      <th>84</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:41:54</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>大米</td>\n", "      <td>141</td>\n", "      <td>0.4468</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:50:00</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>咖啡大师</td>\n", "      <td>140</td>\n", "      <td>0.3929</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:22:16</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>海南子弹头香水柠檬</td>\n", "      <td>134</td>\n", "      <td>0.4552</td>\n", "    </tr>\n", "    <tr>\n", "      <th>104</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:38:50</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>椰子油</td>\n", "      <td>133</td>\n", "      <td>0.0827</td>\n", "    </tr>\n", "    <tr>\n", "      <th>393</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:06:45</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>魔客麦芬蛋糕</td>\n", "      <td>127</td>\n", "      <td>0.1890</td>\n", "    </tr>\n", "    <tr>\n", "      <th>439</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-20 14:04:39</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>椰子粉</td>\n", "      <td>118</td>\n", "      <td>0.1356</td>\n", "    </tr>\n", "    <tr>\n", "      <th>374</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:08:59</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>红枣</td>\n", "      <td>101</td>\n", "      <td>0.1386</td>\n", "    </tr>\n", "    <tr>\n", "      <th>228</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:16:33</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>番石榴</td>\n", "      <td>99</td>\n", "      <td>0.3232</td>\n", "    </tr>\n", "    <tr>\n", "      <th>144</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:32:28</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>味斯美3A海苔酥脆松</td>\n", "      <td>98</td>\n", "      <td>0.3776</td>\n", "    </tr>\n", "    <tr>\n", "      <th>146</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:32:12</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>红茶粉</td>\n", "      <td>96</td>\n", "      <td>0.2500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>86</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:41:42</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>罗勒</td>\n", "      <td>96</td>\n", "      <td>0.3333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>66</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:44:33</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>花生</td>\n", "      <td>94</td>\n", "      <td>0.1383</td>\n", "    </tr>\n", "    <tr>\n", "      <th>265</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 10:46:56</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>唯品</td>\n", "      <td>90</td>\n", "      <td>0.3889</td>\n", "    </tr>\n", "    <tr>\n", "      <th>157</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:30:41</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>沐清友</td>\n", "      <td>89</td>\n", "      <td>0.1798</td>\n", "    </tr>\n", "    <tr>\n", "      <th>169</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:27:38</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>沐清友</td>\n", "      <td>89</td>\n", "      <td>0.1798</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:22:13</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>红颜</td>\n", "      <td>88</td>\n", "      <td>0.3409</td>\n", "    </tr>\n", "    <tr>\n", "      <th>221</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:17:55</td>\n", "      <td>18</td>\n", "      <td>旧版本</td>\n", "      <td>悦焙友榴莲千层蛋糕</td>\n", "      <td>87</td>\n", "      <td>0.4483</td>\n", "    </tr>\n", "    <tr>\n", "      <th>378</th>\n", "      <td>广州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:08:38</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>片油</td>\n", "      <td>77</td>\n", "      <td>0.1429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>149</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:31:52</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>安佳干酪</td>\n", "      <td>77</td>\n", "      <td>0.3766</td>\n", "    </tr>\n", "    <tr>\n", "      <th>76</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:42:44</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>mg</td>\n", "      <td>75</td>\n", "      <td>0.3600</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:49:45</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>爱真38</td>\n", "      <td>73</td>\n", "      <td>0.5068</td>\n", "    </tr>\n", "    <tr>\n", "      <th>73</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:43:09</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>金像A高筋粉（纸袋）</td>\n", "      <td>72</td>\n", "      <td>0.5694</td>\n", "    </tr>\n", "    <tr>\n", "      <th>74</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:42:58</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>塔壳</td>\n", "      <td>72</td>\n", "      <td>0.1111</td>\n", "    </tr>\n", "    <tr>\n", "      <th>243</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:11:20</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>广东菠萝</td>\n", "      <td>70</td>\n", "      <td>0.5143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>216</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:19:15</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>广东菠萝</td>\n", "      <td>70</td>\n", "      <td>0.5143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:52:25</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>冷冻树莓</td>\n", "      <td>69</td>\n", "      <td>0.1449</td>\n", "    </tr>\n", "    <tr>\n", "      <th>311</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:13:34</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>鲜活</td>\n", "      <td>67</td>\n", "      <td>0.1343</td>\n", "    </tr>\n", "    <tr>\n", "      <th>198</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:22:37</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>枇杷罐头</td>\n", "      <td>67</td>\n", "      <td>0.0896</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:39:45</td>\n", "      <td>24</td>\n", "      <td>旧版本</td>\n", "      <td>澄善葡萄罐头</td>\n", "      <td>64</td>\n", "      <td>0.5312</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:56:48</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>勺</td>\n", "      <td>63</td>\n", "      <td>0.4286</td>\n", "    </tr>\n", "    <tr>\n", "      <th>277</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:37:18</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>澄善冷冻青提汁</td>\n", "      <td>60</td>\n", "      <td>0.6833</td>\n", "    </tr>\n", "    <tr>\n", "      <th>128</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:34:28</td>\n", "      <td>24</td>\n", "      <td>旧版本</td>\n", "      <td>百利</td>\n", "      <td>58</td>\n", "      <td>0.4138</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:49:37</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>名忠紫米罐头</td>\n", "      <td>56</td>\n", "      <td>0.3393</td>\n", "    </tr>\n", "    <tr>\n", "      <th>234</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:15:02</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>芝士奶盖</td>\n", "      <td>54</td>\n", "      <td>0.2593</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:39:25</td>\n", "      <td>24</td>\n", "      <td>旧版本</td>\n", "      <td>桂花酱</td>\n", "      <td>52</td>\n", "      <td>0.3077</td>\n", "    </tr>\n", "    <tr>\n", "      <th>51</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:46:56</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>金像B高筋粉(布袋)</td>\n", "      <td>51</td>\n", "      <td>0.4118</td>\n", "    </tr>\n", "    <tr>\n", "      <th>45</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:48:07</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>百吉福</td>\n", "      <td>49</td>\n", "      <td>0.3878</td>\n", "    </tr>\n", "    <tr>\n", "      <th>38</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:49:03</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>澄善凤梨冻肉</td>\n", "      <td>47</td>\n", "      <td>0.4255</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-27 16:06:10</td>\n", "      <td>30</td>\n", "      <td>旧版本</td>\n", "      <td>圆月</td>\n", "      <td>44</td>\n", "      <td>0.1818</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:51:19</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>王后面粉</td>\n", "      <td>43</td>\n", "      <td>0.4186</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:29:42</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>橙色</td>\n", "      <td>40</td>\n", "      <td>0.3750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:31:59</td>\n", "      <td>24</td>\n", "      <td>旧版本</td>\n", "      <td>玲珑小番茄</td>\n", "      <td>38</td>\n", "      <td>0.3947</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:50:12</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>MG奶油芝士2kg</td>\n", "      <td>36</td>\n", "      <td>0.4167</td>\n", "    </tr>\n", "    <tr>\n", "      <th>82</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:42:06</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>阿华田麦芽可可粉</td>\n", "      <td>36</td>\n", "      <td>0.3056</td>\n", "    </tr>\n", "    <tr>\n", "      <th>71</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:43:57</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>安佳有盐黄油</td>\n", "      <td>35</td>\n", "      <td>0.0571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:52:46</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>奥昆速冻榴莲酥</td>\n", "      <td>31</td>\n", "      <td>0.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:26:29</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>新悦纯牧</td>\n", "      <td>30</td>\n", "      <td>0.6000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:53:42</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>纽</td>\n", "      <td>30</td>\n", "      <td>0.6333</td>\n", "    </tr>\n", "    <tr>\n", "      <th>426</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-22 11:56:55</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>吉利</td>\n", "      <td>28</td>\n", "      <td>0.2500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>376</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-22 14:08:50</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>吉利</td>\n", "      <td>28</td>\n", "      <td>0.2500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>423</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-22 11:58:24</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>吉利</td>\n", "      <td>28</td>\n", "      <td>0.2500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>182</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:25:21</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>切块</td>\n", "      <td>26</td>\n", "      <td>0.0769</td>\n", "    </tr>\n", "    <tr>\n", "      <th>114</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:36:59</td>\n", "      <td>24</td>\n", "      <td>旧版本</td>\n", "      <td>鲜活香芋果泥</td>\n", "      <td>24</td>\n", "      <td>0.3750</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:56:37</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td><PERSON><PERSON>芝士酪乳(含乳饮料)</td>\n", "      <td>24</td>\n", "      <td>0.4167</td>\n", "    </tr>\n", "    <tr>\n", "      <th>205</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:21:10</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>艾许</td>\n", "      <td>23</td>\n", "      <td>0.2174</td>\n", "    </tr>\n", "    <tr>\n", "      <th>106</th>\n", "      <td>上海</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:37:55</td>\n", "      <td>24</td>\n", "      <td>旧版本</td>\n", "      <td>叶</td>\n", "      <td>21</td>\n", "      <td>0.1429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>188</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:23:57</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>黎宝斑斓叶粉</td>\n", "      <td>21</td>\n", "      <td>0.5714</td>\n", "    </tr>\n", "    <tr>\n", "      <th>175</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:26:54</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>咖喱</td>\n", "      <td>20</td>\n", "      <td>0.1500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>431</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-20 14:30:36</td>\n", "      <td>6</td>\n", "      <td>旧版本</td>\n", "      <td>帕玛森</td>\n", "      <td>14</td>\n", "      <td>0.1429</td>\n", "    </tr>\n", "    <tr>\n", "      <th>32</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:49:46</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>65</td>\n", "      <td>13</td>\n", "      <td>0.3077</td>\n", "    </tr>\n", "    <tr>\n", "      <th>231</th>\n", "      <td>杭州</td>\n", "      <td></td>\n", "      <td>2024-11-25 18:15:34</td>\n", "      <td>12</td>\n", "      <td>旧版本</td>\n", "      <td>铁</td>\n", "      <td>13</td>\n", "      <td>0.6154</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     城市 吐槽                   时间  页码大小   版本           query   搜索次数  query的ctr\n", "424  杭州     2024-11-22 11:58:11     6  旧版本              草莓  82062     0.3139\n", "363  广州     2024-11-22 14:09:45     6  旧版本              黄油  27627     0.1623\n", "68   杭州     2024-11-25 18:44:15    12  旧版本              柠檬  15926     0.3473\n", "355  广州     2024-11-22 14:10:26     6  旧版本             淡奶油  12347     0.2172\n", "337  广州     2024-11-22 14:11:43     6  旧版本             纯牛奶   7571     0.2685\n", "323  广州     2024-11-22 14:12:46     6  旧版本              芝士   4740     0.1443\n", "309  广州     2024-11-22 14:13:43     6  旧版本             芝士片   3844     0.1751\n", "303  广州     2024-11-22 14:14:10     6  旧版本             可可粉   3620     0.2006\n", "91   上海     2024-11-25 18:40:45    24  旧版本             奥利奥   3595     0.5402\n", "235  杭州     2024-11-25 18:14:50    12  旧版本              鸡蛋   3380     0.3962\n", "190  杭州     2024-11-25 18:23:41    12  旧版本              鸡蛋   3380     0.3962\n", "316  广州     2024-11-22 14:13:15     6  旧版本              奶粉   2417     0.2098\n", "154  杭州     2024-11-25 18:31:07    12  旧版本             芝士碎   1740     0.2339\n", "421  广州     2024-11-22 14:02:07     6  旧版本             车厘子   1696     0.0991\n", "331  广州     2024-11-22 14:12:14     6  旧版本              金桔   1638     0.3578\n", "313  广州     2024-11-22 14:13:29     6  旧版本             气泡水   1543     0.4634\n", "27   杭州     2024-11-25 18:50:34    12  旧版本            动物奶油   1540     0.1935\n", "238  杭州     2024-11-25 18:14:03    12  旧版本            马苏里拉   1511     0.2383\n", "301  广州     2024-11-22 14:14:23     6  旧版本             山茶花   1468     0.7078\n", "170  杭州     2024-11-25 18:27:27    12  旧版本             巴斯克   1352     0.3521\n", "20   上海     2024-11-25 18:52:02     6  旧版本              砂糖   1314     0.4422\n", "306  广州     2024-11-22 14:13:54     6  旧版本         TS韩国幼砂糖   1103     0.5830\n", "143  杭州     2024-11-25 18:32:37    12  旧版本              总统   1057     0.4588\n", "177  杭州     2024-11-25 18:26:41    12  旧版本          澄善草莓果酱   1014     0.3047\n", "78   上海     2024-11-25 18:42:34     6  旧版本              红茶    889     0.3408\n", "258  杭州     2024-11-25 11:41:16     6  旧版本           雀巢纯牛奶    685     0.4657\n", "274  杭州     2024-11-22 14:40:28    12  旧版本             柠檬汁    593     0.3895\n", "213  杭州     2024-11-25 18:19:41    12  旧版本             血糯米    570     0.5789\n", "417  广州     2024-11-22 14:02:31     6  旧版本            玉米淀粉    556     0.1169\n", "174  杭州     2024-11-25 18:27:00    12  旧版本           红心火龙果    473     0.3890\n", "413  广州     2024-11-22 14:02:53     6  旧版本             脆啵啵    454     0.0771\n", "218  上海     2024-11-25 18:18:43    18  旧版本            巧克力豆    425     0.2000\n", "79   杭州     2024-11-25 18:42:30    12  旧版本            巧克力豆    425     0.2000\n", "410  广州     2024-11-22 14:03:07     6  旧版本              面包    389     0.0771\n", "202  杭州     2024-11-25 18:21:56    12  旧版本              可可    357     0.2325\n", "412  广州     2024-11-22 14:02:58     6  旧版本              樱桃    353     0.1048\n", "15   上海     2024-11-25 18:53:05     6  旧版本          肯迪雅淡奶油    310     0.5194\n", "22   杭州     2024-11-25 18:51:28    12  旧版本          澄善蓝莓果馅    291     0.3402\n", "108  杭州     2024-11-25 18:37:40    12  旧版本            榴莲千层    290     0.3690\n", "147  杭州     2024-11-25 18:32:02    12  旧版本            无盐黄油    286     0.2308\n", "203  杭州     2024-11-25 18:21:37    12  旧版本            无盐黄油    286     0.2308\n", "112  杭州     2024-11-25 18:37:17    12  旧版本              荔枝    273     0.4908\n", "405  广州     2024-11-22 14:05:21     6  旧版本              啵啵    253     0.0593\n", "7    杭州     2024-11-25 18:53:52    12  旧版本             大米粉    237     0.3418\n", "356  杭州     2024-11-22 14:10:25    12  旧版本              奶咖    235     0.0894\n", "113  杭州     2024-11-25 18:37:04    12  旧版本              提子    231     0.2381\n", "434  杭州     2024-11-20 14:28:42     6  旧版本             新希望    221     0.5973\n", "102  杭州     2024-11-25 18:39:16    12  旧版本              乔艺    211     0.4882\n", "194  杭州     2024-11-25 18:23:13    12  旧版本              椰蓉    210     0.3143\n", "171  杭州     2024-11-25 18:27:27    12  旧版本             小金桔    209     0.4689\n", "168  杭州     2024-11-25 18:28:04    12  旧版本              蛋黄    201     0.2537\n", "397  广州     2024-11-22 14:06:19     6  旧版本              焦糖    196     0.1888\n", "370  杭州     2024-11-22 14:09:17    12  旧版本              焦糖    196     0.1888\n", "185  杭州     2024-11-25 18:24:33    12  旧版本              蜂蜜    186     0.2366\n", "308  杭州     2024-11-22 14:13:47    12  旧版本               姜    174     0.0460\n", "240  杭州     2024-11-25 18:11:56    12  旧版本             纽麦福    168     0.5833\n", "158  杭州     2024-11-25 18:30:35    12  旧版本             白黄油    167     0.0898\n", "193  杭州     2024-11-25 18:23:16    12  旧版本              菲诺    166     0.1386\n", "327  杭州     2024-11-22 14:12:27    12  旧版本             蛋糕盒    165     0.1212\n", "93   杭州     2024-11-25 18:40:27    12  旧版本            黑巧克力    161     0.2422\n", "189  杭州     2024-11-25 18:23:49    12  旧版本              梨子    154     0.3312\n", "262  杭州     2024-11-25 11:25:37     6  旧版本              坚果    153     0.1242\n", "186  杭州     2024-11-25 18:24:11    12  旧版本              皇后    143     0.2028\n", "50   杭州     2024-11-25 18:47:06    12  旧版本             芒果块    142     0.2676\n", "84   杭州     2024-11-25 18:41:54    12  旧版本              大米    141     0.4468\n", "31   杭州     2024-11-25 18:50:00    12  旧版本            咖啡大师    140     0.3929\n", "199  杭州     2024-11-25 18:22:16    12  旧版本       海南子弹头香水柠檬    134     0.4552\n", "104  杭州     2024-11-25 18:38:50    12  旧版本             椰子油    133     0.0827\n", "393  广州     2024-11-22 14:06:45     6  旧版本          魔客麦芬蛋糕    127     0.1890\n", "439  杭州     2024-11-20 14:04:39     6  旧版本             椰子粉    118     0.1356\n", "374  广州     2024-11-22 14:08:59     6  旧版本              红枣    101     0.1386\n", "228  杭州     2024-11-25 18:16:33    12  旧版本             番石榴     99     0.3232\n", "144  杭州     2024-11-25 18:32:28    12  旧版本      味斯美3A海苔酥脆松     98     0.3776\n", "146  杭州     2024-11-25 18:32:12    12  旧版本             红茶粉     96     0.2500\n", "86   上海     2024-11-25 18:41:42     6  旧版本              罗勒     96     0.3333\n", "66   杭州     2024-11-25 18:44:33    12  旧版本              花生     94     0.1383\n", "265  杭州     2024-11-25 10:46:56     6  旧版本              唯品     90     0.3889\n", "157  杭州     2024-11-25 18:30:41    12  旧版本             沐清友     89     0.1798\n", "169  杭州     2024-11-25 18:27:38    12  旧版本             沐清友     89     0.1798\n", "200  杭州     2024-11-25 18:22:13    12  旧版本              红颜     88     0.3409\n", "221  上海     2024-11-25 18:17:55    18  旧版本       悦焙友榴莲千层蛋糕     87     0.4483\n", "378  广州     2024-11-22 14:08:38     6  旧版本              片油     77     0.1429\n", "149  杭州     2024-11-25 18:31:52    12  旧版本            安佳干酪     77     0.3766\n", "76   上海     2024-11-25 18:42:44     6  旧版本              mg     75     0.3600\n", "33   上海     2024-11-25 18:49:45     6  旧版本            爱真38     73     0.5068\n", "73   杭州     2024-11-25 18:43:09    12  旧版本      金像A高筋粉（纸袋）     72     0.5694\n", "74   上海     2024-11-25 18:42:58     6  旧版本              塔壳     72     0.1111\n", "243  杭州     2024-11-25 18:11:20    12  旧版本            广东菠萝     70     0.5143\n", "216  杭州     2024-11-25 18:19:15    12  旧版本            广东菠萝     70     0.5143\n", "18   上海     2024-11-25 18:52:25     6  旧版本            冷冻树莓     69     0.1449\n", "311  杭州     2024-11-22 14:13:34    12  旧版本              鲜活     67     0.1343\n", "198  杭州     2024-11-25 18:22:37    12  旧版本            枇杷罐头     67     0.0896\n", "99   上海     2024-11-25 18:39:45    24  旧版本          澄善葡萄罐头     64     0.5312\n", "5    上海     2024-11-25 18:56:48     6  旧版本               勺     63     0.4286\n", "277  杭州     2024-11-22 14:37:18    12  旧版本         澄善冷冻青提汁     60     0.6833\n", "128  上海     2024-11-25 18:34:28    24  旧版本              百利     58     0.4138\n", "34   杭州     2024-11-25 18:49:37    12  旧版本          名忠紫米罐头     56     0.3393\n", "234  杭州     2024-11-25 18:15:02    12  旧版本            芝士奶盖     54     0.2593\n", "101  上海     2024-11-25 18:39:25    24  旧版本             桂花酱     52     0.3077\n", "51   杭州     2024-11-25 18:46:56    12  旧版本      金像B高筋粉(布袋)     51     0.4118\n", "45   杭州     2024-11-25 18:48:07    12  旧版本             百吉福     49     0.3878\n", "38   上海     2024-11-25 18:49:03     6  旧版本          澄善凤梨冻肉     47     0.4255\n", "0    杭州     2024-11-27 16:06:10    30  旧版本              圆月     44     0.1818\n", "24   杭州     2024-11-25 18:51:19    12  旧版本            王后面粉     43     0.4186\n", "163  杭州     2024-11-25 18:29:42    12  旧版本              橙色     40     0.3750\n", "148  上海     2024-11-25 18:31:59    24  旧版本           玲珑小番茄     38     0.3947\n", "30   杭州     2024-11-25 18:50:12    12  旧版本       MG奶油芝士2kg     36     0.4167\n", "82   上海     2024-11-25 18:42:06     6  旧版本        阿华田麦芽可可粉     36     0.3056\n", "71   杭州     2024-11-25 18:43:57    12  旧版本          安佳有盐黄油     35     0.0571\n", "17   杭州     2024-11-25 18:52:46    12  旧版本         奥昆速冻榴莲酥     31     0.0000\n", "179  杭州     2024-11-25 18:26:29    12  旧版本            新悦纯牧     30     0.6000\n", "8    杭州     2024-11-25 18:53:42    12  旧版本               纽     30     0.6333\n", "426  杭州     2024-11-22 11:56:55     6  旧版本              吉利     28     0.2500\n", "376  杭州     2024-11-22 14:08:50    12  旧版本              吉利     28     0.2500\n", "423  杭州     2024-11-22 11:58:24     6  旧版本              吉利     28     0.2500\n", "182  杭州     2024-11-25 18:25:21    12  旧版本              切块     26     0.0769\n", "114  上海     2024-11-25 18:36:59    24  旧版本          鲜活香芋果泥     24     0.3750\n", "6    上海     2024-11-25 18:56:37     6  旧版本  Kiri芝士酪乳(含乳饮料)     24     0.4167\n", "205  杭州     2024-11-25 18:21:10    12  旧版本              艾许     23     0.2174\n", "106  上海     2024-11-25 18:37:55    24  旧版本               叶     21     0.1429\n", "188  杭州     2024-11-25 18:23:57    12  旧版本          黎宝斑斓叶粉     21     0.5714\n", "175  杭州     2024-11-25 18:26:54    12  旧版本              咖喱     20     0.1500\n", "431  杭州     2024-11-20 14:30:36     6  旧版本             帕玛森     14     0.1429\n", "32   杭州     2024-11-25 18:49:46    12  旧版本              65     13     0.3077\n", "231  杭州     2024-11-25 18:15:34    12  旧版本               铁     13     0.6154"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["loose_df = records_with_category_df[\n", "    records_with_category_df[\"prefered_version\"] == \"旧版本\"\n", "][\n", "    [\n", "        \"city\",\n", "        \"comment\",\n", "        \"create_time\",\n", "        \"page_size\",\n", "        \"prefered_version\",\n", "        \"query\",\n", "        \"search_cnt\",\n", "        \"ctr\",\n", "    ]\n", "].sort_values(\n", "    by=\"search_cnt\", ascending=False\n", ")\n", "\n", "loose_df.columns = [\n", "    \"城市\",\n", "    \"吐槽\",\n", "    \"时间\",\n", "    \"页码大小\",\n", "    \"版本\",\n", "    \"query\",\n", "    \"搜索次数\",\n", "    \"query的ctr\",\n", "]\n", "loose_df.to_csv(\"./选择了旧版本更好的所有query.csv\", index=False)\n", "loose_df"]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>query</th>\n", "      <th>category4</th>\n", "      <th>category3</th>\n", "      <th>searched_users</th>\n", "      <th>search_cnt</th>\n", "      <th>click_cnt</th>\n", "      <th>click_users</th>\n", "      <th>avg_click_index</th>\n", "      <th>max_click_index</th>\n", "      <th>min_click_index</th>\n", "      <th>std_click_index</th>\n", "      <th>p25_click_index</th>\n", "      <th>p50_click_index</th>\n", "      <th>p75_click_index</th>\n", "      <th>p90_click_index</th>\n", "      <th>days_have_impression</th>\n", "      <th>days_have_click</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>安佳</td>\n", "      <td>搅打型稀奶油</td>\n", "      <td>稀奶油</td>\n", "      <td>3862</td>\n", "      <td>13468</td>\n", "      <td>5155</td>\n", "      <td>2415</td>\n", "      <td>4.0</td>\n", "      <td>36</td>\n", "      <td>0</td>\n", "      <td>3.9</td>\n", "      <td>1.00</td>\n", "      <td>3.0</td>\n", "      <td>5.00</td>\n", "      <td>8.0</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>安佳</td>\n", "      <td>无盐黄油</td>\n", "      <td>黄油</td>\n", "      <td>3766</td>\n", "      <td>10970</td>\n", "      <td>331</td>\n", "      <td>234</td>\n", "      <td>13.5</td>\n", "      <td>34</td>\n", "      <td>0</td>\n", "      <td>10.3</td>\n", "      <td>4.00</td>\n", "      <td>12.5</td>\n", "      <td>22.00</td>\n", "      <td>28.0</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>安佳</td>\n", "      <td>切达再制干酪</td>\n", "      <td>奶酪丨芝士</td>\n", "      <td>2958</td>\n", "      <td>8739</td>\n", "      <td>313</td>\n", "      <td>209</td>\n", "      <td>14.7</td>\n", "      <td>33</td>\n", "      <td>0</td>\n", "      <td>8.1</td>\n", "      <td>9.00</td>\n", "      <td>15.0</td>\n", "      <td>22.00</td>\n", "      <td>26.0</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>安佳</td>\n", "      <td>有盐黄油</td>\n", "      <td>黄油</td>\n", "      <td>2513</td>\n", "      <td>5656</td>\n", "      <td>103</td>\n", "      <td>90</td>\n", "      <td>9.2</td>\n", "      <td>33</td>\n", "      <td>0</td>\n", "      <td>5.8</td>\n", "      <td>4.00</td>\n", "      <td>9.0</td>\n", "      <td>12.00</td>\n", "      <td>16.6</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>安佳</td>\n", "      <td>乳酸黄油</td>\n", "      <td>黄油</td>\n", "      <td>1880</td>\n", "      <td>3282</td>\n", "      <td>27</td>\n", "      <td>26</td>\n", "      <td>6.9</td>\n", "      <td>26</td>\n", "      <td>1</td>\n", "      <td>6.6</td>\n", "      <td>2.00</td>\n", "      <td>4.0</td>\n", "      <td>9.00</td>\n", "      <td>13.4</td>\n", "      <td>14</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>33</th>\n", "      <td>安佳</td>\n", "      <td>全脂乳粉</td>\n", "      <td>奶粉</td>\n", "      <td>1727</td>\n", "      <td>3545</td>\n", "      <td>24</td>\n", "      <td>24</td>\n", "      <td>2.9</td>\n", "      <td>10</td>\n", "      <td>0</td>\n", "      <td>2.7</td>\n", "      <td>0.75</td>\n", "      <td>3.0</td>\n", "      <td>4.00</td>\n", "      <td>4.7</td>\n", "      <td>14</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34</th>\n", "      <td>安佳</td>\n", "      <td>奶油奶酪</td>\n", "      <td>奶酪丨芝士</td>\n", "      <td>1690</td>\n", "      <td>4836</td>\n", "      <td>303</td>\n", "      <td>205</td>\n", "      <td>14.0</td>\n", "      <td>30</td>\n", "      <td>0</td>\n", "      <td>6.4</td>\n", "      <td>9.00</td>\n", "      <td>14.0</td>\n", "      <td>19.00</td>\n", "      <td>22.8</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>40</th>\n", "      <td>安佳</td>\n", "      <td>马苏里拉</td>\n", "      <td>奶酪丨芝士</td>\n", "      <td>1400</td>\n", "      <td>3497</td>\n", "      <td>144</td>\n", "      <td>99</td>\n", "      <td>13.2</td>\n", "      <td>36</td>\n", "      <td>0</td>\n", "      <td>8.2</td>\n", "      <td>9.00</td>\n", "      <td>15.0</td>\n", "      <td>18.00</td>\n", "      <td>22.0</td>\n", "      <td>14</td>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>85</th>\n", "      <td>安佳</td>\n", "      <td>淡味黄油</td>\n", "      <td>黄油</td>\n", "      <td>740</td>\n", "      <td>1090</td>\n", "      <td>18</td>\n", "      <td>18</td>\n", "      <td>8.8</td>\n", "      <td>28</td>\n", "      <td>4</td>\n", "      <td>5.4</td>\n", "      <td>6.00</td>\n", "      <td>8.0</td>\n", "      <td>9.75</td>\n", "      <td>12.2</td>\n", "      <td>14</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>305</th>\n", "      <td>安佳</td>\n", "      <td>果汁原浆</td>\n", "      <td>果汁原料</td>\n", "      <td>274</td>\n", "      <td>467</td>\n", "      <td>6</td>\n", "      <td>6</td>\n", "      <td>34.8</td>\n", "      <td>69</td>\n", "      <td>22</td>\n", "      <td>16.1</td>\n", "      <td>23.75</td>\n", "      <td>30.0</td>\n", "      <td>34.75</td>\n", "      <td>52.0</td>\n", "      <td>14</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>311</th>\n", "      <td>安佳</td>\n", "      <td>凤梨丨菠萝</td>\n", "      <td>浆果类</td>\n", "      <td>268</td>\n", "      <td>417</td>\n", "      <td>6</td>\n", "      <td>5</td>\n", "      <td>28.7</td>\n", "      <td>35</td>\n", "      <td>23</td>\n", "      <td>5.7</td>\n", "      <td>23.00</td>\n", "      <td>28.5</td>\n", "      <td>34.00</td>\n", "      <td>34.5</td>\n", "      <td>14</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>368</th>\n", "      <td>安佳</td>\n", "      <td>喷射型稀奶油</td>\n", "      <td>稀奶油</td>\n", "      <td>232</td>\n", "      <td>317</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>20.3</td>\n", "      <td>23</td>\n", "      <td>14</td>\n", "      <td>3.6</td>\n", "      <td>20.00</td>\n", "      <td>22.0</td>\n", "      <td>22.25</td>\n", "      <td>22.7</td>\n", "      <td>14</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>534</th>\n", "      <td>安佳</td>\n", "      <td>果泥丨果茸</td>\n", "      <td>水果风味制品</td>\n", "      <td>165</td>\n", "      <td>361</td>\n", "      <td>19</td>\n", "      <td>17</td>\n", "      <td>39.3</td>\n", "      <td>57</td>\n", "      <td>21</td>\n", "      <td>12.0</td>\n", "      <td>28.00</td>\n", "      <td>38.5</td>\n", "      <td>52.00</td>\n", "      <td>52.6</td>\n", "      <td>14</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1592</th>\n", "      <td>安佳</td>\n", "      <td>果茶酱</td>\n", "      <td>水果风味制品</td>\n", "      <td>44</td>\n", "      <td>123</td>\n", "      <td>9</td>\n", "      <td>7</td>\n", "      <td>50.6</td>\n", "      <td>104</td>\n", "      <td>22</td>\n", "      <td>23.4</td>\n", "      <td>35.50</td>\n", "      <td>47.0</td>\n", "      <td>54.25</td>\n", "      <td>76.0</td>\n", "      <td>13</td>\n", "      <td>5</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     query category4 category3  searched_users  search_cnt  click_cnt  \\\n", "4       安佳    搅打型稀奶油       稀奶油            3862       13468       5155   \n", "6       安佳      无盐黄油        黄油            3766       10970        331   \n", "11      安佳    切达再制干酪     奶酪丨芝士            2958        8739        313   \n", "20      安佳      有盐黄油        黄油            2513        5656        103   \n", "30      安佳      乳酸黄油        黄油            1880        3282         27   \n", "33      安佳      全脂乳粉        奶粉            1727        3545         24   \n", "34      安佳      奶油奶酪     奶酪丨芝士            1690        4836        303   \n", "40      安佳      马苏里拉     奶酪丨芝士            1400        3497        144   \n", "85      安佳      淡味黄油        黄油             740        1090         18   \n", "305     安佳      果汁原浆      果汁原料             274         467          6   \n", "311     安佳     凤梨丨菠萝       浆果类             268         417          6   \n", "368     安佳    喷射型稀奶油       稀奶油             232         317          4   \n", "534     安佳     果泥丨果茸    水果风味制品             165         361         19   \n", "1592    安佳       果茶酱    水果风味制品              44         123          9   \n", "\n", "      click_users  avg_click_index  max_click_index  min_click_index  \\\n", "4            2415              4.0               36                0   \n", "6             234             13.5               34                0   \n", "11            209             14.7               33                0   \n", "20             90              9.2               33                0   \n", "30             26              6.9               26                1   \n", "33             24              2.9               10                0   \n", "34            205             14.0               30                0   \n", "40             99             13.2               36                0   \n", "85             18              8.8               28                4   \n", "305             6             34.8               69               22   \n", "311             5             28.7               35               23   \n", "368             4             20.3               23               14   \n", "534            17             39.3               57               21   \n", "1592            7             50.6              104               22   \n", "\n", "      std_click_index  p25_click_index  p50_click_index  p75_click_index  \\\n", "4                 3.9             1.00              3.0             5.00   \n", "6                10.3             4.00             12.5            22.00   \n", "11                8.1             9.00             15.0            22.00   \n", "20                5.8             4.00              9.0            12.00   \n", "30                6.6             2.00              4.0             9.00   \n", "33                2.7             0.75              3.0             4.00   \n", "34                6.4             9.00             14.0            19.00   \n", "40                8.2             9.00             15.0            18.00   \n", "85                5.4             6.00              8.0             9.75   \n", "305              16.1            23.75             30.0            34.75   \n", "311               5.7            23.00             28.5            34.00   \n", "368               3.6            20.00             22.0            22.25   \n", "534              12.0            28.00             38.5            52.00   \n", "1592             23.4            35.50             47.0            54.25   \n", "\n", "      p90_click_index  days_have_impression  days_have_click  \n", "4                 8.0                    14               14  \n", "6                28.0                    14               14  \n", "11               26.0                    14               14  \n", "20               16.6                    14               14  \n", "30               13.4                    14               13  \n", "33                4.7                    14               11  \n", "34               22.8                    14               14  \n", "40               22.0                    14               14  \n", "85               12.2                    14               11  \n", "305              52.0                    14                5  \n", "311              34.5                    14                4  \n", "368              22.7                    14                4  \n", "534              52.6                    14               11  \n", "1592             76.0                    13                5  "]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["category_prediction_df[category_prediction_df['query']=='安佳']"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>搜索频率</th>\n", "      <th>版本</th>\n", "      <th>query数量</th>\n", "      <th>总投票次数</th>\n", "      <th>query的平均点击率</th>\n", "      <th>query总搜索次数</th>\n", "      <th>query总搜索次数%</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>中等频率词</td>\n", "      <td>新版本</td>\n", "      <td>121</td>\n", "      <td>132</td>\n", "      <td>0.276035</td>\n", "      <td>17450</td>\n", "      <td>2.02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>中等频率词</td>\n", "      <td>旧版本</td>\n", "      <td>84</td>\n", "      <td>91</td>\n", "      <td>0.297485</td>\n", "      <td>12067</td>\n", "      <td>1.40</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>中等频率词</td>\n", "      <td>差不多</td>\n", "      <td>57</td>\n", "      <td>63</td>\n", "      <td>0.310054</td>\n", "      <td>7692</td>\n", "      <td>0.89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>中等频率词</td>\n", "      <td>都很差</td>\n", "      <td>27</td>\n", "      <td>30</td>\n", "      <td>0.227400</td>\n", "      <td>4236</td>\n", "      <td>0.49</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>低频词</td>\n", "      <td>新版本</td>\n", "      <td>5</td>\n", "      <td>5</td>\n", "      <td>0.278960</td>\n", "      <td>78</td>\n", "      <td>0.01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>低频词</td>\n", "      <td>差不多</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0.379175</td>\n", "      <td>68</td>\n", "      <td>0.01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>低频词</td>\n", "      <td>旧版本</td>\n", "      <td>4</td>\n", "      <td>4</td>\n", "      <td>0.304000</td>\n", "      <td>60</td>\n", "      <td>0.01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>低频词</td>\n", "      <td>都很差</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>0.350000</td>\n", "      <td>35</td>\n", "      <td>0.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>高频词</td>\n", "      <td>新版本</td>\n", "      <td>36</td>\n", "      <td>41</td>\n", "      <td>0.297354</td>\n", "      <td>176800</td>\n", "      <td>20.45</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>高频词</td>\n", "      <td>差不多</td>\n", "      <td>35</td>\n", "      <td>39</td>\n", "      <td>0.359685</td>\n", "      <td>453337</td>\n", "      <td>52.44</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>高频词</td>\n", "      <td>旧版本</td>\n", "      <td>29</td>\n", "      <td>30</td>\n", "      <td>0.336233</td>\n", "      <td>191251</td>\n", "      <td>22.12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>高频词</td>\n", "      <td>都很差</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>0.096400</td>\n", "      <td>1452</td>\n", "      <td>0.17</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     搜索频率   版本  query数量  总投票次数  query的平均点击率  query总搜索次数  query总搜索次数%\n", "1   中等频率词  新版本      121    132     0.276035       17450         2.02\n", "2   中等频率词  旧版本       84     91     0.297485       12067         1.40\n", "0   中等频率词  差不多       57     63     0.310054        7692         0.89\n", "3   中等频率词  都很差       27     30     0.227400        4236         0.49\n", "5     低频词  新版本        5      5     0.278960          78         0.01\n", "4     低频词  差不多        4      4     0.379175          68         0.01\n", "6     低频词  旧版本        4      4     0.304000          60         0.01\n", "7     低频词  都很差        2      2     0.350000          35         0.00\n", "9     高频词  新版本       36     41     0.297354      176800        20.45\n", "8     高频词  差不多       35     39     0.359685      453337        52.44\n", "10    高频词  旧版本       29     30     0.336233      191251        22.12\n", "11    高频词  都很差        2      2     0.096400        1452         0.17"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 定义搜索频率分类函数\n", "def categorize_query_frequency(search_count):\n", "    if search_count >= 472:\n", "        return \"高频词\"\n", "    elif search_count <= 20:\n", "        return \"低频词\"\n", "    else:\n", "        return \"中等频率词\"\n", "\n", "\n", "# 为records_with_category_df添加搜索频率字段\n", "records_with_category_df[\"搜索频率\"] = records_with_category_df[\"search_cnt\"].apply(\n", "    categorize_query_frequency\n", ")\n", "\n", "# 按搜索频率和preferred_version分组统计\n", "frequency_version_summary = (\n", "    records_with_category_df.groupby([\"搜索频率\", \"prefered_version\"])\n", "    .agg(\n", "        {\n", "            \"query\": \"nunique\",  # 不同query的数量\n", "            \"create_time\": \"count\",  # 总搜索次数\n", "            \"ctr\": \"mean\",  # 平均点击率\n", "            \"search_cnt\": \"sum\",  # 总搜索次数\n", "        }\n", "    )\n", "    .reset_index()\n", ")\n", "\n", "# 重命名列以提高可读性\n", "frequency_version_summary.columns = [\n", "    \"搜索频率\",\n", "    \"版本\",\n", "    \"query数量\",\n", "    \"总投票次数\",\n", "    \"query的平均点击率\",\n", "    \"query总搜索次数\",\n", "]\n", "\n", "# 排序以便于阅读\n", "frequency_version_summary = frequency_version_summary.sort_values(\n", "    [\"搜索频率\", \"总投票次数\"], ascending=[True, False]\n", ")\n", "frequency_version_summary[\"query总搜索次数%\"] = round(\n", "    frequency_version_summary[\"query总搜索次数\"]\n", "    / frequency_version_summary[\"query总搜索次数\"].sum()\n", "    * 100.00,\n", "    2,\n", ")\n", "\n", "display(frequency_version_summary)"]}, {"cell_type": "code", "execution_count": 103, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>是否有类目预测</th>\n", "      <th>query个数</th>\n", "      <th>query总搜索次数</th>\n", "      <th>query总点击次数</th>\n", "      <th>query总搜索次数%</th>\n", "      <th>query总点击次数%</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>有类目预测</td>\n", "      <td>1579</td>\n", "      <td>827291</td>\n", "      <td>266368</td>\n", "      <td>87.826273</td>\n", "      <td>92.546409</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>没有类目预测结果</td>\n", "      <td>11025</td>\n", "      <td>114672</td>\n", "      <td>21453</td>\n", "      <td>12.173727</td>\n", "      <td>7.453591</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    是否有类目预测  query个数  query总搜索次数  query总点击次数  query总搜索次数%  query总点击次数%\n", "0     有类目预测     1579      827291      266368    87.826273    92.546409\n", "1  没有类目预测结果    11025      114672       21453    12.173727     7.453591"]}, "execution_count": 103, "metadata": {}, "output_type": "execute_result"}], "source": ["category_prediction_grouped_result\n", "top_query_df\n", "total_click_cnt\n", "total_search_cnt\n", "\n", "df1=pandasql.sqldf(f\"\"\"\n", "select case when b.category4 is null then '没有类目预测结果' else '有类目预测' end as 是否有类目预测, \n", "               count(distinct a.query) as query个数,\n", "               sum(a.search_cnt) query总搜索次数,\n", "               sum(a.click_cnt)query总点击次数,\n", "                100.00*sum(a.search_cnt)/{total_search_cnt} `query总搜索次数%`,\n", "               100.00*sum(a.click_cnt)/{total_click_cnt} `query总点击次数%`\n", "               from top_query_df a\n", "               left join category_prediction_grouped_result b\n", "               on a.query = b.query\n", "               group by 1\n", "\"\"\")\n", "df1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 2}