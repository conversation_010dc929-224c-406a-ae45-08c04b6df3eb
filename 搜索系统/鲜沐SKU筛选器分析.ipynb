{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-03-25 14:12:46 - INFO - Tunnel session created: <InstanceDownloadSession id=202503251412452d1d3b1a05d1ff00 project_name=summerfarm_ds_dev instance_id=20250325061238423g2yfzy7kdy2>\n", "2025-03-25 14:12:47 - INFO - sql:\n", "\n", "SELECT  a.*,b.*\n", "FROM    summerfarm_tech.dim_sku_df a\n", "JOIN    (\n", "            SELECT  sku\n", "                    ,MIN(price) AS min_price\n", "                    ,MAX(price) AS max_price\n", "            FROM    summerfarm_tech.ods_area_sku_df\n", "            WHERE   ds = MAX_PT('summerfarm_tech.ods_area_sku_df')\n", "            AND     on_sale = 1\n", "            AND     m_type = 0\n", "            GROUP BY sku\n", "            HAVING  COUNT(DISTINCT area_no) > 0\n", "        ) b\n", "ON      a.sku_id = b.sku\n", "WHERE ds=MAX_PT('summerfarm_tech.dim_sku_df')\n", "AND sub_type!=5\n", "AND spu_name not like '%测试%'\n", "AND spu_name not like '%专用%'\n", "\n", "columns:Index(['sku_id', 'spu_id', 'spu_name', 'spu_no', 'disc', 'store_method',\n", "       'sku_type', 'big_cust_id', 'category1', 'category2', 'category3',\n", "       'category4', 'sku_spec', 'origin', 'sku_brand', 'tempature', 'weight',\n", "       'volume', 'other_properties', 'create_date', 'create_time',\n", "       'abandon_date', 'abandon_time', 'standard_cnt', 'standard_unit',\n", "       'warn_days', 'is_self_owned_brand', 'tax_rate', 'tax_rate_code',\n", "       'min_sale_cnt', 'sale_step_cnt', 'max_after_sale_cnt',\n", "       'after_sale_unit', 'pack_unit', 'outdated', 'category2_id',\n", "       'category3_id', 'category4_id', 'inv_id', 'after_sale_time',\n", "       'warn_time', 'sub_type', 'net_weight_num', 'net_weight_unit',\n", "       'buyer_id', 'buyer', 'after_sale_rule_detail', 'ds', 'sku', 'min_price',\n", "       'max_price'],\n", "      dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sku_id</th>\n", "      <th>spu_id</th>\n", "      <th>spu_name</th>\n", "      <th>spu_no</th>\n", "      <th>disc</th>\n", "      <th>store_method</th>\n", "      <th>sku_type</th>\n", "      <th>big_cust_id</th>\n", "      <th>category1</th>\n", "      <th>category2</th>\n", "      <th>category3</th>\n", "      <th>category4</th>\n", "      <th>sku_spec</th>\n", "      <th>origin</th>\n", "      <th>sku_brand</th>\n", "      <th>tempature</th>\n", "      <th>weight</th>\n", "      <th>volume</th>\n", "      <th>other_properties</th>\n", "      <th>create_date</th>\n", "      <th>create_time</th>\n", "      <th>abandon_date</th>\n", "      <th>abandon_time</th>\n", "      <th>standard_cnt</th>\n", "      <th>standard_unit</th>\n", "      <th>warn_days</th>\n", "      <th>is_self_owned_brand</th>\n", "      <th>tax_rate</th>\n", "      <th>tax_rate_code</th>\n", "      <th>min_sale_cnt</th>\n", "      <th>sale_step_cnt</th>\n", "      <th>max_after_sale_cnt</th>\n", "      <th>after_sale_unit</th>\n", "      <th>pack_unit</th>\n", "      <th>outdated</th>\n", "      <th>category2_id</th>\n", "      <th>category3_id</th>\n", "      <th>category4_id</th>\n", "      <th>inv_id</th>\n", "      <th>after_sale_time</th>\n", "      <th>warn_time</th>\n", "      <th>sub_type</th>\n", "      <th>net_weight_num</th>\n", "      <th>net_weight_unit</th>\n", "      <th>buyer_id</th>\n", "      <th>buyer</th>\n", "      <th>after_sale_rule_detail</th>\n", "      <th>ds</th>\n", "      <th>sku</th>\n", "      <th>min_price</th>\n", "      <th>max_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1001052874538</td>\n", "      <td>7530</td>\n", "      <td>安卡拉意大利（直面）500g</td>\n", "      <td>1001052874</td>\n", "      <td>500G*20袋</td>\n", "      <td>常温</td>\n", "      <td>2</td>\n", "      <td>-1</td>\n", "      <td>其他</td>\n", "      <td>成品原料</td>\n", "      <td>方便速食</td>\n", "      <td>速食面</td>\n", "      <td>500G*20袋</td>\n", "      <td>土耳其</td>\n", "      <td>安卡拉</td>\n", "      <td>常温</td>\n", "      <td>10</td>\n", "      <td>0.80*0.60*0.60</td>\n", "      <td>{\"商品性质\":\"常规\"}</td>\n", "      <td>20231106</td>\n", "      <td>2023-11-06 16:12:13</td>\n", "      <td>99991231</td>\n", "      <td>9999-12-31 00:00:00</td>\n", "      <td>10000</td>\n", "      <td>g</td>\n", "      <td>45</td>\n", "      <td>0</td>\n", "      <td>0.01</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>20</td>\n", "      <td>袋</td>\n", "      <td>箱</td>\n", "      <td>0</td>\n", "      <td>405</td>\n", "      <td>998</td>\n", "      <td>1001</td>\n", "      <td>14600</td>\n", "      <td>48</td>\n", "      <td>45.0</td>\n", "      <td>1</td>\n", "      <td>None</td>\n", "      <td></td>\n", "      <td>None</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>20250324</td>\n", "      <td>1001052874538</td>\n", "      <td>132.25</td>\n", "      <td>135</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          sku_id  spu_id        spu_name      spu_no      disc store_method  \\\n", "0  1001052874538    7530  安卡拉意大利（直面）500g  1001052874  500G*20袋           常温   \n", "\n", "   sku_type big_cust_id category1 category2 category3 category4  sku_spec  \\\n", "0         2          -1        其他      成品原料      方便速食       速食面  500G*20袋   \n", "\n", "  origin sku_brand tempature weight          volume other_properties  \\\n", "0    土耳其       安卡拉        常温     10  0.80*0.60*0.60    {\"商品性质\":\"常规\"}   \n", "\n", "  create_date         create_time abandon_date         abandon_time  \\\n", "0    20231106 2023-11-06 16:12:13     99991231  9999-12-31 00:00:00   \n", "\n", "  standard_cnt standard_unit  warn_days  is_self_owned_brand tax_rate  \\\n", "0        10000             g         45                    0     0.01   \n", "\n", "  tax_rate_code  min_sale_cnt  sale_step_cnt  max_after_sale_cnt  \\\n", "0             1             1              1                  20   \n", "\n", "  after_sale_unit pack_unit  outdated category2_id category3_id category4_id  \\\n", "0               袋         箱         0          405          998         1001   \n", "\n", "   inv_id  after_sale_time  warn_time  sub_type net_weight_num  \\\n", "0   14600               48       45.0         1           None   \n", "\n", "  net_weight_unit buyer_id buyer after_sale_rule_detail        ds  \\\n", "0                     None                               20250324   \n", "\n", "             sku min_price max_price  \n", "0  1001052874538    132.25       135  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from datetime import datetime, timedelta\n", "import sys\n", "\n", "sys.path.append(\"../\")\n", "\n", "# category_prediction_df 获取\n", "from odps_client import get_odps_sql_result_as_df\n", "\n", "sku_query=\"\"\"\n", "SELECT  a.*,b.*\n", "FROM    summerfarm_tech.dim_sku_df a\n", "JOIN    (\n", "            SELECT  sku\n", "                    ,MIN(price) AS min_price\n", "                    ,MAX(price) AS max_price\n", "            FROM    summerfarm_tech.ods_area_sku_df\n", "            WHERE   ds = MAX_PT('summerfarm_tech.ods_area_sku_df')\n", "            AND     on_sale = 1\n", "            AND     m_type = 0\n", "            GROUP BY sku\n", "            HAVING  COUNT(DISTINCT area_no) > 0\n", "        ) b\n", "ON      a.sku_id = b.sku\n", "WHERE ds=MAX_PT('summerfarm_tech.dim_sku_df')\n", "AND sub_type!=5\n", "AND spu_name not like '%测试%'\n", "AND spu_name not like '%专用%'\n", "\"\"\"\n", "\n", "xm_sku_df=get_odps_sql_result_as_df(sku_query)\n", "xm_sku_df.head(1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 类目、品牌、品种、价格、产地、鲜果等级、乳脂含量、储藏区域"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["产地列表: ['土耳其' '台湾' '越南' '中国' '江苏' '荷兰' '上海' '新西兰' '四川' '广东,四川,云南' '广东' '海南' '无'\n", " '福建省' '意大利' '泰国' '嘉兴' '广西' '广东汕头' '塞拉诺火腿切片' '广西/越南' '河南' '河南省' '浙江'\n", " '浙江湖州' '湖北' '国产' '辽宁' '四川安岳' '山东' '马来西亚' '苏州' '云南' '新加坡' '法国' '日本' '瑞士'\n", " '中国大陆' '福建' '美国' '比利时' '澳大利亚' '丹麦' '未知' '新疆' '北京' '河北' '墨西哥米却肯州' '德国'\n", " '安徽' '南非' '天津' '西班牙' '天津市' '爱尔兰' '菲律宾' '中国福建' '湖州' '昆山' '广东佛山' '杭州'\n", " '海南文昌' '墨西哥' '苏格兰' '古巴' '英国' '瑞典' '广州' '江西' '湖北、浙江' '重庆' '湖南' '湖北宜昌'\n", " '222' '重庆奉节' '陕西' '云南/海南' '成都' '智利' '老挝' '埃及' '辽宁丹东' '秘鲁' '江苏南通' '宁夏'\n", " '香港' '海外' '印尼' '还好' '江西,湖南,湖北' '加拿大' '澳洲' '甘肃' '砀山' '韶关' '广西武鸣' '以色列'\n", " '无锡' '徐州' '南通' '湛江' ' 广东' '本地' '安徽合肥' ' 菲律宾' '四川、云南' '秘鲁,智利' '内蒙,东北'\n", " '广东、广西' '山西' '贺兰山' '阿根廷' '江苏省泰州市' '纽西兰' '上海松江' '俄罗斯' '天津市滨海新区' '上海市'\n", " '丹东、四川、安徽等' '吉林' '武汉' '港澳台' '贵州贵阳' '黑龙江' '河北、黑龙江、天津、山东' '广西北海市' '青岛'\n", " '甘肃传祁' '波兰' '青岛、承德' '浙江徐州' '必如黄油牛乳' '山东省济宁市' '呼和浩特' '河北石家庄' '湖南长沙' '江苏苏州'\n", " '中国浙江' '保加利亚' '浙江省' '江苏省' '东北' '安徽阜阳' '攀枝花' '浙江省湖州市' '福建漳州' '浙江嘉兴' '安徽宣城'\n", " '台州' '浙江台州' '浙江杭州' '厦门' '江苏南京' '温州' '金华' '广东省' '广州、湖南' '江苏、广东、四川' '中国山东'\n", " '韩国' '汕头' '爱上大大' '马鞍山' '安徽、天津' '桂林' '广西、福建' '哈尔滨' '宁波' '安徽宿州' '浙江衢州'\n", " '山东潍坊' '莱阳' '广西南宁' '法国/波兰' '山东莱阳' '山东泰安' '广西崇左' '合肥' '中国辽宁' '马达加斯加'\n", " 'FIJI/斐济' '印度尼西亚' '中国安徽' '桐乡' '广东省深圳市' '塞浦路斯' '广西钦州' '长沙' '张家口' '漳州'\n", " '广东省佛山市' '滁州' '佛山市' '河南商丘' '巴西、哥伦比亚、曼特宁、越南' '云南、越南' '哥伦比亚、云南、印尼' '广东省江门市'\n", " '江苏无锡' '广东省东莞市' '广东东莞' '深圳' '大陆' '11' '安徽马鞍山' '江苏镇江' '鲜沐农场' '中国江苏' '潮汕'\n", " '杭州临安' '保定' '波多黎各' '福建泉州永春' '四川/云南' '希腊' '伊拉克' '海南,广东,广西' '安徽砀山' '甘肃省武威市'\n", " '湖北武汉' '山东省' '白俄罗斯' '广州市' '广东省广州市' '河北廊坊' '海南省' '河南新乡' '新建' '1' '东莞' '常州'\n", " '文昌' '云南昆明' '宿州' '山东济南' '佛山' '四川成都' '中国上海' '南宁' '江苏省无锡市']\n"]}], "source": ["print(\"产地列表:\", xm_sku_df['origin'].unique())"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["品牌列表: ['安卡拉' '张君雅' '水妈妈' '无' '菲仕兰' 'Protag蛋白标签' '奈特兰' '安佳' '你好椰' '欢乐家' '速拓' '肆饮'\n", " '德宝' '珠江' '慕玛' '福特莎' '贝丝达' '冠利' '立高' '乐其雅' '馨芝' '潘泰' '歌伦' '阿米可' '好时' '百利'\n", " '家乐' '-' '德馨珍选' '加利' 'Beretta' '味斯美' '黑旗' '荷美尔' '黑棋' '卡丽娜' '大成' '至大' '台宏'\n", " '百倍德' '品味' '兴志和' '黑旗食品' '臻行者' '金合慧' '合慧' 'Beretra' '丸菱' '唐人神' '日龙' '_'\n", " '安琪' '听茶昵语' '白钻' '敬松庄园' '麦嘉' '百钻' '碧富' '展艺' '锐凯' '阿华田' '乃欣' '南国' '都梅'\n", " '白燕' '伯爵' '佩尔图斯' '鸟越' '顶焙' '水手' '谷本' '金像' '添然' '法瑞芙' '璞季' '英雄' '味客吉'\n", " '三叔公' '奥朵' 'C味' '甲晟' '蒲福' '莫奈瑞' '落基山' '日清' '广村' 'yuzhi金桐宇治' '法芙娜' '蒙娜丽莎'\n", " '嘉利宝' '可可百利' '比妮卡萨' 'MG' '琪雷萨' '爱氏晨曦' '金龙鱼' '乐芙娜' '百吉福' '乌巴尼' '成宝儿' '圣艾德'\n", " '安德鲁' '香馨堂' '暂无' '鲜沐农场' '总统' '哈斯（hass）' '鲜沐' '阿尔乐' '妙可蓝多' '恒天然' '维益植造'\n", " '维他' '咖岚/MILKLAB' '养乐多' 'Oatly' '无籽西瓜' '君皇' '澳醇牧场' '廸比克' '爱真' '雀巢' '欧德堡'\n", " '爱氏晨曦(阿尔乐)' 'Dr.Pro' '爱护' '艾恩摩尔' '伊利' '可尔必思' '宝茸' '和牧' 'SW' '金钻'\n", " 'Africana' '德馨' '鲜活' '新的' '望牌' '喜椰榭' '正芳' 'Olmeca/奥美加' 'JACOBSCREEK/杰卡斯'\n", " 'Malibu/马利宝' \"Ballantine's/百龄坛\" 'HavanaClub/哈瓦纳俱乐部' 'Beefeater/必富达'\n", " 'LILLET/莉蕾' 'Chivas/芝华士' 'Absolut/绝对' 'SUMMERFARM' '名忠' '天聪' '奕方' '太古'\n", " '莫林' '迪吉福DGF' '达芬奇' '欧之玫' '蓓朵芬' '春茗' '博多家园' '都乐' '戴妃' '台创' '奥利奥' '黑白'\n", " '扬雅' 'perrier' '屈臣氏' '可口可乐' '宾得宝' '三麟' '快乐柠檬' '索萨' '福牌' '梁山伯和茱丽叶' '熊猫'\n", " '大青芒' '<PERSON><PERSON><PERSON>/甘露' '金山' '蓝钻' '伊高' '王后' '三象' '美玫' '南桥' '立高奥昆' '维益' '鲜恩滋'\n", " '丘比' '法芮雅' '焙考林' '彩虹果园' '高达' '蓝威斯顿' '优鲜沛' '佳乐' '燕子' '瑞娜' '扬可' '乔栩娅' '铁塔'\n", " '樱淘星球' 'dell' 'Fruit Dor' ' 越南椰青' '佳沃' '佳农' '翠香猕猴桃' '国产' '进口' '八喜' '歌文'\n", " 'Eurofoo' 'Tonadita' '诺曼底' '金凯利' '西乐迪' 'Bega' '牛佰士' '牧恩' 'WESTPRO/威士宝'\n", " '爱登' '伊斯尼' '金茶王' '海融' '诺蕊纤' '塞尚' '凯瑞' '乐菲利娜' '和润' '嘉品多' 'HCL' '塔图拉' '宝宏'\n", " '柏札莱' '拉伊塔' '柏札莱阿尔卑' '牛佰仕' '妙可百吉' '欧澜' '诗黛乐' '三元' '大拓' '辛尼琪' '格尔巴巴' '意文'\n", " '多美鲜' '欧萨' '廷诺' '菲仕利' '味熙' '不二' '保利' '焙之玺' '特莱宝' '新悦纯牧' '骑士' '安琪Angel'\n", " 'Kiri' '爱乐薇' 'MILLAC' '紫风车' '亨佳' '常春' '认养' '优酪谷' '伊然' '南国乳业' '新希望' '植上牧场'\n", " '酷盖' '兰雀' '荷兰旗牌' '云上传祁' '牡纯' '梅维堡' '纽德福' '卫岗' '必如' '奥兰克' '蒙牛' '黑海盗' '妙可维'\n", " '味全' '悦鲜活' '光明' '朝日唯品' '优诺' '宝德谷' '.' '盛嘉' '鸿创' '象牌' '。' '天宏' '渝龙' '奥昆'\n", " '优蕾悦享' '福爱缇' '尚嘉' '波路梦' '豫吉' '莱家' 'Bahlsen' 'MOTTO' '恬焙' '劳仑兹' '谷优' '法吉娜'\n", " '梵豪登' '馥格' '黛妃' '瑞士莲' '格莉芙' '摩丽可' '可可琳娜' '焙乐道' '豪非凡' '舒可曼' 'Barry' '朱师傅'\n", " '台创蓝黛' '绯世' '大卫吉利丁' 'HERSHEY’S/好时' '巧力可' '巧斯诺' '十味町' '宇治' '科麦' '日深' '青昔'\n", " '贵茶' '沐清友' '萃茶季' '鲜拿缇' '弘泰食品' '彤幸玖隆' '启谐诚' '彤幸' '安立司' '红山绿珠' '弘泰' '有茶道'\n", " '盾皇' '速品' '听茶' '卡罗' '磨力' '凯焙乐' '芝兰雅' '碧琪' '贝一' '新日清' '宇峰' '欧福' '茂麟' '能多益'\n", " '七色花语' '甄想记' '高贝' '好禧坊' '七哥' '麦滋跳动' '慕玛星厨' '蓝禾' '大佬强' '俏侬' '克拉秋天' '妞丝达特'\n", " '普利欧' '魔客' '郑丹尼尔' '东桃' '法布甜' '约翰丹尼' '泡泡乃斯' '欧西麦' '弗莱甜田' '安特鲁七哥' '克拉春天'\n", " '谷百分' '园月' '壹米汀' '苏格私甜' '悦焙友' '金穗' '巴斯克' '摩奇' '妞·丝达特' '麦香威尔' 'SIMPLY'\n", " '杭老大' '徐泉记' '俊滢红' '倍成鲜肉酥饼' '雅乐可' '可士达' '语奇' '京日' '安贝' '广州酒家' '食之旺' '牛绅士'\n", " '力创' '新良' 'Q天下' '莫利' '维拉' '戴维娜' '乔翊娅' '三豆' '克拉农场' '鹰牌' '昭和' '樱皇' '拿破仑'\n", " '中粮' '黄絮雪' '蓝风车' '艾蒙塔' '锦心' '/' '克拉' '芭蜂' '植赋' '贝菜琳' '秀爱' 'OLAM' '唯乐福'\n", " '贝莱琳' '竹亿' '奥兰' '蓝钻石' '米婆婆' '三得利' '君度' '迪可派' '阿佩罗' '香博' 'Campari' '蒂诺'\n", " '三只猴子' '阿达' '华冰' '雨润' '尊享' 'beretta' '艾熙雅' '臻富' '百凝' 'Alkary艾可里' '乐斯福'\n", " '早苗' '燕牌' 'PCB油溶色粉' '佳杰' '旭泰源' 'AC色素' '麦穗' '爱满多' '领优' '馨芝味' '凝白' '倍盛'\n", " '茶语天下' '安堤卡' '亿芭利' '蔡合盛' '喜多多' '安娜丽莎' '乔康' '呈香' '麦肯' '乐事' '雪川' '好C冠'\n", " '新汁源' '澄善' '自然尚品' '大师傅' '川果齐味' '众利日盛' '劲宝' '上上乐' '榴妃凡' \"Crop's芒果果茸\" '阿榴哥'\n", " '憨思' '新仙尼' '艾维尼' '爱弗特' '听茶呢语' 'swad' '果为媒' '格林斯凯' 'kos' '星牌' '军臣' '如水'\n", " '湖北万佳园' 'kos金快达' '溯甜' '卡拉农场' '赫薇' '智利' '金黄' 'TST' '防港' '巧卡兹' '唐亦' '百利吉'\n", " '卡彩' '清净园玉米糖浆' '三角龙是吃素的' '清净园' '达芬奇果美' '1883' '嘉吉' '小伙子' '双桥' '元宝'\n", " 'ZILIULIU' '宏泰' '仙妮贝儿' '罗迪' '斐济' '依云EVIAN' '依云' 'SIRMA' '百岁山' 'YOU-C1000'\n", " '泰果乐' '麦多维多' '佳果源' '溢漾好' '菲诺' '丽米' '鸣聪' '维乐鲜' '甘乐堂' '椰子跳动' '椰树' '舌界'\n", " '卡丽玛' '圣培露' '傲牌' '派帕斯' '好望水' 'MOMA' '麦子和麦' '奥麦星球' 'AmazingOat' '奥帝牌'\n", " '利宾纳' '俏果牌' 'ACP' '果多' '果然' '鲜友' '敬松' '苏亚' '益植好' '金牌高达' '达川' '芳蜜圆' '芝士坊'\n", " 'UCC' '小鹰咖' '麦隆' '豆+计划' '观树' '金猫' '赛梦达/sammontana' '五丰' '阿波罗' '润之美'\n", " 'FUSU馥苏' '黎宝' '美极' '英士顿' '大杰' '悠焙' '哈斯' '应该' '阳广' '塑料扳手' 'MOSA' '金有辰科技'\n", " \"'焙之玺\" '澳亚牧场' '阳光' '维记' 'CHOYA/蝶矢' '卡曼之巅' '缘牌' '鲁康' '11' '尧胜/康腾' '斯拉槎'\n", " '莫顿' '臻的牛' '爱尚松' 'TS' '凯芮' 'EVA' '蔻曼' '迪比克' '茄意欧' '欧罗蒂' '河南'\n", " 'vanilla&pepper' '千谷粮缘' '凯萨琳' '皮维蒂' '成功烘焙' '科轮' '寇曼' '亨氏' '中国' '维纯'\n", " 'OATOAT' '萨宝多' '九宇' 'Jameson/尊美醇' '圣王' '味群' '荷兰乳牛' '百加得' '佳沛' '无极岛' '肯迪雅'\n", " '奥兰迪' '托纳迪塔' '法仕睿' '卡夫菲力' '莱益塔' '阿根廷' '马苏里拉' '格尔巴尼' '辛尼迪' '含贝' '海融丝诺'\n", " '纽麦福' '乐芙丽娜' '<PERSON><PERSON>保利' '格里则' '皇氏' '兰格格' '维达' '尤崎' '安诺尼' '利文达' '西克莱特'\n", " '费列罗' '蓝黛' '味佳厨' '贝可臻蒂' '北川' '起源' '伊健园' '金凤凰' '艺福堂' '莞花' '泰昌' '奥夫' '爱焙士'\n", " '美煌' '闪味' '派刻' '泰榴伙伴' '旗本' '久和' '三必致' '希美' '嘉乐牌' '新博明' '巴洛尼亚'\n", " '大阳面包职人小麦细粉' '鹤牌' '梦力B' '昌源' 'Gaffaro' '加倍牛' '卡帕诺' '嘉利达' '老虎牌' '金燕子' '欧罗'\n", " 'FLEUR·COULEUR' 'FLEURCOULEUR' 'SAMJIN' '宏雪' '味可' '首榴香' 'Raisins' '焙友'\n", " '艾达曼' '榛臻' 'NH' '金快达浓' '优果坊' '富岛' '龙田' '冰度' '王力咖啡TEISSEIRE' '柯金偶' '祁果果'\n", " '泰象' '美可' '淳轩' '元气氧' '酷椰屿' '柚香谷' 'SIRMA/地中海榭漫柠' '元气森林' '果汁然' '产地' '金牌'\n", " '合萃源' '麦巨豆' '唤醒力' '非同凡饮' '每日姜茶' '诺思库' '晒焙' '益山高' '南侨' '安高天娜' '科轮牌' '辣椒仔'\n", " '是拉差' '兰花' '纷乐旗' 'NZMP' '法兰达' '金顺昌' '意华']\n"]}], "source": ["print(\"品牌列表:\", xm_sku_df['sku_brand'].unique())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["存储温区列表: ['常温' '无' '冷冻' '-18度' '-18℃以下' '5' ' 5-8度' '5-8度' '常温阴凉干燥避光' '25度'\n", " '3°C-7°C冷藏保存' '-18℃以下冷冻' '请置于阴凉干燥处' '开启后冷藏' '冷藏保存' '置于阴凉干燥处' '15-20度'\n", " '16-25度' '0℃-4℃' '2-4' '2~4摄氏度保存' '2-4℃低温保存' '2℃-4℃低温贮存' '-18摄氏度冷冻保存'\n", " '0℃-4℃低温存储' '冷冻保存' '-18℃以下保存' '-18摄氏度冷藏保存' '-18℃及以下' '-18℃或以下' '-18度储存冷冻'\n", " '-18℃冷冻' '阴凉干燥处' '2℃-4℃冷藏' '18度以下冷冻保存' '冷藏' '2-4℃低温储藏' '-18℃' '-18摄氏度冷冻'\n", " '0℃-4℃冷藏' '2-4°C冷藏储存' '2-8度' '4-8度' '20℃' '2-6℃' '2°' '20kg' '冷冻-18'\n", " '18-24度' '12-20度' '0-5度' '4-6度' '2-6度' '18' '2-15度' '未开常温开封4-8度' '2-5度'\n", " '未知' '2-7度' '4-10度' '首次使用前需冷藏2小时' '0-7度' '-18度冷冻' '-16度' '6-10度' '-18度冷藏'\n", " '10-15度' '2-10度' '22' '5-15度' '12-20°C 存放于阴凉干燥处' '3-10度' '11-13度' '5-10度'\n", " '1-5度' '3-7度' '12' '12℃' '7-8度' '5-12度' '8度' '12度' '2-8' '5-20度' '8-15度'\n", " '3-5度' '10度' '10-20度' '3-8度' '2-18度' '1-3度' '2-15度冷藏' '10-25度' '15-25度'\n", " '阴凉干燥避光' ' 3-10度' '2-4度' '10-15' '8-10度' '5到10' '5--15度' '5-8都' '0-10'\n", " '-18°' '6℃以下冷藏' '常温，开封后可冷藏保存' '6-25℃' '4-8℃冷藏保存' '2℃-9℃冷藏' '2-8°'\n", " '请置于冷藏保鲜4℃到7℃冷' '2-8℃' '4℃冷藏保存' '2℃-7℃冷藏' '0-4度' '2-8°C冷藏储存'\n", " '-18℃及以下冷冻贮藏' '-18°C及以下冷冻贮藏' '-18度冷冻保存' '零下18度' '-18°冷冻保存' '-18℃冷冻保存'\n", " '-19度' '0-4℃冷藏' '2-10' '德宝' '0-4℃' '2-25摄氏度' '4-25度' '冷藏2-8℃' '4-8度冷藏保存'\n", " '冷藏36' '阴凉干燥通风' '冷藏0-5度' '1-5℃' '2-6摄氏度' '阴凉干燥以及大于十度的地方存储' '2℃-8℃' '2-4℃'\n", " '2-6' '2℃-25℃' '2-7摄氏度冷藏' '2-25℃' '2-6℃冷藏' '2~6度' '-20度' '温度≤25℃；相对湿度≤6'\n", " '22℃以下阴凉干燥处保存' '请将产品贮存于阴凉及干燥处' '<25℃' '将产品贮存于阴凉及干燥处' '阴凉、干燥、避光'\n", " '请将产品贮存于阴凉干燥处' '常温阴凉干燥处保存' '≤25°C' '低于25度' '≤25' '阴凉干燥避光（温度≤20℃）' '23'\n", " '在通风、清洁、干燥、' '清洁，避光，密封' '放于阴凉通风' '-18摄氏度' '-18' '16度' '阴凉干燥' '常温、避光'\n", " '常温阴凉干燥处' '常温保存' '存放于常温阴凉干燥处' '常温干燥避光' '在阴凉、干燥的环境存储' '请放于阴凉通风干燥处'\n", " '放置于阴凉干燥处' '冷冻-18度存储' '零下18摄氏度' '冷冻（零下18摄氏度）' '-18摄氏度以下' '18℃冷冻保存'\n", " '常温，开封后须冷藏' '常温下保存，避免阳光直射' '阴凉处，恒温室' '阴凉通风干燥处保存' '25℃以下，冷藏最佳'\n", " '25℃一下，冷藏最佳' '1度' '-18℃及以下冷冻保存' '14-20℃' '5-15℃' '置于阴凉避光处' '干燥通风处'\n", " '请置于阴凉、避光避热处' '置于阴凉、避光、避热处' '储存於阴凉、干燥处' '储存于阴凉、干燥处' '储藏于阴凉干燥处'\n", " '-18℃以下冷冻保存' '常温避光' '常温，开食后需冷藏' '常温，开封后需0-7℃冷藏' '常温储存' '常温，避光' '15℃'\n", " '阴凉干燥处储存' '室温，避热，避水' '温度≤38度，湿度＜70%' '干燥阴凉密封防潮' '阴凉干燥避光密封' '阴凉、干燥、通风'\n", " '18度' '阴凉干燥处存放' '常温，阴凉干燥（建议28-32℃）' '常温存储' '阴凉干燥处保存' '阴凉通风处' '存放于阴凉通风干燥处'\n", " '请放置于阴凉干燥' '8℃以下冷藏运输与贮存' '阴凉干燥处，2℃-25℃' '常温阴凉干燥' '2℃-26℃存储' '常温避光保存' '中国'\n", " '置于阿凉干燥处' '常温通风' '存放于阴凉干燥处' '储存于阴凉干燥处' '避免阳光直射，放置通风厨存储' '常温避光，冷藏更佳'\n", " '存放于阴凉干燥通风处' '阴凉干燥处常温初查' '请于6℃以下冷保存' '-18摄氏度保存' '于阴凉干燥处储藏' '2-4℃冷藏保存' '1'\n", " '听茶昵语' '4-12度' '无限大' '-10度' '阴凉避光' '6-25度' '-4度到27度' '4-11度' '南通' ' 4-8度'\n", " ' 2-8度' '-18°C度以下冷冻保存' '零下5℃至零下25℃冷冻保' '2-4°' '1℃到10℃保存' '4-7度'\n", " '置于0-4℃的环境下冷藏' '2℃-8℃冷藏' '4-10℃' '1-8摄氏度冷藏保存' '2-25度' '常温，开封后冷藏'\n", " '2℃-6℃冷藏' '0-6度低温保存' '12-20℃' '阴凉干燥处避光保存' '10℃-22℃' '阴凉干燥，避免阳光' '德馨斑斓味果冻'\n", " '置于阴凉通风干燥处' '常温、阴凉干燥处' '干燥阴凉处' '低温' '彤幸玖隆' '-18度及以下' '0-25度' '阴凉干燥避光处'\n", " '请避免放置于高温处' '0℃以上' '18℃以下冷冻' '18℃以下' '-18°冷冻' '阴凉、干燥' '常温避光干燥'\n", " '4℃~27℃、避免阳光直射' '阴凉，干燥，通风处' '-4℃到4℃' '凉爽干燥密封' '干燥阴凉避光密封' '放阴凉干燥处保存'\n", " '常温，不超过38度' '常温避光贮存' '避免阳光直射及高温' '请勿放于高温' '25℃' '常温阴凉处' '0-28度' '严防高温潮湿'\n", " '置于阴凉干燥处，密封' '常温保存，开盖后冷藏' '1-5度冷藏' '16-18度']\n"]}], "source": ["print(\"存储温区列表:\", xm_sku_df['tempature'].unique())"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["品种列表: ['' '青金桔' '无籽青柠檬' '意式传统熟火腿' '意式小食拼盘' '美多萨拉米' '火腿' '紫香1号百香果' '有籽黄柠檬' '冬瓜老姜'\n", " '红富士苹果' '红心火龙果' '白心火龙果' '红西柚' '红心无籽西瓜' '哈密瓜' '皇冠梨' '菲律宾凤梨' '爱媛橙' '有籽青柠檬'\n", " '红心柚' '脐橙' '新奇士橙' '伦晚橙' '三红蜜柚' '元红脐橙' '香水柠檬' '青提' '葡萄柚' '软柿' '桂味荔枝' '冬枣'\n", " '巨峰葡萄' '金钻凤梨' '蜜桔' '野生苹果' '红心黑籽西瓜' '秋月梨' '千禧番茄' '爱妃苹果' '凤梨' '牛油果' '车厘子'\n", " '红颜草莓' '黑莓' '大青芒' '红凯特芒' '青凯特芒' '金煌芒' '台农' '无籽红提' '黄凯特芒' '白心蜜柚' '其他柚子'\n", " '耙耙柑' '沃柑' '佛手' '粑粑柑' '不知火丑橘' '水仙芒' '油桃' '白凤' '红皮' '突围水蜜桃' '椰青' '椰皇'\n", " '阿克苏苹果' '小番茄' '龙眼' '青皇冠梨' '进口龙眼' '红心猕猴桃' '黄金桔' '鸡心黄皮' '春雪水蜜桃' '三华李'\n", " '奇异果金果' '黄肉波罗蜜' '雪梨' '阳山水蜜桃' '贡梨' '西梅' '青金煌芒' '水晶苹果' '甜瓜' '黄心有籽西瓜'\n", " '黄金百香果' '圣女果' '奶油草莓' '章姬奶油草莓' '大青芒/金煌芒' '红心木瓜' '莲雾' '突尼斯软籽石榴' '鸭梨' '海沃德'\n", " '梅陇网纹瓜' '夏橙' '苹果枣' '百香果' '有籽麒麟' '其他蜜瓜' '八六王' '网纹瓜' '黄金蜜瓜' '香野草莓' '白草莓'\n", " '猕猴桃绿果' '金艳' '徐香猕猴桃' '燕窝果' '菠萝' '普通红提' '其他提子' '红提' '茉莉香葡萄' '夏黑葡萄' '国产蓝莓'\n", " '白心番石榴' '红心番石榴' '红心芭乐' '山竹' '雪莲果' '蜜梨' '酥梨' '苹果' '丑苹果' '花牛苹果' '青苹果'\n", " '进口苹果' '其他苹果' '红富士' '枇杷' '砀山梨' '蒙特瑞草莓' '四季青柠檬' '红树莓' '千层蛋糕' '小茶点' '芝士蛋糕'\n", " '慕斯蛋糕' '/' '瑞士卷' '半熟芝士' '麦芬' '贝果' '熔岩巧克力' '荔浦芋头' '鸡蛋' '甜椒' '意式美食拼盘'\n", " '火鸡胸火腿片' '黑胡椒牛肉' '组合装' '美早' '毛桃' '甜油柑' '草莓' '黔莓' '红金龙' '樱桃' '脆皮金桔' '金桔'\n", " '青柑' '冰糖橙' '其他橙' '芦柑' '砂糖橘' '红肉菠萝蜜' '鹰嘴芒' '妃子笑荔枝' '东魁杨梅' '芙蓉李' '其他李子'\n", " '奇异果绿果' '羊角蜜瓜' '水果黄瓜' '牛奶枣' '大青枣' '果干礼盒' '特小凤' '其他西瓜' '香瓜' '木瓜' '黄千禧'\n", " '其他无籽葡萄' '苹果蕉' '进口香蕉' '脆冠梨' '红香酥' '香梨' '1' '蛋糕' '贝贝番茄']\n"]}], "source": ["import json\n", "xm_sku_df['品种']=xm_sku_df['other_properties'].apply(lambda x: json.loads(x).get('品种', ''))\n", "\n", "print(\"品种列表:\", xm_sku_df['品种'].unique())"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["鲜果等级列表: ['' '二级' '一级' '三级' '普通' '其他' '精品']\n"]}], "source": ["xm_sku_df['鲜果等级']=xm_sku_df['other_properties'].apply(lambda x: json.loads(x).get('级别', ''))\n", "\n", "print(\"鲜果等级列表:\", xm_sku_df['鲜果等级'].unique())"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["乳脂含量列表: ['' '牛油65%奶油35%' '冷冻' '3.4%' '3.6g' '35%' '23.6%' '34.8g' '26g' '38%'\n", " '17.2%' '-' '16.2g' '8.4g' '0' '21.4g' '25.5g' '8.2g' '4.6g' '0g'\n", " '脂肪含量43.5g' '35.1g' '20.1g' '47g' '26.3%' '35g' '35.5%' '8.3g' '3.5'\n", " '14g' '1' '35.1' '10.9g' '36%' '35' '28%' '35.4%' '35.1%' '32g' '26.5%'\n", " '35.7g' '18%' '24.4%' '30.5%' '20.2g' '35.5g' '51%' '25.9g' '3.7g' '3.7'\n", " '3.8%' '3.8g' '8%' '3.5%' '3.8' '3.6' '3.6%' '5.5' '3.2g' '4%' '4.0%'\n", " '6%' '3.5g/100mL' '3.2' '3.7克' '3.9%' '3.9g/100L' '4.0g' '3.4' '5.0g'\n", " '38g' '11%' '21.9g' '20.7g/100g' '11g' '21.9%' '7.5g' '3.5g' '3.1g' '6g'\n", " '11' '36.5g' '31.3g' '23.2g' '10.1' '19.2%' '24.7%' '29％' '36％' '34.8%'\n", " '36g' '35.9%' '30.5' '26%' '3.2g/100ml' '20%' '4.1g' '2.9%' '0.9%']\n"]}], "source": ["xm_sku_df['乳脂含量']=xm_sku_df['other_properties'].apply(lambda x: json.loads(x).get('乳脂含量', ''))\n", "print(\"乳脂含量列表:\", xm_sku_df['乳脂含量'].unique())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}