#!/usr/bin/env python3
"""
完整的评测系统验证和测试脚本
"""

import sys
import os
import json
import requests
from datetime import datetime

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_search_api():
    """测试搜索API接口"""
    base_url = "http://localhost:5800/api/search-content"
    
    print("🔍 测试搜索API接口...")
    try:
        response = requests.get(base_url, params={
            "query": "芒果",
            "city": "杭州",
            "page_size": 5
        }, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 搜索API接口响应成功")
            print(f"📊 查询词: {data.get('query')}")
            print(f"📈 结果数量: {len(data.get('result_container', []))} 个版本")
            return True
        else:
            print(f"❌ 搜索API返回错误状态码: {response.status_code}")
            print(f"响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 搜索API测试失败: {e}")
        return False

def test_category_prediction():
    """测试查询词数据的可用性"""
    print("\n🔍 测试category_prediction模块...")
    try:
        from category_prediction import top_query_df, last_n_days
        
        print(f"📊 category_prediction模块加载成功")
        print(f"📝 查询词总行数: {len(top_query_df)}")
        print(f"📅 最后N天设置: {last_n_days}")
        
        # 显示部分数据
        print("\n📈 数据预览:")
        preview = top_query_df[['query', 'searched_users', 'user_ctr', '搜索频次标签']]
        print(preview.head(5))
        
        # 检查user_ctr < 0.7的数据
        low_ctr = top_query_df[top_query_df['user_ctr'] < 0.7]
        print(f"\n📉 user_ctr < 0.7的查询词数量: {len(low_ctr)}")
        
        if len(low_ctr) > 0:
            print("🔎 部分低CTR查询词:")
            print(low_ctr[['query', 'searched_users', 'user_ctr']].head(5))
            
        return True, low_ctr
        
    except Exception as e:
        print(f"❌ category_prediction模块测试失败: {e}")
        return False, None

def generate_test_queries(count=10):
    """生成测试查询词"""
    
    # 首先从category_prediction获取数据
    success, low_ctr_df = test_category_prediction()
    
    queries = []
    
    if success and low_ctr_df is not None and len(low_ctr_df) > 0:
        # 使用真实数据
        top_queries = low_ctr_df.sort_values('searched_users', ascending=False).head(count)
        
        for _, row in top_queries.iterrows():
            try:
                queries.append({
                    "query": str(row['query']),
                    "searched_users": int(row['searched_users']),
                    "user_ctr": float(row['user_ctr']),
                    "search_frequency": str(row.get('搜索频次标签', '未知'))
                })
            except:
                continue
    
    # 如果没有数据，使用备用数据
    if not queries:
        print("使用备用测试数据...")
        queries = [
            {"query": "芒果", "searched_users": 1200, "user_ctr": 0.45, "search_frequency": "高频"},
            {"query": "奶油", "searched_users": 980, "user_ctr": 0.52, "search_frequency": "高频"},
            {"query": "苹果", "searched_users": 850, "user_ctr": 0.38, "search_frequency": "高频"},
            {"query": "香蕉", "searched_users": 720, "user_ctr": 0.41, "search_frequency": "高频"},
            {"query": "草莓", "searched_users": 650, "user_ctr": 0.33, "search_frequency": "高频"},
        ]
    
    return queries

def test_environment():
    """测试环境变量"""
    print("\n🔍 测试环境变量...")
    
    required_vars = ["XM_LLM_API_KEY", "XM_LLM_BASE_URL", "XM_LLM_MODEL"]
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        else:
            print(f"✅ {var}: 已设置")
    
    if missing_vars:
        print(f"❌ 缺失环境变量: {missing_vars}")
        return False
    
    return True

def main():
    """主测试流程"""
    print("🚀 AI搜索评测系统测试开始...")
    print("=" * 50)
    
    # 1. 测试环境变量
    env_ok = test_environment()
    
    # 2. 测试搜索API
    api_ok = test_search_api()
    
    # 3. 测试查询词数据
    data_ok, low_ctr_df = test_category_prediction()
    
    # 4. 生成测试查询词
    queries = generate_test_queries(10)
    
    print("\n" + "=" * 50)
    print("📋 测试结果总结:")
    print(f"环境变量: {'✅' if env_ok else '❌'}")
    print(f"搜索API: {'✅' if api_ok else '❌'}")
    print(f"查询数据: {'✅'如果 data_ok else '❌'}")
    
    print(f"\n📊 最终测试查询词 ({len(queries)}个):")
    for i, q in enumerate(queries, 1):
        print(f"{i:2d}. {q['query']} (用户:{q['searched_users']:,}, CTR:{q['user_ctr']:.1%})")
    
    # 保存测试文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    test_file = f"/tmp/test_queries_{timestamp}.json"
    with open(test_file, 'w', encoding='utf-8') as f:
        json.dump(queries, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 测试数据已保存: {test_file}")
    
    # 提供运行建议
    print("\n🎯 运行建议:")
    print("1. 检查搜索服务是否运行: python3 app.py (端口5800)")
    print("2. 设置环境变量: cp evaluate_search_env.example .env")
    print("3. 编辑.env文件填入API密钥")
    print("4. 或者运行完整评测: python3 evaluate_search_queries.py")

if __name__ == "__main__":
    main()