{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-03-06 14:38:56 - INFO - Tunnel session created: <InstanceDownloadSession id=202503061438565330f60b03dd404e project_name=summerfarm_ds_dev instance_id=20250306063850916g98fgpnis6t6>\n", "2025-03-06 14:38:57 - INFO - sql:\n", "\n", "SELECT  a.sku_id\n", "        ,a.spu_id\n", "        ,a.spu_name\n", "        ,b.sku_name\n", "        ,a.spu_no\n", "        ,a.disc\n", "        ,a.store_method\n", "        ,a.sku_type\n", "        ,a.category1\n", "        ,a.category2\n", "        ,a.category3\n", "        ,a.category4\n", "        ,a.sku_spec\n", "        ,a.origin\n", "        ,a.sku_brand\n", "        ,a.temp<PERSON>\n", "        ,a.other_properties\n", "        ,a.sub_type\n", "        ,a.ds\n", "        ,price.area_price\n", "        ,COALESCE(b.sku_pic,c.picture_path,'404.jpg') AS img_url\n", "        ,d.total_gmv as current_month_gmv\n", "FROM    summerfarm_tech.dim_sku_df a\n", "INNER JOIN summerfarm_tech.ods_inventory_df b\n", "ON      b.ds = MAX_PT('summerfarm_tech.ods_inventory_df')\n", "AND     a.sku_id = b.sku\n", "INNER JOIN summerfarm_tech.ods_products_df c\n", "ON      c.ds = MAX_PT('summerfarm_tech.ods_products_df')\n", "AND     a.spu_id = c.pd_id\n", "INNER JOIN  (\n", "                SELECT  aa.sku\n", "                        ,ARRAY_JOIN(COLLECT_SET(CONCAT(bb.area_name,'¥',aa.price)),',') AS area_price\n", "                FROM    summerfarm_tech.ods_area_sku_df aa\n", "                INNER JOIN summerfarm_tech.ods_area_df bb\n", "                ON      aa.area_no = bb.area_no\n", "                AND     bb.ds = MAX_PT('summerfarm_tech.ods_area_df')\n", "                WHERE   aa.ds = MAX_PT('summerfarm_tech.ods_area_sku_df')\n", "                AND     bb.area_name IN ('上海','杭州','深圳','广州','苏州','重庆','成都','宁波','武汉普冷','南京','青岛','长沙普冷')\n", "                AND     aa.on_sale = 1\n", "                AND     bb.status = 1\n", "                GROUP BY aa.sku\n", "            ) price\n", "ON      a.sku_id = price.sku\n", "LEFT JOIN   (\n", "                SELECT  sku_id\n", "                        ,SUM(gmv) total_gmv\n", "                        ,COUNT(1) total_count\n", "                        ,SUM(sales_volume) total_sales_volume\n", "                FROM    summerfarm_tech.app_crm_sku_month_gmv_di\n", "                WHERE   ds = MAX_PT('summerfarm_tech.app_crm_sku_month_gmv_di')\n", "                GROUP BY sku_id\n", "            ) d\n", "ON      a.sku_id = d.sku_id\n", "WHERE   a.ds = MAX_PT('summerfarm_tech.dim_sku_df')\n", "AND     a.sub_type != 4\n", "AND     a.outdated != 1\n", "ORDER BY d.total_gmv DESC\n", ";\n", "\n", "columns:Index(['sku_id', 'spu_id', 'spu_name', 'sku_name', 'spu_no', 'disc',\n", "       'store_method', 'sku_type', 'category1', 'category2', 'category3',\n", "       'category4', 'sku_spec', 'origin', 'sku_brand', 'tempature',\n", "       'other_properties', 'sub_type', 'ds', 'area_price', 'img_url',\n", "       'current_month_gmv'],\n", "      dtype='object')\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>sku_id</th>\n", "      <th>spu_id</th>\n", "      <th>spu_name</th>\n", "      <th>sku_name</th>\n", "      <th>spu_no</th>\n", "      <th>disc</th>\n", "      <th>store_method</th>\n", "      <th>sku_type</th>\n", "      <th>category1</th>\n", "      <th>category2</th>\n", "      <th>category3</th>\n", "      <th>category4</th>\n", "      <th>sku_spec</th>\n", "      <th>origin</th>\n", "      <th>sku_brand</th>\n", "      <th>tempature</th>\n", "      <th>other_properties</th>\n", "      <th>sub_type</th>\n", "      <th>ds</th>\n", "      <th>area_price</th>\n", "      <th>img_url</th>\n", "      <th>current_month_gmv</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>N001S01R005</td>\n", "      <td>56</td>\n", "      <td>安佳淡奶油</td>\n", "      <td>None</td>\n", "      <td>56</td>\n", "      <td>1L*12盒</td>\n", "      <td>冷藏</td>\n", "      <td>0</td>\n", "      <td>乳制品</td>\n", "      <td>乳制品</td>\n", "      <td>稀奶油</td>\n", "      <td>搅打型稀奶油</td>\n", "      <td>1L*12盒</td>\n", "      <td>新西兰</td>\n", "      <td>安佳</td>\n", "      <td>2-4度</td>\n", "      <td>{\"每100g乳脂含量\":\"35.5g\",\"乳脂含量\":\"35.5%\",\"商品性质\":\"常规\"}</td>\n", "      <td>3</td>\n", "      <td>20250305</td>\n", "      <td>上海¥495,南京¥495,宁波¥495,广州¥495,成都¥495,杭州¥495,武汉普冷¥505,深圳¥495,苏州¥495,重庆¥495,长沙普冷¥505,青岛¥495</td>\n", "      <td>picture-path/a435zn07h3jv32ovx.png</td>\n", "      <td>3397804.2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>N001S01R002</td>\n", "      <td>52</td>\n", "      <td>爱乐薇(铁塔)淡奶油</td>\n", "      <td>None</td>\n", "      <td>52</td>\n", "      <td>1L*12盒</td>\n", "      <td>冷藏</td>\n", "      <td>0</td>\n", "      <td>乳制品</td>\n", "      <td>乳制品</td>\n", "      <td>稀奶油</td>\n", "      <td>搅打型稀奶油</td>\n", "      <td>1L*12盒</td>\n", "      <td>法国</td>\n", "      <td>铁塔</td>\n", "      <td>2-8度</td>\n", "      <td>{\"每100g乳脂含量\":\"35.1g\",\"乳脂含量\":\"35.1g\",\"商品性质\":\"常规\"}</td>\n", "      <td>3</td>\n", "      <td>20250305</td>\n", "      <td>上海¥575,南京¥575,宁波¥575,广州¥575,成都¥575,杭州¥575,武汉普冷¥575,深圳¥575,苏州¥575,重庆¥575,长沙普冷¥575,青岛¥575</td>\n", "      <td>sku_list1487850267933898</td>\n", "      <td>1549096.62</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>L001S01R001</td>\n", "      <td>71</td>\n", "      <td>蓝风车蓝米吉稀奶油</td>\n", "      <td>None</td>\n", "      <td>71</td>\n", "      <td>1L*12盒</td>\n", "      <td>冷藏</td>\n", "      <td>0</td>\n", "      <td>乳制品</td>\n", "      <td>乳制品</td>\n", "      <td>稀奶油</td>\n", "      <td>搅打型稀奶油</td>\n", "      <td>1L*12盒</td>\n", "      <td>英国</td>\n", "      <td>蓝风车</td>\n", "      <td>2-8度</td>\n", "      <td>{\"每100g乳脂含量\":\"38g\",\"乳脂含量\":\"38g\",\"商品性质\":\"常规\"}</td>\n", "      <td>3</td>\n", "      <td>20250305</td>\n", "      <td>上海¥630,南京¥630,宁波¥630,广州¥630,成都¥630,杭州¥630,武汉普冷¥630,深圳¥630,苏州¥630,重庆¥630,长沙普冷¥630,青岛¥630</td>\n", "      <td>1516846274306</td>\n", "      <td>659378.57</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>N001H01Y003</td>\n", "      <td>94</td>\n", "      <td>安佳无盐大黄油25kg</td>\n", "      <td>None</td>\n", "      <td>94</td>\n", "      <td>25KG*1箱</td>\n", "      <td>冷冻</td>\n", "      <td>0</td>\n", "      <td>乳制品</td>\n", "      <td>乳制品</td>\n", "      <td>黄油</td>\n", "      <td>无盐黄油</td>\n", "      <td>25KG*1箱</td>\n", "      <td>新西兰</td>\n", "      <td>安佳</td>\n", "      <td>-18度</td>\n", "      <td>{\"商品性质\":\"常规\"}</td>\n", "      <td>3</td>\n", "      <td>20250305</td>\n", "      <td>上海¥1730,南京¥1730,宁波¥1730,广州¥1730,成都¥1730,杭州¥1730,武汉普冷¥1730,深圳¥1730,苏州¥1730,重庆¥1730,长沙普冷¥1730,青岛¥1730</td>\n", "      <td>picture-path/15469395337270</td>\n", "      <td>657372.1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>464633265</td>\n", "      <td>859</td>\n", "      <td>艾恩摩尔35%淡奶油</td>\n", "      <td>None</td>\n", "      <td>464633</td>\n", "      <td>1L*12瓶</td>\n", "      <td>冷藏</td>\n", "      <td>0</td>\n", "      <td>乳制品</td>\n", "      <td>乳制品</td>\n", "      <td>稀奶油</td>\n", "      <td>搅打型稀奶油</td>\n", "      <td>1L*12瓶</td>\n", "      <td>爱尔兰</td>\n", "      <td>艾恩摩尔</td>\n", "      <td>4-8度</td>\n", "      <td>{\"每100g乳脂含量\":\"35g\",\"乳脂含量\":\"35%\",\"商品性质\":\"常规\"}</td>\n", "      <td>3</td>\n", "      <td>20250305</td>\n", "      <td>上海¥455,南京¥455,宁波¥455,广州¥455,成都¥455,杭州¥455,武汉普冷¥455,深圳¥455,苏州¥455,重庆¥455,长沙普冷¥455,青岛¥455</td>\n", "      <td>picture-path/vv13en7gqv9lz76xx.jpg</td>\n", "      <td>646823.68</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["        sku_id  spu_id     spu_name sku_name  spu_no     disc store_method  \\\n", "0  N001S01R005      56        安佳淡奶油     None      56   1L*12盒           冷藏   \n", "1  N001S01R002      52   爱乐薇(铁塔)淡奶油     None      52   1L*12盒           冷藏   \n", "2  L001S01R001      71    蓝风车蓝米吉稀奶油     None      71   1L*12盒           冷藏   \n", "3  N001H01Y003      94  安佳无盐大黄油25kg     None      94  25KG*1箱           冷冻   \n", "4    464633265     859   艾恩摩尔35%淡奶油     None  464633   1L*12瓶           冷藏   \n", "\n", "   sku_type category1 category2 category3 category4 sku_spec origin sku_brand  \\\n", "0         0       乳制品       乳制品       稀奶油    搅打型稀奶油   1L*12盒    新西兰        安佳   \n", "1         0       乳制品       乳制品       稀奶油    搅打型稀奶油   1L*12盒     法国        铁塔   \n", "2         0       乳制品       乳制品       稀奶油    搅打型稀奶油   1L*12盒     英国       蓝风车   \n", "3         0       乳制品       乳制品        黄油      无盐黄油  25KG*1箱    新西兰        安佳   \n", "4         0       乳制品       乳制品       稀奶油    搅打型稀奶油   1L*12瓶    爱尔兰      艾恩摩尔   \n", "\n", "  tempature                                  other_properties  sub_type  \\\n", "0      2-4度  {\"每100g乳脂含量\":\"35.5g\",\"乳脂含量\":\"35.5%\",\"商品性质\":\"常规\"}         3   \n", "1      2-8度  {\"每100g乳脂含量\":\"35.1g\",\"乳脂含量\":\"35.1g\",\"商品性质\":\"常规\"}         3   \n", "2      2-8度      {\"每100g乳脂含量\":\"38g\",\"乳脂含量\":\"38g\",\"商品性质\":\"常规\"}         3   \n", "3      -18度                                     {\"商品性质\":\"常规\"}         3   \n", "4      4-8度      {\"每100g乳脂含量\":\"35g\",\"乳脂含量\":\"35%\",\"商品性质\":\"常规\"}         3   \n", "\n", "         ds  \\\n", "0  20250305   \n", "1  20250305   \n", "2  20250305   \n", "3  20250305   \n", "4  20250305   \n", "\n", "                                                                                            area_price  \\\n", "0              上海¥495,南京¥495,宁波¥495,广州¥495,成都¥495,杭州¥495,武汉普冷¥505,深圳¥495,苏州¥495,重庆¥495,长沙普冷¥505,青岛¥495   \n", "1              上海¥575,南京¥575,宁波¥575,广州¥575,成都¥575,杭州¥575,武汉普冷¥575,深圳¥575,苏州¥575,重庆¥575,长沙普冷¥575,青岛¥575   \n", "2              上海¥630,南京¥630,宁波¥630,广州¥630,成都¥630,杭州¥630,武汉普冷¥630,深圳¥630,苏州¥630,重庆¥630,长沙普冷¥630,青岛¥630   \n", "3  上海¥1730,南京¥1730,宁波¥1730,广州¥1730,成都¥1730,杭州¥1730,武汉普冷¥1730,深圳¥1730,苏州¥1730,重庆¥1730,长沙普冷¥1730,青岛¥1730   \n", "4              上海¥455,南京¥455,宁波¥455,广州¥455,成都¥455,杭州¥455,武汉普冷¥455,深圳¥455,苏州¥455,重庆¥455,长沙普冷¥455,青岛¥455   \n", "\n", "                              img_url current_month_gmv  \n", "0  picture-path/a435zn07h3jv32ovx.png         3397804.2  \n", "1            sku_list1487850267933898        1549096.62  \n", "2                       1516846274306         659378.57  \n", "3         picture-path/15469395337270          657372.1  \n", "4  picture-path/vv13en7gqv9lz76xx.jpg         646823.68  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.path.append(\"../\")\n", "\n", "from odps_client import get_odps_sql_result_as_df\n", "\n", "sku_dim_query = \"\"\"\n", "SELECT  a.sku_id\n", "        ,a.spu_id\n", "        ,a.spu_name\n", "        ,b.sku_name\n", "        ,a.spu_no\n", "        ,a.disc\n", "        ,a.store_method\n", "        ,a.sku_type\n", "        ,a.category1\n", "        ,a.category2\n", "        ,a.category3\n", "        ,a.category4\n", "        ,a.sku_spec\n", "        ,a.origin\n", "        ,a.sku_brand\n", "        ,a.temp<PERSON>\n", "        ,a.other_properties\n", "        ,a.sub_type\n", "        ,a.ds\n", "        ,price.area_price\n", "        ,COALESCE(b.sku_pic,c.picture_path,'404.jpg') AS img_url\n", "        ,d.total_gmv as current_month_gmv\n", "FROM    summerfarm_tech.dim_sku_df a\n", "INNER JOIN summerfarm_tech.ods_inventory_df b\n", "ON      b.ds = MAX_PT('summerfarm_tech.ods_inventory_df')\n", "AND     a.sku_id = b.sku\n", "INNER JOIN summerfarm_tech.ods_products_df c\n", "ON      c.ds = MAX_PT('summerfarm_tech.ods_products_df')\n", "AND     a.spu_id = c.pd_id\n", "INNER JOIN  (\n", "                SELECT  aa.sku\n", "                        ,ARRAY_JOIN(COLLECT_SET(CONCAT(bb.area_name,'¥',aa.price)),',') AS area_price\n", "                FROM    summerfarm_tech.ods_area_sku_df aa\n", "                INNER JOIN summerfarm_tech.ods_area_df bb\n", "                ON      aa.area_no = bb.area_no\n", "                AND     bb.ds = MAX_PT('summerfarm_tech.ods_area_df')\n", "                WHERE   aa.ds = MAX_PT('summerfarm_tech.ods_area_sku_df')\n", "                AND     bb.area_name IN ('上海','杭州','深圳','广州','苏州','重庆','成都','宁波','武汉普冷','南京','青岛','长沙普冷')\n", "                AND     aa.on_sale = 1\n", "                AND     bb.status = 1\n", "                GROUP BY aa.sku\n", "            ) price\n", "ON      a.sku_id = price.sku\n", "LEFT JOIN   (\n", "                SELECT  sku_id\n", "                        ,SUM(gmv) total_gmv\n", "                        ,COUNT(1) total_count\n", "                        ,SUM(sales_volume) total_sales_volume\n", "                FROM    summerfarm_tech.app_crm_sku_month_gmv_di\n", "                WHERE   ds = MAX_PT('summerfarm_tech.app_crm_sku_month_gmv_di')\n", "                GROUP BY sku_id\n", "            ) d\n", "ON      a.sku_id = d.sku_id\n", "WHERE   a.ds = MAX_PT('summerfarm_tech.dim_sku_df')\n", "AND     a.sub_type != 4\n", "AND     a.outdated != 1\n", "ORDER BY d.total_gmv DESC\n", ";\n", "\"\"\"\n", "sku_dim_df = get_odps_sql_result_as_df(sku_dim_query)\n", "sku_dim_df.head(5)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.17"}}, "nbformat": 4, "nbformat_minor": 2}