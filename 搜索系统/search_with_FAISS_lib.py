import sys
import sqlite3
import os
import pandas as pd

# Add the scripts directory to the sys.path
sys.path.append("../")

from odps_client import get_odps_sql_result_as_df

sku_dim_query = """
WITH spu_properties AS 
(
    SELECT  a.pd_id
            ,ARRAY_JOIN(COLLECT_SET(CONCAT(b.name,":",a.products_property_value)),', ') AS spu_properties
    FROM    summerfarm_tech.ods_products_property_value_df a
    INNER JOIN summerfarm_tech.ods_products_property_df b
    ON      b.ds = MAX_PT('summerfarm_tech.ods_products_property_df')
    AND     a.products_property_id = b.id
    WHERE   a.ds = MAX_PT('summerfarm_tech.ods_products_property_value_df')
    AND     b.name IN ('品牌','产地','口味','品种','果规')
    GROUP BY a.pd_id
)
SELECT  a.spu_id
        ,a.spu_name
        ,a.sku_brand AS brand
        ,pp.spu_properties
        ,CONCAT(category2,'_',category3,'_',category4) category_path
        ,ARRAY_JOIN(COLLECT_SET(sku_id),',') sku_id_list
        ,CONCAT(MIN(min_price),'~',MAX(max_price)) AS price_range
FROM    summerfarm_tech.dim_sku_df a
LEFT JOIN spu_properties pp
ON      a.spu_id = pp.pd_id
INNER JOIN  (
                SELECT  sku
                        ,MIN(price) min_price
                        ,MAX(price) max_price
                FROM    summerfarm_tech.ods_area_sku_df b
                WHERE   b.ds = MAX_PT('summerfarm_tech.ods_area_sku_df')
                AND     b.on_sale = 1
                AND     b.m_type = 0
                GROUP BY sku
            ) c
ON      c.sku = a.sku_id
WHERE   a.ds = MAX_PT('summerfarm_tech.dim_sku_df')
GROUP BY a.spu_id
         ,a.spu_name
         ,category_path
         ,pp.spu_properties
         ,a.sku_brand;
"""
spu_dim_df = get_odps_sql_result_as_df(sku_dim_query)

db_path = os.path.expanduser("~/sqlite/xianmu_spu_dim_df.db")
embedding_path = os.path.expanduser("~/sqlite/embeddings.db")
os.makedirs(os.path.dirname(db_path), exist_ok=True)
os.makedirs(os.path.dirname(embedding_path), exist_ok=True)

conn = sqlite3.connect(db_path)
embedding_conn = sqlite3.connect(embedding_path)
embedding_conn.execute(
    """
    CREATE TABLE IF NOT EXISTS embeddings (
        input_text TEXT PRIMARY KEY,
        embedding TEXT
    )
"""
)

spu_dim_df.to_sql("spu_dim_df", conn, if_exists="replace", index=False)

import logging
import time
import json
from typing import List
import numpy as np
import faiss
from tqdm import tqdm


from openai import AzureOpenAI
import httpx

client = AzureOpenAI(
    # https://esat-us.openai.azure.com/openai/deployments/text-embedding-3-small/embeddings?api-version=2023-05-15
    api_version="2023-07-01-preview",
    azure_endpoint="https://esat-us.openai.azure.com",
    api_key=os.getenv("AZURE_API_KEY_XM", "please set:AZURE_API_KEY_XM"),
    http_client=httpx.Client(proxies={"http://": None, "https://": None}),
)

embedding_model = os.getenv("AZURE_EMBEDDING_MODEL", "text-embedding-3-small")


def get_embedding_directly_from_azure(input: list[str]) -> list[list]:
    embbed = client.embeddings.create(model=embedding_model, input=input)
    return [em.embedding for em in embbed.data]


def get_embedding(input_texts: list[str]) -> list[np.ndarray]:
    embeddings_dict = {}
    embedding_conn = sqlite3.connect(embedding_path)

    # 从数据库中获取现有的嵌入向量
    with embedding_conn:
        cursor = embedding_conn.cursor()
        # 使用参数化查询来构建IN子句
        placeholders = ', '.join(['?'] * len(input_texts))
        cursor.execute(
            f"SELECT input_text, embedding FROM embeddings WHERE input_text IN ({placeholders})",
            tuple(input_texts)
        )
        results = cursor.fetchall()

    for text, embedding_str in results:
        # 假设嵌入向量存储为字符串
        embeddings_dict[text] = json.loads(embedding_str)

    # 识别需要嵌入的文本
    texts_to_embed = [text for text in input_texts if text not in embeddings_dict]

    # 获取缺失文本的嵌入向量
    if texts_to_embed:
        new_embeddings = get_embedding_directly_from_azure(texts_to_embed)

        # 在数据库中存储新的嵌入向量
        with embedding_conn:
            cursor = embedding_conn.cursor()
            for text, embedding in zip(texts_to_embed, new_embeddings):
                try:
                    cursor.execute(
                        "INSERT INTO embeddings (input_text, embedding) VALUES (?, ?)",
                        (text, json.dumps(embedding)),
                    )
                except sqlite3.IntegrityError:
                    # 如果因为input_text重复而插入失败，则更新现有的记录
                    cursor.execute(
                        "UPDATE embeddings SET embedding = ? WHERE input_text = ?",
                        (json.dumps(embedding), text)
                    )
            embedding_conn.commit()  # 提交更改
        for text, embedding in zip(texts_to_embed, new_embeddings):
            embeddings_dict[text] = embedding

    # 按照正确的顺序组合所有嵌入向量，并作为numpy数组返回.
    return np.array([embeddings_dict[text] for text in input_texts], dtype=np.float32)


print(get_embedding(['hello','how you doing?']))


# 1. 数据预处理和特征工程
# 组合多个字段作为文本特征,增加字段权重
spu_dim_df_clean_100 = spu_dim_df.head(50000)
spu_dim_df_clean_100["brand"] = spu_dim_df_clean_100["brand"].fillna("")
spu_dim_df_clean_100["brand"] = spu_dim_df_clean_100["brand"].replace("无", "")

spu_dim_df_clean_100["text_features"] = spu_dim_df_clean_100.apply(
    lambda row: f'{row["spu_name"]},{row["category_path"]},{row["spu_properties"]}',
    # 增加商品名称权重
    # 增加四级级类目
    axis=1,
)

spu_dim_df_clean_100["name_features"] = spu_dim_df_clean_100.apply(
    lambda row: f'{row["spu_name"]},{row["brand"]}', axis=1
)

# 2. 批量获取嵌入向量, 每50个text一批次
# 中文：定义批次大小
batch_size = 50
embeddings = []
name_embeddings = []

# 中文：对文本特征进行分批处理
for i in range(0, len(spu_dim_df_clean_100["text_features"]), batch_size):
    # 中文：获取当前批次的文本
    batch_texts = spu_dim_df_clean_100["text_features"].tolist()[i : i + batch_size]
    # 中文：获取当前批次的嵌入向量
    batch_embeddings = get_embedding(batch_texts)
    # 中文：将当前批次的嵌入向量添加到总列表中
    embeddings.extend(batch_embeddings)

# 中文：对名称特征进行分批处理
for i in range(0, len(spu_dim_df_clean_100["name_features"]), batch_size):
    # 中文：获取当前批次的文本
    batch_texts = spu_dim_df_clean_100["name_features"].tolist()[i : i + batch_size]
    # 中文：获取当前批次的嵌入向量
    batch_embeddings = get_embedding(batch_texts)
    # 中文：将当前批次的嵌入向量添加到总列表中
    name_embeddings.extend(batch_embeddings)

embeddings_array = np.array(embeddings, dtype=np.float32)  # 确保数据类型为float32
name_embeddings_array = np.array(
    name_embeddings, dtype=np.float32
)  # 确保数据类型为float32

# 3. 构建FAISS索引
# 使用IVFFlat索引提高搜索效率
# 设置合适的聚类中心数量,一般建议为数据量的平方根
# nlist = int(np.sqrt(len(embeddings_array)))
nlist = spu_dim_df_clean_100["spu_id"].unique().shape[0]
quantizer = faiss.IndexFlatL2(embeddings_array.shape[1])
index = faiss.IndexIVFFlat(
    quantizer, embeddings_array.shape[1], nlist, faiss.METRIC_INNER_PRODUCT
)  # 改用内积相似度

name_quantizer = faiss.IndexFlatL2(name_embeddings_array.shape[1])
name_index = faiss.IndexIVFFlat(
    name_quantizer, name_embeddings_array.shape[1], nlist, faiss.METRIC_INNER_PRODUCT
)  # 改用内积相似度

# 归一化向量
embeddings_array = embeddings_array.astype(np.float32)  # 再次确保类型
faiss.normalize_L2(embeddings_array)

name_embeddings_array = name_embeddings_array.astype(np.float32)  # 再次确保类型
faiss.normalize_L2(name_embeddings_array)

index.train(embeddings_array)
index.add(embeddings_array)

name_index.train(name_embeddings_array)
name_index.add(name_embeddings_array)


def search_similar_skus(query: str, k: int = 5, nprobe: int = 2):
    """搜索相似SKU
    Args:
        query: 查询文本
        k: 返回结果数量
        nprobe: 搜索聚类中心数量
    Returns:
        distances: 距离分数
        indices: 相似商品索引
    """
    query_embedding = np.array(get_embedding(query)[0], dtype=np.float32).reshape(
        1, -1
    )
    # 归一化查询向量
    faiss.normalize_L2(query_embedding)

    # 设置搜索聚类中心数量
    index.nprobe = nprobe
    name_index.nprobe = nprobe

    # 分别搜索两个索引
    D1, I1 = index.search(query_embedding, k)
    D2, I2 = name_index.search(query_embedding, k)

    # 合并结果并计算总分
    results = []
    for i in range(len(I1[0])):
        results.append((I1[0][i], D1[0][i], D2[0][i]))
    for i in range(len(I2[0])):
        if I2[0][i] not in [x[0] for x in results]:
            results.append((I2[0][i], D1[0][i], D2[0][i]))

    # 按总分排序
    results.sort(key=lambda x: x[1] + x[2], reverse=True)

    # 取前k个结果
    results = results[:k]

    # 转换为原始格式
    D = np.array([[x[1] + x[2] for x in results]])
    I = np.array([[x[0] for x in results]])

    return D, I


import logging


category_path_cnt = len(spu_dim_df_clean_100["category_path"].unique())


def search_items_with_query(
    query: str,
    page_size: int = 20,
    nprobe: int = category_path_cnt,
) -> list[dict]:
    """使用向量搜索查找相似商品

    根据输入的查询文本,在商品库中搜索相似的商品。支持按城市筛选价格。

    Args:
        query (str): 查询文本
        page_size (int, optional): 返回结果数量。默认为 20
        nprobe (int, optional): FAISS索引搜索聚类中心数量。默认为商品总数的一半
    """
    D, I = search_similar_skus(
        query,
        k=min(max(page_size * 4, 100), int(len(spu_dim_df_clean_100) / 40)),
        nprobe=nprobe,
    )

    results = []

    # Format results
    for idx, (score, index) in enumerate(zip(D[0], I[0])):
        result_row = spu_dim_df_clean_100.iloc[index].to_dict()
        result_row["score"] = round(score, 4)
        logging.info(f"{query}: {result_row}")
        results.append(
            {
                "spu_name": result_row["spu_name"],
                "category": f"{ result_row['category_path']}",
                "brand": result_row["brand"],
                "spu_id": result_row["spu_id"],
                "spu_properties": result_row["spu_properties"],
                "sku_id_list": result_row["sku_id_list"],
                "price_range": f"{result_row['price_range']}",
            }
        )
    return results


print(search_items_with_query("黄油"))