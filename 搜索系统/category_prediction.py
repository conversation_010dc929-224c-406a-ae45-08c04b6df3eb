from datetime import datetime, timedelta
import logging
import pandas as pd

# category_prediction_df 获取
from odps_client import get_odps_sql_result_as_df


sku_category_sql = """
select sku_id,concat(category2,'>',category3,'>',category4) as category_name
from summerfarm_tech.dim_sku_df
where ds=max_pt('summerfarm_tech.dim_sku_df')
"""
sku_category_df = get_odps_sql_result_as_df(sku_category_sql)

print(f"打印sku_category_df.head(5)")
sku_category_df.head(5)


def get_sku_category(sku_id: str) -> str:
    _df = sku_category_df[sku_category_df["sku_id"] == sku_id]
    if _df.empty:
        return "没有找到类目"
    else:
        return _df.iloc[0]["category_name"]


last_n_days = 14
ds_yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y%m%d")
last_n_days_ago = (datetime.now() - timedelta(days=last_n_days)).strftime("%Y%m%d")

top_query = f"""
SELECT  query
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN cust_id END) AS searched_users
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN CONCAT(time,cust_id) END) AS search_cnt
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN CONCAT(time,cust_id) END) AS click_cnt
        ,ROUND(AVG(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS avg_click_index
        ,ROUND(MAX(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS max_click_index
        ,ROUND(MIN(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS min_click_index
        ,ROUND(STDDEV(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS std_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index
        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN CONCAT(time,query,idx,cust_id) END)*1.00/COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,query,idx,cust_id) END) as sku_ctr
        ,COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN cust_id END)*1.00/COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN cust_id END) as user_ctr
        ,CASE
            WHEN COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,cust_id) END) > 470 THEN '高频搜索词'
            WHEN COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,cust_id) END) <= 20 THEN '低频搜索词'
            ELSE '中频搜索词'
         END AS 搜索频次标签
        ,CASE
            WHEN COUNT(DISTINCT CASE WHEN envent_type = 'click' THEN CONCAT(time,cust_id) END) * 1.0 / COUNT(DISTINCT CASE WHEN envent_type = 'impression' THEN CONCAT(time,cust_id) END) > 0.25 THEN '高点击率词'
            ELSE '低点击率词'
         END AS 点击率标签
FROM    summerfarm_tech.app_log_search_detail_di
WHERE   ds BETWEEN '{last_n_days_ago}' and '{ds_yesterday}'
GROUP BY query
ORDER BY searched_users DESC
;
"""

top_query_df = get_odps_sql_result_as_df(sql=top_query)

# 保留完整数据集，不限制数量
from datetime import datetime
print(f"{datetime.now()}: 原始查询词总数: {len(top_query_df)}")

top_query_df["ctr"] = round(
    top_query_df["click_cnt"] * 1.00 / top_query_df["search_cnt"], 4
)

TOP_QUERY_CNT=50

middle_20_df = top_query_df[
    (top_query_df["search_cnt"] <= 470) & (top_query_df["search_cnt"] > 20)
].head(TOP_QUERY_CNT)
middle_20_query = [item.to_dict() for _, item in middle_20_df.iterrows()]

# **重要**: 把所有查询词都赋给top_20_query，不限制50个
# 为了向后兼容性保留变量名
# 这也是evaluate_search_queries.py使用的实际数据源
top_20_query_df = top_query_df.head(TOP_QUERY_CNT)
top_20_query = [item.to_dict() for _, item in top_20_query_df.iterrows()]

logging.info(
    f"""有点击的query的ctr分布:\n
    {top_query_df[top_query_df["click_cnt"] > 5]["ctr"].quantile(
        [0.10, 0.25, 0.5, 0.75, 0.90, 0.95])}
    """
),

# 低点击率的top search term
top_high_click_index_20_query_df = top_query_df[
    top_query_df["searched_users"] > 10
].sort_values(by=["search_cnt"], ascending=False)

top_high_click_index_20_query_df = top_high_click_index_20_query_df[
    (top_high_click_index_20_query_df["min_click_index"] > 6)
    | (top_high_click_index_20_query_df["ctr"] <= 0.25)
].head(TOP_QUERY_CNT)

top_high_click_index_20_query = [
    item.to_dict() for _, item in top_high_click_index_20_query_df.iterrows()
]


category_prediction_query = f"""
SELECT  query
        ,b.category4
        ,b.category4_id
        ,b.category3
        ,b.category3_id
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN cust_id END) AS searched_users
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'impression' THEN CONCAT(time,cust_id) END) AS search_cnt
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN CONCAT(time,cust_id) END) AS click_cnt
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN cust_id END) AS click_users
        ,ROUND(AVG(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS avg_click_index
        ,ROUND(MAX(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS max_click_index
        ,ROUND(MIN(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS min_click_index
        ,ROUND(STDDEV(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END),1) AS std_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.25) AS p25_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.5) AS p50_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.75) AS p75_click_index
        ,PERCENTILE(CASE    WHEN envent_type = 'click' THEN CAST(idx AS BIGINT) END,0.9) AS p90_click_index
        ,COUNT(DISTINCT a.ds) AS days_have_impression
        ,COUNT(DISTINCT CASE    WHEN envent_type = 'click' THEN a.ds END) AS days_have_click
FROM    summerfarm_tech.app_log_search_detail_di a
LEFT JOIN summerfarm_tech.dim_sku_df b
ON      b.ds = MAX_PT('summerfarm_tech.dim_sku_df')
AND     a.sku_id = b.sku_id
WHERE   a.ds BETWEEN '{last_n_days_ago}' and '{ds_yesterday}'
GROUP BY query
         ,b.category4
         ,b.category4_id
         ,b.category3
         ,b.category3_id
HAVING  click_cnt > 0 and click_users>2
ORDER BY searched_users DESC
;"""

category_prediction_df = get_odps_sql_result_as_df(category_prediction_query)

# category_prediction_valid_df 过滤
category_prediction_valid_df = category_prediction_df[
    (category_prediction_df["min_click_index"] <= 6.0)
    & (category_prediction_df["click_cnt"] >= 7)
]

# category_prediction_grouped 分组统计
category_prediction_grouped = (
    category_prediction_valid_df.groupby(
        ["query", "category4", "category4_id", "category3"]
    )
    .agg({"click_cnt": "sum"})
    .reset_index()
)

# Calculate percentile rank within each query group
category_prediction_grouped["category_rank"] = category_prediction_grouped.groupby(
    "query"
)["click_cnt"].transform(lambda x: x.rank(pct=True))

# Calculate click count ratio within each query group
category_prediction_grouped["category_percentile"] = (
    category_prediction_grouped.groupby("query")["click_cnt"].transform(
        lambda x: x / x.sum()
    )
)

category_prediction_grouped.sort_values(by=["click_cnt"], ascending=False, inplace=True)
category_prediction_grouped["category_rank"] = category_prediction_grouped[
    "category_rank"
].astype(float)

print(
    category_prediction_grouped[
        category_prediction_grouped["query"].isin(["芒果", "奶油", "苹果", "梨"])
    ]
    .head(20)
    .to_json(force_ascii=False)
)

# category_prediction_map 字典构建
category_prediction_map = {}
query_category_rank = {}
for _, row in category_prediction_grouped.iterrows():
    query = row["query"]
    category4 = row["category4"]
    category4_id = row["category4_id"]
    category_rank = row["category_rank"]
    category_percentile = row["category_percentile"]
    if query not in query_category_rank:
        query_category_rank[query] = {}
    if category_percentile >= 0.5:
        category_prediction_map[query] = {
            "category": category4,
            "category3": row["category3"],
            "category_id": row["category4_id"],
        }
    query_category_rank[query][f"{category4_id}"] = category_rank


def get_query_category_prediction(query: str) -> dict:
    return category_prediction_map.get(query, {})


def refine_items_with_category(item_list: list[dict], query: str) -> list[dict]:
    if len(item_list) <= 1:
        return item_list
    if query not in query_category_rank:
        logging.info(f"query not in query_category_rank, query={query}")
        return item_list
    category_rank_map = query_category_rank[query]
    if len(category_rank_map) <= 1:
        logging.info(f"query has only one category prediction result. query={query}")

    # Calculate category weighted score for each item
    for index, item in enumerate(item_list):
        # Assign score based on index, with higher index getting lower score
        item["score"] = 2000 + len(item_list) - index

        category_id = f'{item.get("category_id", "0")}'
        if category_id in category_rank_map:
            logging.info(
                f"类目预测干预的结果:{category_id}, query={query}, score={item['score']}, sku={item['sku_id']}"
            )
            item["category_weighted_score"] = (
                item["score"] * category_rank_map[f"{category_id}"]
            )
        else:
            item["category_weighted_score"] = item["score"] * 0.8

    # Sort by category_weighted_score and score
    sorted_items = sorted(
        item_list, key=lambda x: (-x["category_weighted_score"], -x["score"])
    )

    return sorted_items


import random


random_query_list = []

for _, row in top_query_df[top_query_df["searched_users"] > 10].iterrows():
    random_query_list.append(
        {"query": row["query"], "ctr": row["ctr"], "search_cnt": row["search_cnt"]}
    )


def get_random_query(count: int = 1) -> list[dict]:
    """
    Get random query(ies) from the random_query_list

    Args:
        count (int, optional): Number of random queries to return. Defaults to 1.

    Returns:
        list[dict]: List of random query dictionaries
    """
    if count <= 0:
        return []

    # If count is 1, return a single dictionary
    if count == 1:
        return random.choice(random_query_list)

    # If count is greater than list length, return shuffled list
    if count >= len(random_query_list):
        return random.sample(random_query_list, len(random_query_list))

    # Return specified number of unique random queries
    return random.sample(random_query_list, count)


def get_query_metrics(query: str) -> dict:
    """
    根据查询词获取相关的关键指标数据
    
    Args:
        query (str): 搜索关键词
        
    Returns:
        dict: 包含sku_ctr, user_ctr, searched_users, search_cnt, 搜索频次标签等数据
    """
    try:
        # 在top_query_df中查找对应query的数据
        query_data = top_query_df[top_query_df["query"] == query]
        
        if query_data.empty:
            return {
                "sku_ctr": "-",
                "user_ctr": "-", 
                "searched_users": "-",
                "search_cnt": "-",
                "search_frequency_label": "暂无数据"
            }
        
        # 取第一条匹配数据（可能有多个类目下的相同query）
        row = query_data.iloc[0]
        
        return {
            "sku_ctr": round(row.get("sku_ctr", 0) * 100, 2) if pd.notna(row.get("sku_ctr")) else "-",
            "user_ctr": round(row.get("user_ctr", 0) * 100, 2) if pd.notna(row.get("user_ctr")) else "-",
            "searched_users": int(row.get("searched_users", 0)) if pd.notna(row.get("searched_users")) else "-",
            "search_cnt": int(row.get("search_cnt", 0)) if pd.notna(row.get("search_cnt")) else "-",
            "search_frequency_label": row.get("搜索频次标签", "未知") if pd.notna(row.get("搜索频次标签")) else "暂无数据"
        }
    except Exception as e:
        logging.error(f"获取查询指标失败，query={query}, error={str(e)}")
        return {
            "sku_ctr": "-",
            "user_ctr": "-",
            "searched_users": "-",
            "search_cnt": "-",
            "search_frequency_label": "数据异常"
        }
