#!/usr/bin/env python3
"""
测试ODPS连接的调试脚本
"""

import sys
import os

# 添加父目录路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    print("🔍 测试ODPS模块导入...")
    
    # 尝试导入odps客户端
    from odps_client import get_odps_sql_result_as_df
    print("✅ ODPS客户端导入成功")
    
    # 测试一个简单的查询
    print("🔍 测试一个简单的查询...")
    test_sql = "SELECT '测试' as query, 100 as searched_users, 0.5 as user_ctr"
    result_df = get_odps_sql_result_as_df(test_sql)
    print(f"✅ 查询执行成功，结果类型: {type(result_df)}")
    print(f"📊 结果预览: {len(result_df)} 行")
    
    # 打印前几行
    if len(result_df) > 0:
        print(result_df.head())
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请检查: 1) odps_client.py 是否存在 2) 环境变量是否正确")
    
    # 列出当前目录下的odps相关文件
    import glob
    print("\n📁 发现的相关文件:")
    for f in glob.glob("**/odps*.py", recursive=True):
        print(f"  - {f}")
        
except Exception as e:
    print(f"❌ 执行错误: {e}")
    print("可能原因: 网络连接、权限、SQL语法或环境变量问题")