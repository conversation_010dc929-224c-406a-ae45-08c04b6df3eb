# AI搜索评测系统环境变量示例

# OpenAI API配置
XM_LLM_API_KEY=your_openai_api_key_here
XM_LLM_BASE_URL=https://api.openai.com/v1  # 或其它自定义端点
XM_LLM_MODEL=gpt-4o  # 或 gpt-4-turbo-preview, gpt-3.5-turbo

# 搜索API配置
SEARCH_API_BASE_URL=http://localhost:5800/api/search-content

# 日志配置
LOG_LEVEL=INFO
EVAL_QUERY_LIMIT=50  # 设置评测查询词数量限制

# ODPS配置（如果环境已有，可省略）
ODPS_ACCESS_ID=your_odps_access_key
ODPS_SECRET_KEY=your_odps_secret_key
ODPS_ENDPOINT=your_odps_endpoint
ODPS_PROJECT=your_odps_project