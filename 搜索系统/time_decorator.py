#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间装饰器模块
"""

import time
import functools


def log_function_time_spend(func):
    """
    装饰器：记录函数执行时间
    
    使用方法：
    @log_function_time_spend
    def your_function():
        pass
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        # 获取函数名和参数信息（用于日志）
        func_name = func.__name__
        
        # 提取一些关键参数用于日志显示
        log_params = []
        if args:
            # 如果第一个参数是query，显示它
            if len(args) > 0:
                log_params.append(f"query='{args[0]}'")
        
        # 从kwargs中提取一些关键参数
        if 'city' in kwargs:
            log_params.append(f"city='{kwargs['city']}'")
        if 'size' in kwargs:
            log_params.append(f"size={kwargs['size']}")
            
        param_str = ", ".join(log_params) if log_params else "无参数"
        
        print(f"⏱️  开始执行 {func_name}({param_str})")
        
        try:
            # 执行原函数
            result = func(*args, **kwargs)
            
            # 计算执行时间
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 格式化时间显示
            if execution_time < 1:
                time_str = f"{execution_time*1000:.1f}ms"
            else:
                time_str = f"{execution_time:.2f}s"
            
            print(f"✅ {func_name} 执行完成，耗时: {time_str}")
            
            return result
            
        except Exception as e:
            # 即使出错也要记录时间
            end_time = time.time()
            execution_time = end_time - start_time
            
            if execution_time < 1:
                time_str = f"{execution_time*1000:.1f}ms"
            else:
                time_str = f"{execution_time:.2f}s"
                
            print(f"❌ {func_name} 执行失败，耗时: {time_str}，错误: {e}")
            raise
    
    return wrapper


# 测试代码
if __name__ == "__main__":
    @log_function_time_spend
    def test_fast_function():
        """测试快速函数（毫秒级）"""
        time.sleep(0.1)  # 100ms
        return "快速函数执行完成"

    @log_function_time_spend
    def test_slow_function():
        """测试慢速函数（秒级）"""
        time.sleep(1.5)  # 1.5秒
        return "慢速函数执行完成"

    @log_function_time_spend
    def test_function_with_params(query, city="杭州", size=10):
        """测试带参数的函数"""
        time.sleep(0.2)
        return f"查询: {query}, 城市: {city}, 大小: {size}"

    @log_function_time_spend
    def test_function_with_error():
        """测试出错的函数"""
        time.sleep(0.3)
        raise ValueError("这是一个测试错误")

    print("=== 测试时间装饰器功能 ===\n")
    
    # 测试1: 快速函数
    print("1. 测试快速函数:")
    result1 = test_fast_function()
    print(f"返回结果: {result1}\n")
    
    # 测试2: 慢速函数
    print("2. 测试慢速函数:")
    result2 = test_slow_function()
    print(f"返回结果: {result2}\n")
    
    # 测试3: 带参数的函数
    print("3. 测试带参数的函数:")
    result3 = test_function_with_params("芒果", city="北京", size=20)
    print(f"返回结果: {result3}\n")
    
    # 测试4: 出错的函数
    print("4. 测试出错的函数:")
    try:
        test_function_with_error()
    except ValueError as e:
        print(f"捕获到预期错误: {e}\n")
    
    print("=== 装饰器测试完成 ===")
