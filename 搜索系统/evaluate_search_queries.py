#!/usr/bin/env python3
"""
AI搜索查询词评测系统
用于获取top查询词并调用AI接口进行搜索质量评测
"""

import os
import json
import asyncio
import aiohttp
import logging
from datetime import datetime
from typing import List, Dict, Any
from dotenv import load_dotenv
import sys

# 添加父目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 加载环境变量
load_dotenv()

# OpenAI SDK集成
try:
    from openai import AsyncOpenAI
except ImportError:
    print("请安装openai包: pip install openai")
    exit(1)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# OpenAI客户端配置
client = AsyncOpenAI(
    api_key=os.getenv("XM_LLM_API_KEY"),
    base_url=os.getenv("XM_LLM_BASE_URL") or "https://api.openai.com/v1",
)

CX_API_BASE_URL = f"http://xianmuai.s7.tunnelfrp.com/search-arena/api/search-content"

# 系统提示词
SEARCH_EVALUATION_PROMPT = """
您是一个专业的电商搜索质量评估专家。

请基于以下搜索结果，评估三个不同版本的表现，并给出详细的评测报告。

## 搜索质量评估标准

### 1. 相关性评估
- **精准度**: 搜索结果与用户查询意图的匹配程度
- **覆盖率**: 是否展现了不同类型但相关的商品
- **语义理解**: 是否理解了用户的真实需求（如同义词、俚语等）

### 2. 商品质量评估
- **热销程度**: 是否优先展示高销量、高人气的商品
- **评分质量**: 商品的评分、评论数量和质量
- **价格合理性**: 价格分布是否合理，覆盖不同消费层次

### 3. 用户体验评估
- **结果多样性**: 品牌、价格、规格等维度的多样性
- **信息丰富度**: 商品信息的完整性（标题清晰、价格完整、库存充足）
- **排序逻辑**: 商品的排序是否符合用户预期

### 4. 商业价值评估
- **转化率**: 当前展示结果的历史转化数据（如CTR、GMV）
- **利润贡献**: 是否平衡了用户体验和商业收益

## 需要评测的版本
- original_es: 原始Elasticsearch搜索结果
- custom_score_search: 自定义打分搜索结果
- custom_score_gmv_sorted: 基于GMV排序的自定义打分结果

## 输出格式
请为每个版本单独评分，并给出最终推荐

### 查询词: {query}
#### 查询统计信息:
- 搜索用户数: {searched_users}
- 用户CTR: {user_ctr}%
- 搜索频次: {search_frequency}

### 版本评测分析:

#### 1. original_es 评分: X/100
**优点**: [列举主要优点]
**缺点**: [列举主要缺点]
**关键问题**: [指出最大问题]
**适用场景**: [适合的使用场景]

#### 2. custom_score_search 评分: X/100
**优点**: [列举主要优点]
**缺点**: [列举主要缺点]
**关键问题**: [指出最大问题]
**适用场景**: [适合的使用场景]

#### 3. custom_score_gmv_sorted 评分: X/100
**优点**: [列举主要优点]  
**缺点**: [列举主要缺点]
**关键问题**: [指出最大问题]
**适用场景**: [适合的使用场景]

### 综合对比与推荐

#### 最佳版本: [版本名称]
**推荐理由**: [详细说明为何该版本在给定查询词上表现最佳]

---

搜索结果数据如下:
{search_data}
"""

class SearchEvaluator:
    def __init__(self, model_name: str = None):
        self.results_cache = {}
        self.evaluation_results = []
        self.model_name = self._get_model_name(model_name)
        
    def _get_model_name(self, override_model: str = None) -> str:
        """根据优先级获取模型名称"""
        # 优先级: --model > 环境变量 > 默认值
        if override_model:
            logger.info(f"使用指定模型: {override_model}")
            return override_model
        elif os.getenv("XM_LLM_MODEL"):
            model = os.getenv("XM_LLM_MODEL")
            logger.info(f"使用环境变量模型: {model}")
            return model
        else:
            default_model = "deepseek-v3-250324"
            logger.info(f"使用默认模型: {default_model}")
            return default_model

    async def get_top_queries_from_existing(self, user_ctr_threshold: float = 0.7) -> List[Dict[str, Any]]:
        """从现有category_prediction.py中获取查询词"""
        try:
            from category_prediction import top_query_df
            
            # 使用自定义user_ctr阈值筛选查询词
            filtered_df = top_query_df.sort_values("searched_users", ascending=False).head(400)[
                (top_query_df["searched_users"] > 0) & 
                (top_query_df["user_ctr"] < user_ctr_threshold)
            ].copy()
            
            # 重命名列
            filtered_df["search_frequency"] = filtered_df["搜索频次标签"]
            filtered_df["user_ctr"] = filtered_df["user_ctr"]  # 已经是正确格式
            
            # 按searched_users排序
            top_queries_df = filtered_df.sort_values("searched_users", ascending=False)
            
            # 确保至少有查询词数据
            queries = []
            for _, row in top_queries_df.iterrows():
                try:
                    query_dic=row.to_dict()
                    query_dic["search_frequency"]=str(row.get("search_frequency", "未知"))
                    
                    queries.append(query_dic)
                except (ValueError, TypeError) as e:
                    logger.warning(f"跳过无效数据: {row.get('query', 'unknown')} - {e}")
                    continue
            
            logger.info(f"从category_prediction模块获取到 {len(queries)} 个查询词")
            
            if not queries:
                raise ValueError("没有获取到有效的查询词数据")
                
            # 返回所有符合条件的查询词
            return queries
            
        except Exception as e:
            logger.error(f"从category_prediction获取查询词失败: {e}")
            logger.info("使用备用测试数据")
            
            # 使用实际的常用查询词作为备份
            return [
                {"query": "芒果", "searched_users": 1200, "user_ctr": 0.45, "search_frequency": "高频搜索词"},
                {"query": "奶油", "searched_users": 980, "user_ctr": 0.52, "search_frequency": "高频搜索词"},
                {"query": "苹果", "searched_users": 850, "user_ctr": 0.38, "search_frequency": "高频搜索词"},
                {"query": "香蕉", "searched_users": 720, "user_ctr": 0.41, "search_frequency": "高频搜索词"},
                {"query": "草莓", "searched_users": 650, "user_ctr": 0.33, "search_frequency": "高频搜索词"},
                {"query": "菠萝", "searched_users": 580, "user_ctr": 0.46, "search_frequency": "高频搜索词"},
                {"query": "西瓜", "searched_users": 420, "user_ctr": 0.39, "search_frequency": "高频搜索词"},
                {"query": "葡萄", "searched_users": 380, "user_ctr": 0.43, "search_frequency": "中频搜索词"},
                {"query": "火龙果", "searched_users": 320, "user_ctr": 0.36, "search_frequency": "中频搜索词"},
                {"query": "橙子", "searched_users": 290, "user_ctr": 0.47, "search_frequency": "中频搜索词"},
            ]
    
    async def fetch_search_results_async(self, session: aiohttp.ClientSession, query: str, city: str = "杭州", page_size: int = 20) -> Dict[str, Any]:
        """异步调用搜索接口"""
        params = {
            "query": query,
            "city": city,
            "page_size": page_size
        }
        
        try:
            async with session.get(CX_API_BASE_URL, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return data
                else:
                    logger.error(f"搜索接口调用失败 {query}: HTTP {response.status}")
                    return {}
        except Exception as e:
            logger.error(f"搜索接口调用异常 {query}: {e}")
            return {}
    
    async def evaluate_query_with_ai(self, query_info: Dict[str, Any], search_result: Dict[str, Any]) -> Dict[str, Any]:
        """使用AI评测单个查询词"""
        try:
            # 构造评测请求
            messages = [
                {
                    "role": "system",
                    "content": SEARCH_EVALUATION_PROMPT.format(
                        query=query_info["query"],
                        searched_users=query_info["searched_users"],
                        user_ctr=f"{query_info['user_ctr']*100:.2f}" if query_info["user_ctr"] < 1 else str(query_info["user_ctr"]),
                        search_frequency=query_info["search_frequency"],
                        search_data=json.dumps(search_result, ensure_ascii=False, indent=2)
                    )
                }
            ]
            
            completion = await client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                max_tokens=4000,
                temperature=0.3
            )
            
            evaluation = completion.choices[0].message.content
            
            logger.info(f"AI评测完成: {query_info['query']}")
            return {
                "query": query_info["query"],
                "query_stats": query_info,
                "search_result": search_result,
                "evaluation": evaluation,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"AI评测失败 {query_info['query']}: {e}")
            return {
                "query": query_info["query"],
                "query_stats": query_info,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def process_single_query(self, session: aiohttp.ClientSession, semaphore: asyncio.Semaphore, query_info: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个查询词"""
        async with semaphore:
            # 获取搜索结果
            search_result = await self.fetch_search_results_async(session, query_info["query"])
            
            if not search_result:
                return {"query": query_info["query"], "error": "无法获取搜索结果"}
            
            # 等待一下避免API限流
            await asyncio.sleep(1)
            
            # AI评测
            evaluation = await self.evaluate_query_with_ai(query_info, search_result)
            
            return evaluation
    
    async def run_evaluation(self, user_ctr_threshold: float = 0.7) -> List[Dict[str, Any]]:
        """运行完整的评测流程"""
        # 获取查询词列表
        queries = await self.get_top_queries_from_existing(user_ctr_threshold)
        
        if not queries:
            logger.error("获取查询词失败，评测终止")
            return []
        
        logger.info(f"实际评测查询数: {len(queries)}")
        
        # 创建会话限制
        semaphore = asyncio.Semaphore(5)  # 并发限制
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=30)) as session:
            tasks = []
            
            # 分批处理查询词，不再限制数量
            batch_size = 10
            for i in range(0, len(queries), batch_size):
                batch = queries[i:i+batch_size]
                
                for query_info in batch:
                    task = self.process_single_query(session, semaphore, query_info)
                    tasks.append(task)
                
                # 每批完成后等待更长时间
                await asyncio.sleep(2)
            
            # 执行所有任务
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            processed_results = []
            for result in results:
                if isinstance(result, Exception):
                    logger.error(f"任务异常: {result}")
                else:
                    processed_results.append(result)
            
            self.evaluation_results = processed_results
            return processed_results
    
    async def generate_ai_summary(self, evaluation_data: List[Dict[str, Any]]) -> str:
        """使用AI生成综合总结分析（分批次处理以避免token限制）"""
        if not evaluation_data:
            return "## ⚠️ 暂无有效的评测数据\n\n当前没有足够数据进行AI总结分析。"
        
        batch_size = 50  # 每批处理50个查询
        total_batches = (len(evaluation_data) + batch_size - 1) // batch_size
        
        logger.info(f"将AI总结分{batch_size}个一批，共{total_batches}批进行处理")
        
        all_batch_summaries = []
        
        for batch_idx in range(0, len(evaluation_data), batch_size):
            batch = evaluation_data[batch_idx:batch_idx + batch_size]
            batch_number = (batch_idx // batch_size) + 1
            
            try:
                batch_summary = await self._generate_batch_summary(batch, batch_number, total_batches)
                all_batch_summaries.append(batch_summary)
                
                # 批次间等待，避免API限流
                if batch_number < total_batches:
                    await asyncio.sleep(1)
                    
            except Exception as e:
                logger.error(f"第{batch_number}批AI总结生成失败: {e}")
                continue
        
        # 合并所有批次的总结
        if len(all_batch_summaries) == 1:
            return all_batch_summaries[0]
        elif all_batch_summaries:
            # 生成统一的分析报告
            return await self._generate_final_consolidated_summary(all_batch_summaries, evaluation_data)
        else:
            return "## ⚠️ AI总结生成失败\n\n所有批次处理均失败。"
    
    async def _generate_batch_summary(self, batch_data: List[Dict[str, Any]], batch_number: int, total_batches: int) -> str:
        """为单个批次生成AI总结"""
        
        batch_summary_prompt = f"""
您是一个资深的数据分析师和搜索算法专家。请基于以下查询词的评测结果，生成本批次({batch_number}/{total_batches})的深度分析。

批次数据包含{len(batch_data)}个查询词的关键统计信息。

## 报告要求
请按照以下结构生成总结：

### 批次{batch_number}/{total_batches}分析

### 本批次综合表现排行榜
基于本批次数据，对三个搜索版本进行客观排序。

### 关键洞察发现
- 哪些搜索策略在本批次中最有效？
- 高搜索量查询词的表现如何？

### 用户行为深度分析
- 本批次中高CTR查询词的共同特征
- 低CTR查询词的问题根源

### 数据说明
{{x}}

请用专业、数据驱动的方式撰写这份总结。
"""
        
        # 构造批次数据
        batch_summary_data = []
        for data in batch_data:
            batch_summary_data.append(f"""
查询词: {data['query']}
搜索用户数: {data['query_stats']['searched_users']:,}
用户CTR: {data['query_stats']['user_ctr']:.2%}
AI评测要点: {data['evaluation_text'][:300]}...  # 缩短每个评测要点以避免过长
""")
        
        data_text = f"\n---\n".join(batch_summary_data)
        
        completion = await client.chat.completions.create(
            model=self.model_name,
            messages=[{
                "role": "system",
                "content": batch_summary_prompt.format(x=data_text)
            }],
            max_tokens=2500,  # 减少每批次的token使用
            temperature=0.2
        )
        
        return completion.choices[0].message.content
    
    async def _generate_final_consolidated_summary(self, batch_summaries: List[str], full_data: List[Dict[str, Any]]) -> str:
        """生成最终的统一汇总报告"""
        
        consolidated_prompt = f"""
您是一个资深的数据分析师和搜索算法专家。请基于以下{len(batch_summaries)}个批次的分析报告，生成一份最终的、统一的数据驱动总结报告。

## 最终汇总要求

### 综合表现排行榜
基于全部{len(full_data)}个查询词的数据，对三个搜索版本进行最终排序。

### 核心洞察发现
从全局视角总结最关键的发现，超越各个批次的局限。

### 商业价值和用户行为洞察
- 高影响力查询词的表现特征
- 跨批次一致性或差异性分析
- 实用的优化建议

### 以下是各个批次的分析摘要:
{{batch_reports}}

请生成本统一、深度的最终分析报告。
"""
        
        # 构造批次报告摘要
        batch_reports_text = "\n---\n".join([f"批次{i+1}总结:\n{batch_summary}" for i, batch_summary in enumerate(batch_summaries)])
        
        try:
            completion = await client.chat.completions.create(
                model=self.model_name,
                messages=[{
                    "role": "system",
                    "content": consolidated_prompt.format(batch_reports=batch_reports_text)
                }],
                max_tokens=3000,
                temperature=0.2
            )
            
            return completion.choices[0].message.content
            
        except Exception as e:
            logger.error(f"最终汇总报告生成失败: {e}")
            # 回退：简单组合各个批次的总结
            return f"## 📊 AI智能总结分析（批次汇总版）\n\n" + "\n---\n".join(batch_summaries)

    async def save_to_markdown(self, output_path: str = "search_evaluation_report.md", user_ctr_threshold: float = 0.7):
        """保存评测结果到markdown文件"""
        if not self.evaluation_results:
            logger.warning("没有评测结果可保存")
            return
        
        markdown_content = f"""# 搜索质量AI评测报告

生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 评测概览
- 总评测查询数: {len(self.evaluation_results)}
- 评测模型: {self.model_name}
- 评测标准: 相关性、商品质量、用户体验、商业价值四维评分
- 用户CTR阈值: {user_ctr_threshold:.3f}

## 评测结果详细分析

"""
        
        # 统计数据（预留）
        
        for idx, result in enumerate(self.evaluation_results, 1):
            if "error" in result:
                markdown_content += f"""
### ❌ {idx}. {result['query']} - 评测失败
**错误信息**: {result['error']}

---

"""
            else:
                markdown_content += f"""
### ✅ {idx}. [{result['query']}](http://xianmuai.s7.tunnelfrp.com/search-arena/?query={result['query']}&page_size=40&city=杭州)

#### 查询词统计
- 过去14天搜索用户数: {result['query_stats']['searched_users']:,}
- 用户CTR: {result['query_stats']['user_ctr']:.2%}
- 商品(SKU)CTR: {result['query_stats']['sku_ctr']:.2%}
- 搜索频次归类: {result['query_stats']['search_frequency']}

#### AI评测分析
{result['evaluation']}

---

"""
                
# 收集所有评测数据用于AI总结分析
        evaluation_data_for_summary = []
        for result in self.evaluation_results:
            if "error" not in result:
                evaluation_data_for_summary.append({
                    "query": result["query"],
                    "query_stats": result["query_stats"],
                    "evaluation_text": result["evaluation"]
                })
        
        # 使用AI生成总结分析
        ai_summary = await self.generate_ai_summary(evaluation_data_for_summary)
        markdown_content += f"""

## 📊 AI智能总结分析

{ai_summary}

"""
        
        # 添加查询统计附录
        if self.evaluation_results:
            markdown_content += "## 📈 查询详情统计表\n\n"
            markdown_content += "| 序号 | 查询词 | 用户量 | CTR | 频次标签 | 状态 |\n"
            markdown_content += "|---|---|---|---|---|---|\n"
            
            for idx, result in enumerate(self.evaluation_results, 1):
                status = "✅成功" if "error" not in result else "❌失败"
                query_stats = result.get('query_stats', {})
                markdown_content += f"| {idx} | {result['query']} | {query_stats.get('searched_users', '未知'):,} | {query_stats.get('user_ctr', 0):.1%} | {query_stats.get('search_frequency', '未知')} | {status} |\n"
        
        # 写入文件
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            logger.info(f"评测报告已保存: {output_path}")
        except Exception as e:
            logger.error(f"保存markdown文件失败: {e}")

async def main(user_ctr_threshold: float = 0.7, model_name: str = None):
    """主函数，带CTR阈值和模型参数"""
    evaluator = SearchEvaluator(model_name)
    
    # 获取环境变量检查
    if not os.getenv("XM_LLM_API_KEY"):
        logger.error("XM_LLM_API_KEY环境变量未设置")
        return
    
    # 确认配置和参数
    logger.info(f"使用模型: {evaluator.model_name}")
    logger.info(f"API端点: {os.getenv('XM_LLM_BASE_URL', 'https://api.openai.com/v1')}")
    logger.info(f"筛选用户CTR阈值: {user_ctr_threshold:.3f}")
    
    try:
        # 运行评测，使用用户指定的CTR阈值
        results = await evaluator.run_evaluation(user_ctr_threshold=user_ctr_threshold)
        
        # 生成报告
        timestamp = datetime.now().strftime("%Y%m%d")
        output_file = f"search_evaluation_report_{timestamp}.md"
        await evaluator.save_to_markdown(output_file, user_ctr_threshold=user_ctr_threshold)
        
        logger.info(f"评测完成！共评测 {len(results)} 个查询词，报告文件: {output_file}")
        
    except KeyboardInterrupt:
        logger.info("评测任务被中断")
    except Exception as e:
        logger.error(f"评测任务失败: {e}")

if __name__ == "__main__":
    # 添加命令行参数解析
    import argparse
    
    parser = argparse.ArgumentParser(description="AI搜索质量评测系统")
    parser.add_argument(
        "--user-ctr",
        type=float,
        default=0.7,
        help="筛选用户CTR的阈值，默认: 0.7"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式"
    )
    parser.add_argument(
        "--model",
        type=str,
        default=None,
        help="指定使用的AI模型，优先级：--model > 环境变量 > 默认值(deepseek-v3-250324)"
    )
    
    args = parser.parse_args()
    
    if args.debug:
        logging.basicConfig(level=logging.DEBUG)
    
    logger.info(f"启动AI评测系统，CTR阈值: {args.user_ctr}, 使用模型: {args.model or '自动检测'}")
    asyncio.run(main(args.user_ctr, args.model))