{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from search_xianmu_directly_from_es import search_xianmu_product_directly_from_es\n", "import requests\n", "\n", "print(requests.get(\"https://ifconfig.io/ip\").text)\n", "\n", "query=\"巴乐\"\n", "\n", "products_list=search_xianmu_product_directly_from_es(query=query, city=\"杭州\", minimum_score=10.0, size=200)\n", "for index, product in enumerate(products_list):\n", "    title = product.get('spu_name', 'N/A')\n", "    score = product.get('_score', 'N/A')\n", "    brand = product.get('brand', 'N/A')\n", "    properties = product.get('properties', 'N/A')\n", "    property_values = product.get('property_values', 'N/A')\n", "    category = product.get('category', 'N/A')\n", "    title_pure = product.get('titlePure', 'N/A')\n", "    store_quantity = product.get('store_quantity', 'N/A')\n", "    print(f\"query:{query} Index: {index}, Title: {title}, Score: {score}, {brand}, {properties},{property_values},{category}, quantity:{store_quantity}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from search_xianmu_product import search_xianmu_product\n", "results=search_xianmu_product(query=query, city=\"杭州\", page_size=60)\n", "for index, product in enumerate(results):\n", "    title = product.get('spu_name', 'N/A')\n", "    score = product.get('sort_score', 'N/A')\n", "    brand = product.get('brand', 'N/A')\n", "    properties = product.get('properties', 'N/A')\n", "    property_values = product.get('property_values', 'N/A')\n", "    category = product.get('category', 'N/A')\n", "    title_pure = product.get('titlePure', 'N/A')\n", "    print(f\"Index: {index}, Title: {title}, Score: {score}, {brand}, {properties},{property_values},{category},{title_pure}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}