<!DOCTYPE html>
<html>
<head>
    <title>鲜沐商城"新搜索"投票系统</title>
    <link rel="icon" type="image/x-icon" href="//azure.summerfarm.net/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <style>
        body { font-family: Arial; }
        .search-box { margin-bottom: 20px; }
        .result-item { margin: 5px; padding: 10px; border: 1px solid #ddd; width: 200px; box-shadow: 0 0 5px rgba(0,0,0,0.1); }
        .item-container { display: flex; flex-wrap: wrap; }
        .result-item { display: flex; align-items: flex-start; flex-direction: column; position: relative; }
        .result-item img { margin-right: 20px; cursor: pointer; }
        .result-item p { margin: 2px 0; padding: 0; font-size: smaller; }
        .search-box .search-box-item { padding:5px 10px; }
        .query-input-item { width: 200px; }
        .query-container a {
            display: inline-block; margin: 2px; padding: 2px 5px; border: 1px solid skyblue; border-radius: 5px; font-size: smaller; background-color: aliceblue; text-decoration: none; color: cornflowerblue; }
        
        /* Hide hidding-properties by default */
        .hidding-properties {
            display: none; position: fixed; background-color: lightyellow; border: 1px solid #ddd; padding: 10px; border-radius: 4px; box-shadow: 0 5px 10px lightyellow;
            z-index: 100; left: 25%;
            top: 30%;
            width: 50vw;
        }        
        .side-result-prefer-btn {width: 40vw;display: block;font-size: xx-large;cursor: pointer;color: blueviolet;border: 1px solid mediumvioletred;border-radius: 10px;background-color: lightyellow;}
        .side-result-prefer-btn:hover {background-color: lightcoral;}
        .side-result-prefer-btn:disabled {background-color: lightgray;border-color: darkgray;color: gray;}
        .side-result-actions {position: absolute;bottom: 0;}
        .result-container .side-results {width: 50%;padding-bottom: 80px;position: relative;}
        .trailing-actions .side-result-prefer-btn, .comment-input {width: 90%;margin-top: 10px;}
        .comment-input {padding: 5px;border-radius: 10px;font-size: larger;}
        .trailing-actions {width:100%;}

        .properties-container {display:flex;flex-wrap: wrap;}
        .result-container {width:100%; display:flex;flex-flow: wrap;}
        .result-container .side-results{width:50%;}
        .properties-container .property-item {display: inline;margin-right: 15px;width: fit-content;border-bottom: 1px solid darkgray;font-size: small;line-height: 2em;}

        /* Modal styles */
        .modal {display: none;position: fixed;z-index: 1;left: 0;top: 0;width: 100%;height: 100%;background-color: rgba(0,0,0,0.9);}
        .modal-content {margin: auto;display: block;max-width: 80%;max-height: 80%;position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);}
        .close {position: absolute;right: 35px;top: 15px;color: #f1f1f1;font-size: 40px;font-weight: bold;cursor: pointer;}
    </style>
</head>
<body>
    <h1>鲜沐商城"新搜索"投票系统</h1>
    <div class="search-box">
        <form method="GET">
            <input class='search-box-item query-input-item' type="text" name="query" placeholder="输入搜索词..." value="{{ query }}">
            <label for="page_size">页码个数：</label>
            <select id='page_size' class='search-box-item' name="page_size">
                {% for size in [6, 12, 18, 24, 30, 50, 100] %}
                <option value="{{ size }}" {% if size == page_size %}selected{% endif %}>{{ size }}</option>
                {% endfor %}
            </select>
            {% if city_list %}
            <select id='citySelector' class='search-box-item' name="city">
                {% for city_name in city_list %}
                <option value="{{ city_name }}" {% if city_name == city %}selected{% endif %}>{{ city_name }}</option>
                {% endfor %}
            </select>
            {% endif %}
            <button class='search-box-item' type="submit">搜索</button>
            <a href="/?query={{random_next_query.query}}&page_size={{page_size}}&city={{city}}">随机搜索一个</a>
        </form>
    </div>
    <div class='result-container'>
        <div class='left-side-results side-results'>
            {% if left_results %}
            <h2>商品结果A: {{ query }}, {{ city }}</h2>
            <div class='item-container'>
            {% for item in left_results %}
                <div class="result-item">
                    <img class='item-img' src="{{ item.img_url }}" alt="{{ item.spu_name }}" width="100" height="100" object-fit="contain" onclick="showModal(this.src)">
                    <p><b>{{ item.spu_name }}</b></p>
                    <p>{{ item.name_features }}</p>
                    <p>SKU: {{ item.sku_id }}</p>
                    <p>{{ item.area_price }}</p>
                    <p>月销: {{ item.monthly_sales }}件, ¥{{ item.monthly_gmv }}</p>
                    <p>score: {{ item.sort_score }}</p>
                    <div class='hidding-properties'>
                        <div class='properties-container'>
                        {% for key, value in item.json_result.items() %}
                            <span class='property-item'><b>{{ key }}</b>: {{ value }}</span>
                        {% endfor %}
                        </div>
                    </div>
                </div>
            {% endfor %}
            </div>
            {% endif %}
            <div class='side-result-actions'>
                <button class='side-result-prefer-btn' data-version='{{ "new_embedding_search" if is_left_side else "old_es_search" }}'
                data-query="{{ query }}" data-city="{{ city }}" data-is-left-side="{{ is_left_side }}" data-side='左边'>左边更好</button>
            </div>
        </div>
        <div class='right-side-results side-results'>
            {% if right_results %}
            <h2>商品结果B: {{ query }}, {{ city }}</h2>
            <div class='item-container'>
            {% for item in right_results %}
                <div class="result-item">
                    <img class='item-img' src="{{ item.img_url }}" alt="{{ item.spu_name }}" width="100" height="100" object-fit="contain" onclick="showModal(this.src)">
                    <p><b>{{ item.spu_name }}</b></p>
                    <p>{{ item.name_features }}</p>
                    <p>SKU: {{ item.sku_id }}</p>
                    <p>{{ item.area_price }}</p>
                    <div class='hidding-properties'>
                        <div class='properties-container'>
                        {% for key, value in item.json_result.items() %}
                            <span class='property-item'><b>{{ key }}</b>: {{ value }}</span>
                        {% endfor %}
                        </div>
                    </div>
                </div>
            {% endfor %}
            </div>
            {% endif %}
            <div class='side-result-actions'>
                <button class='side-result-prefer-btn' data-version='{{ "old_es_search" if is_left_side else "new_embedding_search" }}' 
                data-query="{{ query }}" data-city="{{ city }}" data-is-left-side="{{ is_left_side }}" data-side='右边'>右边更好</button>
            </div>
        </div>
        <div class='trailing-actions'>
            <button class='side-result-prefer-btn' data-version='both-are-poor' 
                data-query="{{ query }}" data-city="{{ city }}" data-is-left-side="{{ is_left_side }}" data-side='都不好'>都不好</button>
            <button class='side-result-prefer-btn' data-version='they-are-tied' 
                data-query="{{ query }}" data-city="{{ city }}" data-is-left-side="{{ is_left_side }}" data-side='都差不多'>都差不多</button>
            <input type='text' class='comment-input' name="comment" placeholder='如果搜索结果都不满意，可以吐槽哦'>
        </div>
    </div>

    {% if top_high_click_index_20_query %}
        <h2>过去{{last_n_days}}天商城最热门的{{ top_high_click_index_20_query|length }}个低点击率搜索词</h2>
        <div class='query-container'>
        {% for item in top_high_click_index_20_query %}
        <a href="/?query={{ item.query }}&city={{ city }}&page_size={{page_size}}">{{ item.query }}, ctr:{{ item.ctr }}, 搜索次数:{{ item.search_cnt }}</a>
        {% endfor %}
    {% endif %}

    {% if top_20_query %}
        <h2>过去{{last_n_days}}天商城最热门的{{ top_20_query|length }}个搜索词</h2>
        <div class='query-container'>
        {% for item in top_20_query %}
        <a href="/?query={{ item.query }}&city={{ city }}&page_size={{page_size}}">{{ item.query }}, ctr:{{ item.ctr }}, 搜索次数:{{ item.search_cnt }}</a>
        {% endfor %}
    {% endif %}

    

    <!-- Modal -->
    <div id="imageModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImg">
    </div>

    <script>
        const random_next_query = {{ random_next_query|tojson }};
        // random_next_query something like this:
        //  {'query': '佳农凤梨', 'ctr': 0.3995, 'search_cnt': 418}

        document.querySelectorAll('.side-result-prefer-btn').forEach(button => {
          button.addEventListener('click', function(event) {
            const version = event.target.getAttribute('data-version');
            const comment = document.querySelector('.comment-input').value;
            const formData = new FormData();
            formData.append('version', version);
            formData.append('comment', comment);
            fetch('/which-is-better', {
              method: 'POST',
              body: formData
            })
            .then(response => response.json())
            .then(data => {
              // 禁用所有按钮
              document.querySelectorAll('.side-result-prefer-btn').forEach(btn => {
                btn.disabled = true;
              });
              // 只修改被点击按钮的文本
              const dataSide = event.target.getAttribute('data-side');
              event.target.textContent = `太棒了，你选择了'${dataSide}'!`;
              
              // 创建并显示toast提示
              if(random_next_query && random_next_query.query) {
                // 创建遮罩层
                const overlay = document.createElement('div');
                overlay.style.cssText = `
                  position: fixed;
                  top: 0;
                  left: 0;
                  width: 100%;
                  height: 100%;
                  background: rgba(128,128,128,0.8);
                  z-index: 999;
                `;
                document.body.appendChild(overlay);

                // 创建toast
                const toast = document.createElement('div');
                toast.style.cssText = `
                  position: fixed;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  background: rgba(0,0,0,0.7);
                  color: white;
                  padding: 20px;
                  border-radius: 8px;
                  z-index: 1000;
                  text-align: center;
                `;
                
                // 添加倒计时和按钮
                let timeLeft = 5;
                const content = document.createElement('div');
                content.textContent = `${timeLeft}秒后将跳转到:"${random_next_query.query},ctr:${random_next_query.ctr},搜索次数:${random_next_query.search_cnt}"的搜索结果`;
                
                const buttonContainer = document.createElement('div');
                buttonContainer.style.cssText = `
                  display: flex;
                  gap: 10px;
                  justify-content: center;
                  margin-top: 10px;
                `;

                const cancelBtn = document.createElement('button');
                cancelBtn.textContent = '取消自动跳转';
                cancelBtn.style.cssText = `
                  padding: 5px 10px;
                  border: none;
                  border-radius: 4px;
                  background: #fff;
                  cursor: pointer;
                `;

                const redirectBtn = document.createElement('button');
                redirectBtn.textContent = '立即跳转';
                redirectBtn.style.cssText = `
                  padding: 5px 10px;
                  border: none;
                  border-radius: 4px;
                  background: #fff;
                  cursor: pointer;
                `;
                
                buttonContainer.appendChild(cancelBtn);
                buttonContainer.appendChild(redirectBtn);
                toast.appendChild(content);
                toast.appendChild(buttonContainer);
                document.body.appendChild(toast);

                // 设置定时器和跳转
                const countdownTimer = setInterval(() => {
                  if(timeLeft>0){timeLeft--;}
                  content.textContent = `${timeLeft}秒后将跳转到："${random_next_query.query},ctr:${random_next_query.ctr},搜索次数:${random_next_query.search_cnt}"的搜索结果`;
                }, 1000);

                const jump_url=`/?query=${random_next_query.query}&page_size={{page_size}}&city={{city}}`;

                const redirectTimer = setTimeout(() => {
                  window.location.href = jump_url;
                }, 3000);

                // 取消按钮点击事件
                cancelBtn.onclick = () => {
                  clearInterval(countdownTimer);
                  clearTimeout(redirectTimer);
                  document.body.removeChild(toast);
                  document.body.removeChild(overlay);
                };

                // 立即跳转按钮点击事件
                redirectBtn.onclick = () => {
                  window.location.href = jump_url;
                };
              }
            });
          });
        });
        
        function showModal(imgSrc) {
            var modal = document.getElementById("imageModal");
            var modalImg = document.getElementById("modalImg");
            modal.style.display = "block";
            modalImg.src = imgSrc;
        }

        function closeModal() {
            document.getElementById("imageModal").style.display = "none";
        }

        // Close modal when clicking outside the image
        window.onclick = function(event) {
            var modal = document.getElementById("imageModal");
            if (event.target == modal) {
                modal.style.display = "none";
            }
        }

        // 监听点击事件来显示/隐藏hidding-properties
        document.querySelectorAll('.result-item').forEach(item => {
          item.addEventListener('click', function(event) {
            const hiddingProps = this.querySelector('.hidding-properties');
            const rect = item.getBoundingClientRect();
            let isCurrentlyDisplay = hiddingProps.style.display === 'block';

            // 先关闭所有其他的hidding-properties
            document.querySelectorAll('.hidding-properties').forEach(prop => {
              prop.style.display = 'none';
            });

            // 如果已经显示则隐藏
            if (isCurrentlyDisplay) {
              hiddingProps.style.display = 'none';
              return;
            } else {
              // 如果点击的是图片则不响应
              if (event.target.classList.contains('item-img')) {
                return;
              } else {
                // 显示属性框
                hiddingProps.style.display = 'block';
              }
            }
          });
        });
    </script>
</body>
</html>