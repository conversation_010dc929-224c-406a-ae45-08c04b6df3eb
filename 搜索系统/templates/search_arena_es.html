<html>
<head>
    <title>鲜沐商城搜索系统-{{ query }}</title>
    <link rel="icon" type="image/x-icon" href="//azure.summerfarm.net/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <style>
        /* 全局字体和背景，贴近 Apple 风格 */
        body {
            /* 全局字体和背景，贴近 Apple 风格，减小字体 */
            font-family: -apple-system, BlinkMacSystemFont, "San Francisco", Arial, sans-serif;
            background: linear-gradient(180deg, #f6f7f9 0%, #ececec 100%);
            color: #222;
            margin: 0;
            padding: 0;
            font-size: 15px;
        }

        /* 顶部标题样式 */
        h1 {
            /* 标题字体缩小，间距减小 */
            font-size: 1.5rem;
            font-weight: 700;
            letter-spacing: 0.02em;
            margin: 22px 0 12px 0;
            text-align: center;
            color: #111;
            text-shadow: 0 1px 4px #fff8, 0 1px 0 #fff;
        }
        h2 {
            font-size: 1.05rem;
            font-weight: 600;
            margin: 18px 0 10px 0;
            color: #222;
        }

        /* 搜索框区域，居中留白，卡片化 */
        .search-box {
            /* 搜索区域整体间距缩小 */
            margin: 0 auto 16px auto;
            max-width: 1200px;
            padding: 10px 0 0 0;
            display: flex;
            justify-content: center;
        }
        .search-box form {
            /* 搜索表单间距缩小 */
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            align-items: center;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 1px 6px 0 #e0e0e0a0;
            padding: 8px 16px;
        }
        /* 输入框和下拉框极简风格 */
        .search-box .search-box-item,
        .query-input-item,
        select {
            /* 输入框缩小，圆角减小 */
            font-size: 0.98rem;
            padding: 5px 10px;
            border: 1px solid #d1d1d6;
            border-radius: 8px;
            background: #f9f9fa;
            outline: none;
            transition: border-color 0.2s, box-shadow 0.2s;
            box-shadow: none;
        }
        .search-box .search-box-item:focus,
        .query-input-item:focus,
        select:focus {
            border-color: #0071e3;
            box-shadow: 0 0 0 2px #0071e320;
        }
        .query-input-item {
            width: 180px;
        }
        /* 搜索按钮 Apple 蓝色风格 */
        .search-box button[type="submit"] {
            /* 搜索按钮缩小 */
            background: linear-gradient(90deg, #0071e3 60%, #2997ff 100%);
            color: #fff;
            border: none;
            border-radius: 8px;
            padding: 6px 18px;
            font-size: 0.98rem;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s, box-shadow 0.2s;
            box-shadow: 0 1px 4px 0 #0071e320;
        }
        .search-box button[type="submit"]:hover {
            background: #005bb5;
        }
        .query-container {
            width: 100%;
            display: flow;
        }
        /* 随机搜索链接样式 */
        .search-box a {
            color: #0071e3;
            text-decoration: none;
            font-weight: 500;
            margin-left: 10px;
            transition: color 0.2s;
        }
        .search-box a:hover {
            color: #005bb5;
            text-decoration: underline;
        }

        /* 结果容器，三列自适应，商品间距大，居中显示 */
        .result-container,.query-box {
            /* 结果区间距缩小，商品卡片更紧凑 */
            width: 100%;
            max-width: 1700px;
            margin: 0 auto;
            display: flex;
            flex-wrap: wrap;
            gap: 18px 18px; /* 行列间距缩小 */
            justify-content: flex-start;
            align-items: stretch;
        }
        .result-container .side-results {
            width: 50%;
            padding-bottom: 80px;
            position: relative;
        }

        /* 商品卡片容器，极简圆角阴影，留白加大 */
        .item-container {
            /* 外层卡片，三列布局，间距缩小 */
            flex: 1 1 0;
            max-width: 33.33%;
            min-width: 240px;
            box-sizing: border-box;
            padding: 10px 6px 8px 6px;
            margin-bottom: 14px;
            margin-right: 0;
            margin-left: 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            border-radius: 12px;
            background-color: #fff;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            transition: box-shadow 0.2s;
            border: none;
            position: relative; /* 让内部的版本徽章能够绝对定位到卡片内，避免影响布局 */
        }
        .item-container:not(:last-child) {
            margin-right: 0;
        }
        .item-container:hover {
            box-shadow: 0 6px 18px rgba(0,0,0,0.10);
        }
        /* 商品列表区域，横向排列商品，间距缩小 */
        .item-container .item-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px 8px; /* 商品间距缩小 */
            width: 100%;
            margin-bottom: 6px;
        }

        /* 商品项卡片，极简风格，留白加大 */
        .result-item {
            /* 商品卡片更紧凑，字体缩小，圆角减小 */
            width: 120px;
            min-width: 100px;
            max-width: 140px;
            margin: 0;
            padding: 6px 4px 6px 4px;
            border: none;
            box-sizing: border-box;
            display: flex;
            align-items: flex-start;
            flex-direction: column;
            position: relative;
            background-color: #f7f8fa;
            border-radius: 7px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.04);
            transition: box-shadow 0.2s, transform 0.2s;
            cursor: pointer;
            min-height: 120px;
        }
        .result-item:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.10);
            transform: translateY(-2px) scale(1.01);
        }
        .result-item img {
            margin: 0 auto 6px auto;
            display: block;
            border-radius: 6px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.06);
            transition: box-shadow 0.2s;
            cursor: pointer;
            background: #f5f6f7;
            width: 60px;
            height: 60px;
            object-fit: cover;
        }
        .result-item p {
            margin: 1px 0;
            padding: 0;
            font-size: 0.88rem;
            color: #222;
            word-break: break-all;
            line-height: 1.3;
        }

        /* 售罄标签样式 */
        .out-of-stock-label {
            display: none;
        }
        .out-of-stock .out-of-stock-label {
            display: inline-block;
            padding: 0.5em 1.5em;
            text-align: center;
            background-color: #b0b0b0;
            opacity: 0.85;
            position: absolute;
            top: 12px;
            right: 12px;
            color: #fff;
            border-radius: 2em;
            font-size: 1rem;
            font-weight: 600;
            letter-spacing: 0.05em;
        }

        /* 版本徽章：与全局 Apple 极简风统一的小圆角胶囊标签 */
        .version-label {
            padding: 0.5em 1em;
            font-size: 1.15em;
            font-weight: bolder;
            color: #6e6e73;
            background: #f2f2f7;
            border: 1px solid #e5e7eb;
            border-radius: 999px;
            letter-spacing: 0.02em;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
            pointer-events: none;
            width: max-content;
            margin-bottom: 0.2em;
        }

        /* 投票按钮 Apple 风格 */
        .side-result-prefer-btn, .vote-btn {
            /* 投票按钮缩小 */
            width: 100%;
            display: block;
            font-size: 0.98rem;
            cursor: pointer;
            color: #fff;
            border: none;
            border-radius: 8px;
            background: linear-gradient(90deg, #0071e3 60%, #2997ff 100%);
            margin: 8px 0 0 0;
            padding: 8px 0;
            font-weight: 600;
            box-shadow: 0 1px 4px #0071e320;
            transition: background 0.2s, box-shadow 0.2s;
        }
        .side-result-prefer-btn:hover, .vote-btn:hover {
            background: #005bb5;
            box-shadow: 0 2px 8px #0071e340;
        }
        .side-result-prefer-btn:disabled, .vote-btn:disabled {
            background-color: #e0e0e0;
            color: #aaa;
            box-shadow: none;
            cursor: not-allowed;
        }
        .side-result-actions {
            position: absolute;
            bottom: 0;
        }
        .trailing-actions {
            width: 100%;
        }
        .trailing-actions .side-result-prefer-btn, .comment-input {
            width: 99%;
            margin-top: 6px;
        }

        /* 评论输入框 */
        .comment-input {
            /* 评论输入框缩小 */
            padding: 6px 10px;
            border-radius: 8px;
            font-size: 0.98rem;
            border: 1px solid #d1d1d6;
            background: #f7f8fa;
            margin-top: 6px;
            transition: border-color 0.2s, box-shadow 0.2s;
        }
        .comment-input:focus {
            border-color: #0071e3;
            box-shadow: 0 0 0 2px #0071e320;
        }

        /* 优化冷门搜索词标签样式 */
        .query-container a {
            display: inline-block;
            margin: 4px 8px 4px 0; /* 增加外边距 */
            padding: 4px 12px; /* 增加内边距 */
            border: none;
            border-radius: 8px;
            font-size: 0.92rem;
            background-color: #f1f2f6;
            text-decoration: none;
            color: #888; /* 冷门词用灰色区分 */
            font-weight: 500;
            transition: background 0.2s, color 0.2s;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .query-container a:hover {
            background-color: #e5e9f2;
            color: #666; /* 悬停时颜色加深 */
        }

        /* 冷门搜索词标题样式 */
        h2 {
            font-size: 1.05rem;
            font-weight: 600;
            margin: 18px 0 12px 0; /* 增加标题与标签的间距 */
            color: #666; /* 标题颜色与标签一致 */
        }

        /* 属性标签 */
        .properties-container {
            display: flex;
            flex-wrap: wrap;
        }
        .properties-container .property-item {
            /* 属性标签缩小 */
            display: inline;
            margin-right: 8px;
            width: fit-content;
            border-bottom: 1px solid #d1d1d6;
            font-size: 0.90rem;
            line-height: 1.5em;
            color: #555;
        }
        .result-item p.sort-score{
            color: #0071e3;
            font-size: 0.90rem;
        }

        /* Modal 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0; top: 0;
            width: 100%; height: 100%;
            background-color: rgba(0,0,0,0.85);
            transition: background 0.2s;
        }
        .modal-content {
            /* 弹窗内容缩小 */
            margin: auto;
            display: block;
            max-width: 80%;
            max-height: 80%;
            position: absolute;
            top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 10px;
            background: #fff;
            box-shadow: 0 4px 16px rgba(0,0,0,0.12);
            padding: 10px;
        }
        .close {
            position: absolute;
            right: 35px;
            top: 15px;
            color: #888;
            font-size: 40px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.2s;
        }
        .close:hover {
            color: #0071e3;
        }

        /* 响应式优化，适配移动端 */
        @media (max-width: 1200px) {
            .result-container {
                gap: 10px 0;
            }
            .item-container {
                max-width: 48%;
                min-width: 120px;
                margin-bottom: 10px;
            }
        }
        @media (max-width: 800px) {
            .result-container {
                flex-direction: column;
                gap: 0;
            }
            .item-container {
                max-width: 98%;
                min-width: 0;
                margin: 0 0 8px 0;
                padding: 6px 2px 6px 2px;
            }
            .result-item {
                width: 98%;
                margin: 2px 1%;
            }
        }
        @media (max-width: 600px) {
            .search-box form {
                flex-direction: column;
                gap: 4px;
                padding: 4px 2px;
            }
            .item-container {
                padding: 2px 1px 2px 1px;
            }
            .result-item {
                padding: 2px 2px 2px 2px;
            }
        }
    }

        /* 🎨 统一美学设计：Apple极简风格搜索区域 */
        .search-metrics-inline {
            max-width: 1200px;
            margin: 0 auto 24px auto;
            padding: 20px;
        }

        .inline-form {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 16px;
            flex-wrap: wrap;
            background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
            border-radius: 16px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
            border: 1px solid #e5e7eb;
            padding: 1vh 6vw;
            transition: all 0.3s ease;
        }

        .inline-form:hover {
            box-shadow: 0 4px 18px rgba(0, 0, 0, 0.12);
            transform: translateY(-1px);
        }

        .search-controls {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;
        }

        /* 搜索输入框 Apple风格 */
        .inline-form .query-input-item {
            font-size: 16px;
            padding: 12px 18px;
            border: 1px solid #d1d1d6;
            border-radius: 12px;
            background: #f9f9fa;
            outline: none;
            transition: all 0.2s ease;
            min-width: 280px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .inline-form .query-input-item:focus {
            border-color: #007aff;
            box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
            background: #ffffff;
        }

        /* 选择器 Apple风格 */
        .inline-form select {
            font-size: 14px;
            padding: 10px 14px;
            border: 1px solid #d1d1d6;
            border-radius: 10px;
            background: #f9f9fa;
            outline: none;
            transition: all 0.2s ease;
            cursor: pointer;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .inline-form select:focus {
            border-color: #007aff;
            box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.2);
        }

        /* 按钮 Apple风格 */
        .inline-form button[type="submit"] {
            background: linear-gradient(135deg, #007aff 0%, #0051d5 100%);
            color: #ffffff;
            border: none;
            border-radius: 10px;
            padding: 12px 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            box-shadow: 0 1px 4px rgba(0, 122, 255, 0.3);
        }

        .inline-form button[type="submit"]:hover {
            background: linear-gradient(135deg, #0071e3 0%, #004bb5 100%);
            box-shadow: 0 2px 8px rgba(0, 122, 255, 0.4);
            transform: translateY(-1px);
        }

        /* 随机链接 Apple风格 */
        .inline-form .random-link {
            color: #007aff;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            padding: 10px 16px;
            border-radius: 10px;
            background: #f2f2f7;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .inline-form .random-link:hover {
            background: #e5e9f2;
            color: #0051d5;
            transform: translateY(-1px);
        }

        /* 指标区域 Apple风格 */
        .metrics-compact-row {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-left: auto;
            flex-wrap: wrap;
        }

        .mini-metric {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-width: 48px;
            padding: 8px 10px;
            border-radius: 10px;
            background: #ffffff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
            border: 1px solid #e5e7eb;
            transition: all 0.2s ease;
        }

        .mini-metric:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border-color: #d1d1d6;
        }

        .mini-value {
            font-size: 12px;
            font-weight: 700;
            color: #1d1d1f;
            line-height: 1.1;
            margin-bottom: 2px;
        }

        .mini-label {
            font-size: 8px;
            font-weight: 500;
            color: #6e6e73;
            line-height: 1;
            text-transform: uppercase;
            letter-spacing: 0.02em;
        }

        .mini-badge {
            font-size: 9px;
            font-weight: 600;
            color: #007aff;
            padding: 0 2px;
            min-height: 12px;
            text-transform: uppercase;
            letter-spacing: 0.04em;
        }

        .frequency-tag.high .mini-badge {
            color: #ff3b30;
            font-weight: 700;
        }

        .frequency-tag.low .mini-badge {
            color: #34a853;
            font-weight: 700;
        }

        /* 统一间距优化 */
        .inline-form label {
            font-size: 14px;
            color: #3a3a3c;
            font-weight: 500;
            margin: 0;
        }

        /* 原搜索框样式覆盖 */
        .search-box {
            display: none;
        }

        /* 🎯 响应式美学调优 */
        @media (max-width: 1200px) {
            .inline-form {
                padding: 16px 20px;
                gap: 12px;
            }
            
            .inline-form .query-input-item {
                min-width: 240px;
            }
        }

        @media (max-width: 992px) {
            .inline-form {
                flex-direction: column;
                align-items: stretch;
                padding: 20px;
                gap: 16px;
            }

            .search-controls {
                justify-content: center;
                width: 100%;
            }

            .inline-form .query-input-item {
                min-width: 0;
                flex: 1;
                max-width: calc(100vw - 180px);
            }

            .metrics-compact-row {
                justify-content: center;
                margin-left: 0;
                margin-top: 12px;
                gap: 8px;
            }

            .mini-metric {
                min-width: 44px;
                padding: 6px 8px;
            }
        }

        @media (max-width: 768px) {
            .search-metrics-inline {
                padding: 16px 12px;
            }

            .inline-form {
                padding: 16px;
                gap: 12px;
            }

            .mini-metric {
                min-width: 42px;
                padding: 5px 7px;
            }
        }

        @media (max-width: 480px) {
            .search-metrics-inline {
                padding: 12px 8px;
            }

            .inline-form {
                padding: 14px 12px;
                border-radius: 12px;
            }

            .mini-metric {
                min-width: 40px;
                padding: 4px 6px;
            }

            .mini-value {
                font-size: 11px;
            }

            .mini-label {
                font-size: 8px;
            }
        }
    </style>
</head>
<body>
    <h1>鲜沐商城-搜索投票系统</h1>
    
    <div class="search-metrics-inline">
        <form method="GET" class="inline-form">
            <div class="search-controls">
                <input class='search-box-item query-input-item' type="text" name="query" placeholder="输入搜索词..." value="{{ query }}">
                <label for="page_size">页码：</label>
                <select id='page_size' class='search-box-item' name="page_size">
                    {% for size in [20, 40, 60, 80, 100] %}
                    <option value="{{ size }}" {% if size == page_size %}selected{% endif %}>{{ size }}</option>
                    {% endfor %}
                </select>
                {% if city_list %}
                <select id='citySelector' class='search-box-item' name="city">
                    {% for city_name in city_list %}
                    <option value="{{ city_name }}" {% if city_name == city %}selected{% endif %}>{{ city_name }}</option>
                    {% endfor %}
                </select>
                {% endif %}
                <button class='search-box-item' type="submit">搜索</button>
                <a href="?query={{random_next_query.query}}&page_size={{page_size}}&city={{city}}" class="random-link">随机</a>
            </div>
            
            <div class="metrics-compact-row">
                <div class="mini-metric">
                    <span class="mini-value">{{ query_metrics.sku_ctr }}%</span>
                    <span class="mini-label">CTR</span>
                </div>
                <div class="mini-metric">
                    <span class="mini-value">{{ query_metrics.user_ctr }}%</span>
                    <span class="mini-label">UCTR</span>
                </div>
                <div class="mini-metric">
                    <span class="mini-value">{{ query_metrics.searched_users }}</span>
                    <span class="mini-label">用户</span>
                </div>
                <div class="mini-metric">
                    <span class="mini-value">{{ query_metrics.search_cnt }}</span>
                    <span class="mini-label">搜索</span>
                </div>
                <div class="mini-metric frequency-tag {% if query_metrics.search_frequency_label == '高频搜索词' %}high{% elif query_metrics.search_frequency_label == '低频搜索词' %}low{% endif %}">
                    <span class="mini-badge">{{ query_metrics.search_frequency_label[:2] }}</span>
                </div>
            </div>
        </form>
    </div>
    <div class='result-container'>
        {% for result in result_container %}
        <div class="item-container">
            <input type="hidden" name="prefered_version" value="{{ result.version }}">
            <div class="version-label">{{ result.version }}</div>
            <div class="item-list">
            {% for item in result.results %}
                <div class="result-item{% if not item.sufficient_stock %} out-of-stock{% endif %}" onclick="showItemDetails('{{ item.category_name }}', '{{ item.weight }}', '{{ item.img_url }}', '{{ item.spu_name }}, {{ item.pd_id }}', 'SKU:{{ item.sku_id }}, {{ item.area_price }}')">
                    <img src="{{ item.img_url }}" width="120" height="120">
                    <p>{{ item.spu_name }}</p>
                    <p>{{ item.area_price }}</p>
                    <p>{{ item.sku_id }}</p>
                    <p>月销: {{ item.monthly_sales }}件, ¥{{ item.monthly_gmv }}</p>
                    <p class="sort-score">score: {{ item.sort_score }}</p>
                    <div class="out-of-stock-label">补货中</div>
                </div>
            {% endfor %}
        </div>
            <div class="trailing-actions">
                <button class="side-result-prefer-btn vote-btn" onclick="vote(event, '{{ result.version }}')">选它(可多选)</button>
            </div>
        </div>
        {% endfor %}
        <div class='trailing-actions'>
            <button class='side-result-prefer-btn vote-btn' onclick="vote(event, 'they-are-tied', '都不错')" data-version='both-are-poor' 
                data-query="{{ query }}" data-city="{{ city }}" data-side='都不好'>都不错</button>
            <button class='side-result-prefer-btn vote-btn' onclick="vote(event, 'they-are-tied', '都很差额')" data-version='they-are-tied' 
                data-query="{{ query }}" data-city="{{ city }}" data-side='都不错'>都很差</button>
            <input type='text' class='comment-input' name="comment" id="comment" placeholder='如果搜索结果都不满意，可以吐槽哦'>
        </div>
    </div>
    <div class="query-box">
        <div class='query-container'>
            <h2><a href="?query={{random_next_query.query}}&page_size={{page_size}}&city={{city}}">带我到下一个词</a></h2>
        </div>

        {% if top_high_click_index_20_query %}
            <h2>过去{{last_n_days}}天商城最冷门的{{ top_high_click_index_20_query|length }}个低点击率搜索词</h2>
            <div class='query-container'>
            {% for item in top_high_click_index_20_query %}
            <a href="?query={{ item.query }}&city={{ city }}&page_size={{page_size}}">{{ item.query }}, ctr:{{ item.ctr }}, 搜索次数:{{ item.search_cnt }}</a>
            {% endfor %}
            </div>
        {% endif %}

        {% if middle_20_query %}
            <h2>过去{{last_n_days}}天商城“次热门”的{{ middle_20_query|length }}个搜索词</h2>
            <div class='query-container'>
            {% for item in middle_20_query %}
            <a href="?query={{ item.query }}&city={{ city }}&page_size={{page_size}}">{{ item.query }}, ctr:{{ item.ctr }}, 搜索次数:{{ item.search_cnt }}</a>
            {% endfor %}
            </div>
        {% endif %}

        {% if top_20_query %}
            <h2>过去{{last_n_days}}天商城最热门的{{ top_20_query|length }}个搜索词</h2>
            <div class='query-container'>
            {% for item in top_20_query %}
            <a href="?query={{ item.query }}&city={{ city }}&page_size={{page_size}}">{{ item.query }}, ctr:{{ item.ctr }}, 搜索次数:{{ item.search_cnt }}</a>
            {% endfor %}
            </div>
        {% endif %}
    </div>

    <!-- Modal for Large Image -->
    <div id="imageModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImg">
    </div>

    <!-- Modal for Item Details -->
    <div id="itemDetailsModal" class="modal">
        <span class="close" onclick="closeItemDetailsModal()">&times;</span>
        <div class="modal-content" id="itemDetailsContent" style="background-color: white; padding: 20px; text-align: center;">
            <img id="itemDetailsImg" style="max-width: 80%; max-height: 400px;">
            <p id="itemCategoryName"></p>
            <p id="itemWeight"></p>
            <p id="itemName"></p>
            <p id="itemDesc"></p>
        </div>
    </div>

    <script>
        // 定义随机下一个搜索词对象，避免重复声明
        // 已移除多余的 random_next_query 变量声明，避免重复和语法错误

        function showItemDetails(categoryName, weight, imgSrc, name, desc) {
            var modal = document.getElementById("itemDetailsModal");
            var modalImg = document.getElementById("itemDetailsImg");
            var categoryNameElem = document.getElementById("itemCategoryName");
            var weightElem = document.getElementById("itemWeight");
            var nameElem = document.getElementById("itemName");
            var descElem = document.getElementById("itemDesc");
            
            modal.style.display = "block";
            modalImg.src = imgSrc;
            categoryNameElem.textContent = `类目: ${categoryName}`;
            weightElem.textContent = `规格: ${weight}`;
            nameElem.textContent = `名称: ${name}`;
            descElem.textContent = `${desc}`;
        }

        function closeItemDetailsModal() {
            document.getElementById("itemDetailsModal").style.display = "none";
        }

        // Close modal when clicking outside the image
        window.onclick = function(event) {
            var imageModal = document.getElementById("imageModal");
            var itemDetailsModal = document.getElementById("itemDetailsModal");
            
            if (event.target == imageModal) {
                imageModal.style.display = "none";
            }
            
            if (event.target == itemDetailsModal) {
                itemDetailsModal.style.display = "none";
            }
        }

        function showTooltip(message, isError = false, duration = 3000) {
            // 创建tooltip元素
            const tooltip = document.createElement('div');
            tooltip.style.position = 'fixed';
            tooltip.style.top = '20px';
            tooltip.style.left = '50%';
            tooltip.style.transform = 'translateX(-50%)';
            tooltip.style.backgroundColor = isError ? 'rgba(255,0,0,0.7)' : 'rgba(0,0,0,0.7)';
            tooltip.style.color = 'white';
            tooltip.style.padding = '10px';
            tooltip.style.borderRadius = '5px';
            tooltip.style.zIndex = '1000';
            tooltip.textContent = message;
            document.body.appendChild(tooltip);

            // 3秒后自动移除tooltip
            setTimeout(() => {
                document.body.removeChild(tooltip);
            }, duration);

            return tooltip;
        }

        function vote(event, version, stop) {
            if(stop) {
                // 把所有.vote-btn的button都disabled
                document.querySelectorAll('.vote-btn').forEach(btn => btn.disabled = true);
                showTooltip(`恭喜你选择了:${stop}`)
            }else{
                event.target.disabled = true;
                showTooltip(`感谢反馈，您可以继续投票`);
            }
            // 发送投票结果到服务器
            fetch('which-is-better', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    version: version,
                    comment: document.getElementById('comment')?.value || ''
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log(`保存成功:${data}`);
            })
            .catch(error => {
                console.error('Error:', error);
                // 显示错误tooltip
                const errorTooltip = showTooltip('提交反馈时出现错误', true);
                
                // 2秒后移除错误提示
                setTimeout(() => {
                    document.body.removeChild(errorTooltip);
                }, 2000);
            });
        }
    </script>
</body>
</html>
