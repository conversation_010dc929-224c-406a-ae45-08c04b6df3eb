<!DOCTYPE html>
<html>
<head>
    <title>搜索版本的偏好比较</title>
    <link rel="icon" type="image/x-icon" href="//azure.summerfarm.net/favicon.ico">
    <script src="https://code.highcharts.com/highcharts.js"></script>
</head>
<body>
    <div id="container" style="width:100%; height:400px;"></div>
    <script>
            // stats=[{'prefered_version': '原始搜索', 'total_count': 5, 'unique_users': 1, 'unique_queries': 5}, {'prefered_version': '类目重排序', 'total_count': 7, 'unique_users': 1, 'unique_queries': 7}, {'prefered_version': '都不错', 'total_count': 4, 'unique_users': 1, 'unique_queries': 3}]
            const stats = {{ stats|tojson }};
            const categories = stats.map(item => item.prefered_version);
            const totalCount = stats.map(item => item.total_count);
            const uniqueUsers = stats.map(item => item.unique_users);
            const uniqueQueries = stats.map(item => item.unique_queries);

            Highcharts.chart('container', {
                chart: {
                    type: 'column'
                },
                title: {
                    text: '搜索版本的偏好比较'
                },
                xAxis: {
                    categories: categories,
                    title: {
                        text: '用户偏好'
                    }
                },
                yAxis: {
                    min: 0,
                    title: {
                        text: '数量'
                    }
                },
                series: [{
                    name: '人次',
                    data: totalCount
                }, {
                    name: '人数',
                    data: uniqueUsers
                }, {
                    name: 'query个数',
                    data: uniqueQueries
                }]
            });
    </script>
</body>
</html>