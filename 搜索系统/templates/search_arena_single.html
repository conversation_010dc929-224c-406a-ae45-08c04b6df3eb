<html>
<head>
    <title>鲜沐商城搜索结果-{{query}}-{{city}}</title>
    <link rel="icon" type="image/x-icon" href="//azure.summerfarm.net/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <style>
        :root {
            --primary-color: #4285F4;
            --secondary-color: #34A853;
            --text-color: #202124;
            --light-gray: #f8f9fa;
            --border-color: #dadce0;
        }
        
        body {
            font-family: 'Roboto', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: var(--text-color);
            line-height: 1.6;
            max-width: 1980px;
            margin: 0 auto;
        }
        
        .search-box {
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 6px rgba(32, 33, 36, 0.1);
        }
        
        .search-box .search-box-item {
            padding: 10px 15px;
            border: 1px solid var(--border-color);
            border-radius: 24px;
            font-size: 16px;
            margin-right: 10px;
        }
        
        .query-input-item {
            width: 100%;
            max-width: 584px;
            padding: 12px 20px !important;
        }
        
        .result-container {
            /* display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px; */
            width: 100%;
        }
        
        .item-container {
            display: flex;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            padding: 16px;
            transition: box-shadow 0.2s;
            flex-wrap: wrap;
        }
        
        .item-container:hover {
            box-shadow: 0 1px 6px rgba(32, 33, 36, 0.28);
        }
        
        .result-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 5px;
            margin-bottom: 5px;
            margin-right: 5px;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: transform 0.2s;
            width: 180px;
        }
        
        .result-item:hover {
            transform: translateY(-2px);
        }
        
        .result-item img {
            width: 100%;
            height: auto;
            max-width: 200px;
            margin: 0 auto 10px;
            object-fit: contain;
        }
        
        .result-item p {
            margin: 0px 0;
            font-size: small;
            text-align: center;
        }
        .query-container {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 20px 0;
        }
        
        .query-container a {
            display: inline-block;
            padding: 8px 12px;
            border-radius: 16px;
            font-size: 14px;
            background-color: var(--light-gray);
            text-decoration: none;
            color: var(--primary-color);
            transition: all 0.2s;
        }
        
        .query-container a:hover {
            background-color: var(--primary-color);
            color: white;
        }
        
        .side-result-prefer-btn {
            width: 100%;
            padding: 12px;
            font-size: 18px;
            cursor: pointer;
            color: white;
            border: none;
            border-radius: 24px;
            background-color: var(--primary-color);
            transition: background-color 0.2s;
            margin: 10px 0;
        }
        
        .side-result-prefer-btn:hover {
            background-color: #3367d6;
        }
        
        .side-result-prefer-btn:disabled {
            background-color: #dadce0;
            color: #70757a;
        }
        
        .comment-input {
            width: 100%;
            padding: 12px 16px;
            border-radius: 24px;
            font-size: 16px;
            border: 1px solid var(--border-color);
            margin: 10px 0;
        }
        
        /* Responsive layout */
        @media (max-width: 768px) {
            .result-container {
                grid-template-columns: 1fr;
            }
            
            .search-box {
                padding: 15px;
            }
            
            .search-box .search-box-item {
                margin-bottom: 10px;
                margin-right: 0;
                width: 100%;
            }
        }

        /* Modal styles */
        .modal {display: none;position: fixed;z-index: 1;left: 0;top: 0;width: 100%;height: 100%;background-color: rgba(0,0,0,0.9);}
        .modal-content {margin: auto;display: block;max-width: 80%;max-height: 80%;position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);}
        .close {position: absolute;right: 35px;top: 15px;color: #f1f1f1;font-size: 40px;font-weight: bold;cursor: pointer;}
        .out-of-stock-label{display: none;}
        .out-of-stock .out-of-stock-label{display: inline-block;padding: 2vh 2vw;text-align: center;background-color: grey;opacity: 0.7;position: absolute;color: #fff;border-radius: 3rem;}
    </style>
</head>
<body>
    <h1>鲜沐商城"新搜索"投票系统</h1>

    <div class="search-metrics-inline">
        <form method="GET" class="inline-form">
            <div class="search-controls">
                <input class='search-box-item query-input-item' type="text" name="query" placeholder="输入搜索词..." value="{{ query }}">
                <label for="page_size">页码：</label>
                <select id='page_size' class='search-box-item' name="page_size">
                    {% for size in [6, 12, 18, 24, 30, 50, 100] %}
                    <option value="{{ size }}" {% if size == page_size %}selected{% endif %}>{{ size }}</option>
                    {% endfor %}
                </select>
                {% if city_list %}
                <select id='citySelector' class='search-box-item' name="city">
                    {% for city_name in city_list %}
                    <option value="{{ city_name }}" {% if city_name == city %}selected{% endif %}>{{ city_name }}</option>
                    {% endfor %}
                </select>
                {% endif %}
                <button class='search-box-item' type="submit">搜索</button>
                <a href="?query={{random_next_query.query}}&page_size={{page_size}}&city={{city}}" class="random-link">随机</a>
            </div>
            
            <div class="metrics-compact-row">
                <div class="mini-metric">
                    <span class="mini-value">{{ query_metrics.sku_ctr }}%</span>
                    <span class="mini-label">CTR</span>
                </div>
                <div class="mini-metric">
                    <span class="mini-value">{{ query_metrics.user_ctr }}%</span>
                    <span class="mini-label">UCTR</span>
                </div>
                <div class="mini-metric">
                    <span class="mini-value">{{ query_metrics.searched_users }}</span>
                    <span class="mini-label">用户</span>
                </div>
                <div class="mini-metric">
                    <span class="mini-value">{{ query_metrics.search_cnt }}</span>
                    <span class="mini-label">搜索</span>
                </div>
                <div class="mini-metric frequency-tag {% if query_metrics.search_frequency_label == '高频搜索词' %}high{% elif query_metrics.search_frequency_label == '低频搜索词' %}low{% endif %}">
                    <span class="mini-badge">{{ query_metrics.search_frequency_label[:2] }}</span>
                </div>
            </div>
        </form>
    </div>
    
    <script>
    // 🌟 统一响应式处理
    document.addEventListener('DOMContentLoaded', function() {
        const handleResponsive = () => {
            const forms = document.querySelectorAll('.inline-form');
            forms.forEach(form => {
                if (window.innerWidth < 768) {
                    form.style.flexDirection = 'column';
                } else {
                    form.style.flexDirection = 'row';
                }
            });
        };
        
        window.addEventListener('resize', handleResponsive);
        handleResponsive();
    });
    
    // 转换single页样式为与主样式一致
    const styleSheet = document.createElement('style');
    styleSheet.textContent = `
        .search-metrics-inline {
            max-width: 100%;
            margin: 0 auto 24px auto;
            padding: 20px 15px;
        }

        .inline-form {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 16px;
            flex-wrap: wrap;
            background: white;
            border-radius: 14px;
            box-shadow: 0 1px 8px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            padding: 18px 16px;
            transition: all 0.3s ease;
        }

        .inline-form:hover {
            box-shadow: 0 2px 16px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .inline-form .query-input-item {
            font-size: 15px;
            padding: 11px 16px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            background: var(--light-gray);
            min-width: 220px;
        }

        .inline-form select {
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            background: var(--light-gray);
            font-size: 14px;
        }

        .inline-form button[type="submit"] {
            padding: 11px 20px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            color: white;}
    
    `;
    document.head.appendChild(styleSheet);
    </script>
    
    <div class='result-container'>
        {% for result in result_container %}
        <div class="item-container">
            <input type="hidden" name="prefered_version" value="{{ result.version }}">
            {% for item in result.results %}
                <div class="result-item{% if not item.sufficient_stock %} out-of-stock{% endif %}" onclick="showItemDetails('{{ item.category_name }}', '{{ item.weight }}', '{{ item.img_url }}', '{{ item.spu_name }}, {{ item.pd_id }}', 'SKU:{{ item.sku_id }}, {{ item.area_price }}')">
                    <img src="{{ item.img_url }}" width="120" height="120">
                    <p>#{{ loop.index }}, {{ item.spu_name }}</p>
                    <p>{{ item.area_price }}</p>
                    <p>{{ item.sku_id }}</p>
                    <p>月销: {{ item.monthly_sales }}件, ¥{{ item.monthly_gmv }}</p>
                    <div class="out-of-stock-label">补货中</div>
                </div>
            {% endfor %}
            <!-- <div class="trailing-actions">
                <button class="side-result-prefer-btn vote-btn" onclick="vote(event, '{{ result.version }}')">选它(可多选)</button>
            </div> -->
        </div>
        {% endfor %}
        <!-- <div class='trailing-actions'>
            <button class='side-result-prefer-btn vote-btn' onclick="vote(event, 'they-are-tied', '都不错')" data-version='both-are-poor' 
                data-query="{{ query }}" data-city="{{ city }}" data-side='都不好'>都不错</button>
            <button class='side-result-prefer-btn vote-btn' onclick="vote(event, 'they-are-tied', '都很差额')" data-version='they-are-tied' 
                data-query="{{ query }}" data-city="{{ city }}" data-side='都不错'>都很差</button>
            <input type='text' class='comment-input' name="comment" placeholder='如果搜索结果都不满意，可以吐槽哦'>
        </div> -->
    </div>
    <h2><a href="?query={{random_next_query.query}}&page_size={{page_size}}&city={{city}}">带我到下一个词</a></h2>

    {% if top_high_click_index_20_query %}
        <h2>过去{{last_n_days}}天商城最冷门的{{ top_high_click_index_20_query|length }}个低点击率搜索词</h2>
        <div class='query-container'>
        {% for item in top_high_click_index_20_query %}
        <a href="?query={{ item.query }}&city={{ city }}&page_size={{page_size}}">{{ item.query }}, ctr:{{ item.ctr }}, 搜索次数:{{ item.search_cnt }}</a>
        {% endfor %}
        </div>
    {% endif %}

    {% if middle_20_query %}
        <h2>过去{{last_n_days}}天商城“次热门”的{{ middle_20_query|length }}个搜索词</h2>
        <div class='query-container'>
        {% for item in middle_20_query %}
        <a href="?query={{ item.query }}&city={{ city }}&page_size={{page_size}}">{{ item.query }}, ctr:{{ item.ctr }}, 搜索次数:{{ item.search_cnt }}</a>
        {% endfor %}
        </div>
    {% endif %}

    {% if top_20_query %}
        <h2>过去{{last_n_days}}天商城最热门的{{ top_20_query|length }}个搜索词</h2>
        <div class='query-container'>
        {% for item in top_20_query %}
        <a href="?query={{ item.query }}&city={{ city }}&page_size={{page_size}}">{{ item.query }}, ctr:{{ item.ctr }}, 搜索次数:{{ item.search_cnt }}</a>
        {% endfor %}
        </div>
    {% endif %}

    <!-- Modal for Large Image -->
    <div id="imageModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-content" id="modalImg">
    </div>

    <!-- Modal for Item Details -->
    <div id="itemDetailsModal" class="modal">
        <span class="close" onclick="closeItemDetailsModal()">&times;</span>
        <div class="modal-content" id="itemDetailsContent" style="background-color: white; padding: 20px; text-align: center;">
            <img id="itemDetailsImg" style="max-width: 80%; max-height: 400px;">
            <p id="itemCategoryName"></p>
            <p id="itemWeight"></p>
            <p id="itemName"></p>
            <p id="itemDesc"></p>
        </div>
    </div>

    <script>
        let random_next_query = {{ random_next_query|tojson }};

        function showItemDetails(categoryName, weight, imgSrc, name, desc) {
            var modal = document.getElementById("itemDetailsModal");
            var modalImg = document.getElementById("itemDetailsImg");
            var categoryNameElem = document.getElementById("itemCategoryName");
            var weightElem = document.getElementById("itemWeight");
            var nameElem = document.getElementById("itemName");
            var descElem = document.getElementById("itemDesc");
            
            modal.style.display = "block";
            modalImg.src = imgSrc;
            categoryNameElem.textContent = `类目: ${categoryName}`;
            weightElem.textContent = `规格: ${weight}`;
            nameElem.textContent = `名称: ${name}`;
            descElem.textContent = `${desc}`;
        }

        function closeItemDetailsModal() {
            document.getElementById("itemDetailsModal").style.display = "none";
        }

        // Close modal when clicking outside the image
        window.onclick = function(event) {
            var imageModal = document.getElementById("imageModal");
            var itemDetailsModal = document.getElementById("itemDetailsModal");
            
            if (event.target == imageModal) {
                imageModal.style.display = "none";
            }
            
            if (event.target == itemDetailsModal) {
                itemDetailsModal.style.display = "none";
            }
        }

        function showTooltip(message, isError = false, duration = 3000) {
            // 创建tooltip元素
            const tooltip = document.createElement('div');
            tooltip.style.position = 'fixed';
            tooltip.style.top = '20px';
            tooltip.style.left = '50%';
            tooltip.style.transform = 'translateX(-50%)';
            tooltip.style.backgroundColor = isError ? 'rgba(255,0,0,0.7)' : 'rgba(0,0,0,0.7)';
            tooltip.style.color = 'white';
            tooltip.style.padding = '10px';
            tooltip.style.borderRadius = '5px';
            tooltip.style.zIndex = '1000';
            tooltip.textContent = message;
            document.body.appendChild(tooltip);

            // 3秒后自动移除tooltip
            setTimeout(() => {
                document.body.removeChild(tooltip);
            }, duration);

            return tooltip;
        }

        function vote(event, version, stop) {
            if(stop) {
                // 把所有.vote-btn的button都disabled
                document.querySelectorAll('.vote-btn').forEach(btn => btn.disabled = true);
                showTooltip(`恭喜你选择了:${stop}`)
            }else{
                event.target.disabled = true;
                showTooltip(`感谢反馈，您可以继续投票`);
            }
            // 发送投票结果到服务器
            fetch('which-is-better', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    version: version,
                    comment: document.getElementById('comment')?.value || ''
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log(`保存成功:${data}`);
            })
            .catch(error => {
                console.error('Error:', error);
                // 显示错误tooltip
                const errorTooltip = showTooltip('提交反馈时出现错误', true);
                
                // 2秒后移除错误提示
                setTimeout(() => {
                    document.body.removeChild(errorTooltip);
                }, 2000);
            });
        }
    </script>
</body>
</html>