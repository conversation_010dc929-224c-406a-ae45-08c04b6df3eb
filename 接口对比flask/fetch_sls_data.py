import sys

sys.path.append("../")

from sls_client import get_sls_raw_data_by_query
from datetime import datetime, timedelta
import pandas as pd

def get_sls_data(uid: str, start_time: datetime, end_time: datetime, logstore: str = "fe-test"):
    """
    Fetches SLS data based on the provided parameters.

    Args:
        uid: User ID.
        start_time: Start time for the query.
        end_time: End time for the query.
        logstore: Logstore to query.

    Returns:
        A pandas DataFrame containing the fetched data.
    """
    query = f"""uid:{uid} and url:"https://qah5.summerfarm.net" and ap not null"""

    all_df = pd.DataFrame()
    while True:
        offset = len(all_df)
        print(f"offset:{offset}")
        _df = get_sls_raw_data_by_query(
            from_time=start_time,
            to_time=end_time,
            query=query,
            logstore=logstore,
            offset=offset,
        )
        if _df.empty:
            break
        all_df = pd.concat([all_df, _df])

    return all_df
