from flask import Flask, render_template, request, send_from_directory, send_file
from fetch_sls_data import get_sls_data
import requests
import hashlib
import json
import argparse
from datetime import datetime, timedelta
import pandas as pd
from urllib.parse import urlencode
import random
import string
import os


def from_json(json_string):
    return json.loads(json_string)


def my_to_json(obj):
    print(f"running....my_to_json:{obj}")
    return json.dumps(obj, ensure_ascii=False, indent=2)


app = Flask(__name__)
app.jinja_env.filters["fromjson"] = from_json
app.jinja_env.filters["mytojson"] = my_to_json

# Configuration
API_TIMEOUT = 12000
KEYS_NO_NEED_TO_COMPARE = set(["addTime", "createTime", "timestamp"])
RESULT_HTML_DIR = os.path.join(os.path.dirname(__file__), "result_html")

# Ensure the result directory exists
if not os.path.exists(RESULT_HTML_DIR):
    os.makedirs(RESULT_HTML_DIR)


# Helper Functions (Adapted from the provided script)
def get_md5_encoded_string(phone_number, date, word):
    combined_string = f"{phone_number}{date}{word}"
    return hashlib.md5(combined_string.encode("utf-8")).hexdigest()


def generate_random_string(length=10):
    letters = string.ascii_lowercase + string.digits
    random_string = "".join(random.choice(letters) for i in range(length))
    return f"test_{random_string}"


def get_token_for_env(phone_number, env):
    date = datetime.now().strftime("%Y%m%d")
    word = "login"
    md5_encoded_string = get_md5_encoded_string(phone_number, date, word)
    url = f"{env}/openid?phone={phone_number}&sign={md5_encoded_string}"
    token = requests.get(url=url, timeout=API_TIMEOUT)
    try:
        token = json.loads(token.text)
        uid = token["data"]["mId"]
        return {"env": f"{env}", "token": token["data"]["token"], "uid": uid}
    except Exception as e:
        print(f"获取Token失败:{url}, {token}, {e}")
        raise e


def request_env_data(
    env: str,
    json_data=None,
    params_data=None,
    method: str = "GET",
    api: str = "",
    headers: dict = None,
):
    if json_data:
        method = "POST"
    host = f"{env}{api}"
    if params_data:
        encoded_params = urlencode(params_data)
        host = f"{host}?{encoded_params}"

    _headers = headers.copy()
    _rqid = generate_random_string()
    _headers["xm-rqid"] = _rqid
    response = requests.request(
        method=method, url=host, json=json_data, headers=_headers
    )
    result = response.json()
    if "serverTime" in result:
        del result["serverTime"]
    return result, _rqid


def compare_two_json(json1, json2):
    diff = {}

    if type(json1) != type(json2):
        diff["type_diff"] = f"json1 type: {type(json1)}, json2 type: {type(json2)}"
        return diff  # Return immediately if types are different

    if isinstance(json1, dict):
        keys1 = set(json1.keys())
        keys2 = set(json2.keys())
        if keys1 != keys2:
            diff1 = list(keys1 - keys2)
            diff2 = list(keys2 - keys1)
            diff["key_diff"] = {
                "key_diff": f"两个json的key set不同: json1独有的key: {diff1}, json2独有的key: {diff2}"
            }

        for key in keys1.intersection(keys2):  # Iterate only through common keys
            if key in KEYS_NO_NEED_TO_COMPARE:
                print(f"这个key不需要比较:{key}, {json1[key]}, {json2[key]}")
                continue
            result = compare_two_json(json1[key], json2[key])
            if result:
                diff[key] = result

    elif isinstance(json1, list):
        if len(json1) != len(json2):
            diff["len_diff"] = f"json1 len: {len(json1)}, json2 len: {len(json2)}"

        # Sort lists before comparison
        sorted_json1 = sorted(json1, key=str)
        sorted_json2 = sorted(json2, key=str)

        for i in range(
            min(len(sorted_json1), len(sorted_json2))
        ):  # Iterate up to the length of the shorter list
            result = compare_two_json(sorted_json1[i], sorted_json2[i])
            if result:
                diff[i] = result
    elif json1 != json2:
        diff["value_diff"] = f"json1: {json1}, json2: {json2}"

    return diff if diff else None


import io
import pandas as pd


# Flask Routes
@app.route("/", methods=["GET"])
def index():
    ip_and_port = f"{request.remote_addr}:80"
    return render_template(
        "index.html", ip_and_port=ip_and_port, envs=["qah5", "devh5", "dev2h5"]
    )


@app.route("/compare", methods=["POST"])
def compare_apis():
    try:
        return compare_apis_internal()
    except Exception as e:
        return f"Error: {e}", 500


def compare_apis_internal():
    phone_number = request.form["phone_number"]
    ip_and_port = request.form.get("ip_and_port")
    if not ip_and_port:
        ip_and_port = f"{request.remote_addr}:80"

    end_time = datetime.now()
    start_time = end_time - timedelta(hours=24)

    start_time_str = request.form.get("start_time")
    end_time_str = request.form.get("end_time")

    if start_time_str:
        start_time = datetime.strptime(start_time_str, "%Y-%m-%dT%H:%M")
    if end_time_str:
        end_time = datetime.strptime(end_time_str, "%Y-%m-%dT%H:%M")

    # Get the URI from the form data
    uri = request.form.get("uri", "")

    # Get token and UID
    selected_env = request.form.get("environment", "https://devh5.summerfarm.net")
    # Get token and UID
    token_dict = get_token_for_env(phone_number, env=selected_env)
    uid = token_dict["uid"]

    # Prepare headers
    ab_str = json.dumps(
        [
            {
                "experimentId": "product_search_rerank",
                "experimentPlace": "place-of:product_search_rerank",
                "variantId": "V3",
            },
            {
                "experimentId": "new-home",
                "experimentPlace": "place-of:new-home",
                "variantId": "V3",
            },
        ],
        ensure_ascii=False,
    )
    headers = {
        "accept": "application/json, text/plain, */*",
        "token": f"{token_dict['token']}",
        "xm-ab-exp": ab_str,
        "xm-biz": "xm-mall",
        "xm-phone": phone_number,
        "xm-platform": "web",
        "xm-uid": f"{uid}",
    }

    all_df = get_sls_data(uid=uid, start_time=start_time, end_time=end_time)
    if all_df.empty:
        return (
            f"没有找到SLS日志, 手机号:{phone_number}, uid:{uid}, start_time: {start_time}, end_time: {end_time}.",
            400,
        )

    if uri:
        print(f"User specified URI: {uri}")
        uris = [u.strip() for u in uri.split(',')]  # Split by comma and trim whitespace
        filtered_df = pd.DataFrame()

        for single_uri in uris:
            if '*' in single_uri:
                uri_pattern = single_uri.replace('*', '.*')
                temp_df = all_df[all_df['ap'].str.match(uri_pattern, na=False)]
            else:
                temp_df = all_df[all_df['ap'] == single_uri]
            filtered_df = pd.concat([filtered_df, temp_df])

        all_df = filtered_df.drop_duplicates()  # Remove any duplicate rows

        if all_df.empty:
            return f"没有找到匹配的SLS日志, URI: {uri}", 400

    # API Comparison Logic (Adapted from the provided script)
    api_compare_result = []
    requested_data = set()

    iterations = int(request.form.get("iterations", 50))
    for index, row in all_df[["ai", "uid", "ap"]].iterrows():
        if len(api_compare_result) >= iterations:
            print(f"达到了用户设定的最大运行次数:{iterations}, index:{index}")
            break
        row_dict = json.loads(row.to_dict()["ai"])
        method = row_dict.get("method").upper()
        request_uri = row["ap"]
        params_data = row_dict.get("params", None)
        try:
            json_data = (
                json.loads(row_dict.get("data")) if row_dict.get("data") else None
            )
        except Exception as e:
            print(f"ERROR:{e}, row:{row_dict}")
            if "=" in row_dict.get("data", ""):
                # 'defaultFlag=1&status=1'
                request_uri = f"{request_uri}?{row_dict.get('data')}"
                print(f"这个请求似乎把参数放到URL中去了:{request_uri}")
            else:
                continue

        new_record = {
            "method": method,
            "request_uri": request_uri,
            "json_data": json_data,
            "params_data": params_data,
        }

        _new_record_str = json.dumps(new_record, ensure_ascii=False)
        if _new_record_str in requested_data:
            print(f"duplicate request:{_new_record_str}")
            continue
        requested_data.add(_new_record_str)

        env_results = []
        ENV_LIST = [f"http://{ip_and_port}", selected_env]
        for env in ENV_LIST:
            try:
                ret, _rqid = request_env_data(
                    env=env,
                    method=method,
                    json_data=json_data,
                    params_data=params_data,
                    api=request_uri,
                    headers=headers,
                )
                new_record[f"result_of_{env}"] = ret
                new_record[f"rqid_of_{env}"] = _rqid
                env_results.append(ret)
            except Exception as e:
                new_record[f"result_of_{env}"] = str(e)
                new_record[f"rqid_of_{env}"] = str(e)
                env_results.append(str(e))

        json1 = env_results[0].get("data")
        json2 = env_results[1].get("data")

        diff = compare_two_json(json1, json2)
        new_record["diff"] = diff
        new_record["is_same_result"] = diff is None
        print(f'{request_uri}, 是否一致:{new_record["is_same_result"]}')

        api_compare_result.append(new_record)

    api_compare_result_df = pd.DataFrame(api_compare_result)
    columns = api_compare_result_df.columns
    rqid_fields = [col for col in columns if col.startswith("rqid_of_")]
    result_fields = [col for col in columns if col.startswith("result_of_")]

    # ['method', 'request_uri', 'json_data', 'params_data', 'result_of_http://localh5', 'rqid_of_http://localh5',
    # 'result_of_https://qah5', 'rqid_of_https://qah5', 'diff', 'is_same_result']
    api_compare_result_df = api_compare_result_df.sort_values(by='request_uri')
    api_compare_result_df["meta_data"] = api_compare_result_df.apply(
        lambda x: "\n".join(
            [
                f"method: {x['method']}",
                f"uri: {x['request_uri']}",
                f"json_data: {x['json_data']}",
                f"params_data: {x['params_data']}",
                f"{rqid_fields[0]}: {x[rqid_fields[0]]}",
                f"{rqid_fields[1]}: {x[rqid_fields[1]]}",
                f"\nis_same_result: {x['is_same_result']}",
            ]
        ),
        axis=1,
    )

    api_compare_result_df = api_compare_result_df[
        ["meta_data", "diff", "is_same_result", result_fields[0], result_fields[1]]
    ]

    colomn_names = ["meta_data", "diff", result_fields[0], result_fields[1]]

    is_all_consistant = api_compare_result_df["is_same_result"].all()
    results_are_diff = api_compare_result_df[
        ~api_compare_result_df["is_same_result"]
    ][colomn_names].to_dict(orient="records")
    results_are_consistant = api_compare_result_df[
        api_compare_result_df["is_same_result"]
    ][colomn_names].to_dict(orient="records")


    total_size = len(api_compare_result_df)
    inconsistant_result_count_msg = f"总请求数量:{total_size}"
    inconsistant_result_count_msg = (
        f"{inconsistant_result_count_msg}, 一致请求数量:{len(results_are_consistant)}"
    )
    inconsistant_result_count_msg = (
        f"{inconsistant_result_count_msg}, 不一致请求数量:{len(results_are_diff)}"
    )

    if not api_compare_result:
        return "No data found for comparison.", 404

    return render_template(
        "result.html",
        results_are_diff=results_are_diff,
        is_all_consistant=is_all_consistant,
        inconsistant_result_count_msg=inconsistant_result_count_msg,
        finished_at=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        results_are_consistant=results_are_consistant,
        colomn_names=colomn_names,
        ip_and_port=f"{ip_and_port}".replace(".", "_").replace(":", "_"),
        api_compare_result_json=json.dumps(api_compare_result),
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="运行Flask应用.", add_help=False)
    parser.add_argument("--port", type=int, default=8800, help="应用运行的端口.")
    parser.add_argument("--debug", action="store_true", help="允许调试模式.")
    args, unknown = parser.parse_known_args()
    app.run(debug=args.debug, host="0.0.0.0", port=args.port)
