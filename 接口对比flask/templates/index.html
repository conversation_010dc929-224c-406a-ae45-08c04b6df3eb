<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
    <title>API Comparison</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }

        .container {
            width: 80%;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            color: #333;
        }

        form {
            background: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .form-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .form-row label {
            display: inline-block;
            width: 45%; /* Adjust as needed */
            margin-right: 10px;
            text-align: right;
        }

        .form-row input[type="text"],
        .form-row .form-control,
        .form-row input[type="number"],
        .form-row input[type="datetime-local"] {
            width: 50%; /* Adjust as needed */
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .form-row select {
            width: 50%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        input[type="submit"] {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        input[type="submit"]:hover {
            background-color: #45a049;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>API Comparison</h1>
        <p>基本原理：从SLS上，把你指定的手机号的门店日志下载下来，然后再回放一遍，逐个对比QA和你本地的应用的结果，得出你的代码是否安全可靠的结论</p>
        <p>同一个接口，参数相同的话，会只跑一次。日志获取区间默认取最近24小时的数据</p>
        <p>整个过程可能比较久，请你耐心等待几分钟，不要关闭流览器</p>
        <p><b style="color: red;">请注意，需要先将你的QA门店设置role_id = -1, 即：update merchant set role_id = -1 where
                phone='1860001111';</b></p>
        <form action="/compare" method="post">
            <div class="form-row">
                <label for="phone_number">门店手机号:</label>
                <input type="text" id="phone_number" name="phone_number" required>
            </div>
            <div class="form-row">
                <label for="ip_and_port">你本地的IP和mall应用的端口({{ ip_and_port }}):</label>
                <input type="text" id="ip_and_port" name="ip_and_port" value="{{ ip_and_port }}">
            </div>

            <div class="form-row">
                <label for="start_time">日志回溯开始时间(默认24小时前):</label>
                <input type="datetime-local" id="start_time" name="start_time">
            </div>

            <div class="form-row">
                <label for="end_time">日志回溯结束时间(默认当前时间):</label>
                <input type="datetime-local" id="end_time" name="end_time">
            </div>

            <div class="form-row">
                <label for="iterations">最大运行次数 (默认 50):</label>
                <input type="number" id="iterations" name="iterations" value="50">
            </div>

            <div class="form-row">
                <label for="uri">对比 URI (支持通配符, 例如 /product-info/*):</label>
                <input type="text" id="uri" name="uri">
            </div>

            <div class="form-row">
                <label id="label_env" for="environment">选择环境:(用以下命令行启动本地应用 java -jar -server -Dxm.mq.listen=false
                    -Dspring.profiles.active=qa target/summerfarm-mall-1.0.0.jar |& tee ./mall.log)</label>
                <select id="environment" name="environment" class="form-control">
                    <option value="https://qah5.summerfarm.net" selected>qa</option>
                    <option value="https://dev2h5.summerfarm.net">dev2</option>
                    <option value="https://devh5.summerfarm.net">dev</option>
                </select>
            </div>

            <div class="form-row">
                <input type="submit" value="Compare">
            </div>
        </form>
        <div id="loading" style="display: none;">
            <p>系统处理中，请不要关闭浏览器... <span id="timer">0</span> seconds</p>
        </div>
        <script>
            const form = document.querySelector('form');
            const loadingDiv = document.getElementById('loading');
            const timerSpan = document.getElementById('timer');
            let seconds = 0;
            let timerInterval;

            $("#environment").on('change', (event) => {
                event.preventDefault();
                let new_env = event.target.value.replace("h5.summerfarm.net", "").replace('https://', '');
                // java -jar -server -Dxm.mq.listen=false -Dspring.profiles.active=qa target/summerfarm-mall-1.0.0.jar |& tee ./mall.log
                let new_label = `请确保你的本地应用已经启动，并且使用以下命令行启动本地应用:\njava -jar -Dxm.mq.listen=false -Dspring.profiles.active=${new_env} target/summerfarm-mall-1.0.0.jar`
                $("#label_env").css("color", "red");
                $("#label_env").text(new_label);
            });

            form.addEventListener('submit', (event) => {
                event.preventDefault();
                loadingDiv.style.display = 'block';
                seconds = 0;
                timerSpan.textContent = seconds;
                timerInterval = setInterval(() => {
                    seconds++;
                    timerSpan.textContent = seconds;
                }, 1000);

                // Submit the form after a delay to ensure the loading message is visible
                setTimeout(() => {
                    form.submit();
                }, 500); // Adjust delay as needed
            });
        </script>
    </div>
</body>

</html>
