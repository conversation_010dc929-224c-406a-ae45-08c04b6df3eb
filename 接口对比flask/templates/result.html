<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Comparison Results</title>
    <link rel="icon" href="https://azure.summerfarm.net/test/atewr8rgjnunpjl8.jpg" type="image/x-icon">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }

        .container {
            width: 100vw;
            margin: 0 auto;
            padding: 10px;
            max-width: 100vw;
        }

        h1 {
            color: #333;
        }

        pre {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ddd;
            white-space: pre-wrap;
            /* Wrap lines */
            word-wrap: break-word;
            /* Break long words */
            font-family: monospace;
            /* Use a monospaced font for code */
        }

        pre.collapsed {
            max-height: 30vh;
            overflow: auto;
        }

        pre.expanded {
            max-height: none;
        }

        .comparison-container .btn-small {
            padding: 0.1rem 0.5rem;
        }
        .non-code-container{
            max-width: 20vw;
        }
        .code-container{
            width: 32vw;
        }
    </style>
    <script>
        function downloadCSV() {
            const table = document.querySelector('.comparison-container table');
            if (!table) {
                alert("No table data to download.");
                return;
            }

            const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent);
            const rows = Array.from(table.querySelectorAll('tbody tr')).map(row => {
                return Array.from(row.querySelectorAll('td')).map(td => {
                    const pre = td.querySelector('pre');
                    return pre ? pre.textContent.trim() : td.textContent.trim();
                });
            });

            const csvRows = [headers, ...rows].map(row => row.map(cell => {
                if (typeof cell === 'string') {
                    cell = cell.replace(/"/g, '""');
                    return `"${cell}"`;
                }
                return cell;
            }).join(','));

            const csvString = csvRows.join('\n');
            const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = '{{ ip_and_port }}_comparison_results.csv';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
    </script>
</head>

<body>
    <div class="comparison-container">
        <h1>API Comparison Results</h1>
        <p>Finished at: {{ finished_at }}. <a href="/" title="返回">Back⬅️</a><button class="btn btn-primary btn-sm"
                onclick='downloadCSV()'>下载CSV</button></p>

        {% if inconsistant_result_count_msg %}
        <div class="alert alert-success" role="alert">
            {{ inconsistant_result_count_msg }}
        </div>
        {% endif %}

        {% if results_are_diff %}
        <h2 style="color: red;">结果不一致的请求:</h2>
        <table class="table table-striped table-bordered">
            <thead>
                <tr>
                    {% for col in colomn_names %}
                    <th>{{ col }}</th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody>
                {% for result in results_are_diff %}
                <tr>
                    {% for col in colomn_names %}
                    <td>
                        {% if col == 'diff' or col.startswith('result_of_') %}
                        <div class="code-container">
                            <button class="btn btn-primary btn-sm btn-copy">Copy</button>
                            <button class="btn btn-secondary btn-sm btn-toggle">Toggle</button>
                            <pre class="collapsed">{{ result[col]|mytojson }}</pre>
                        </div>
                        {% else %}
                        <div class="non-code-container">
                            <pre>{{ result[col] }}</pre>
                        </div>
                        {% endif %}
                    </td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endif %}

        {% if results_are_consistant %}
        <h2>结果一致的请求:</h2>
        <table class="table table-striped table-bordered">
            <thead>
                <tr>
                    {% for col in colomn_names %}
                    <th>{{ col }}</th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody>
                {% for result in results_are_consistant %}
                <tr>
                    {% for col in colomn_names %}
                    <td>
                        {% if col.startswith('result_of_') %}
                        <div class="code-container">
                            <button class="btn btn-primary btn-sm btn-copy">Copy</button>
                            <button class="btn btn-secondary btn-sm btn-toggle">Toggle</button>
                            <pre class="collapsed">{{ result[col]|mytojson }}</pre>
                        </div>
                        {% else %}
                        <div class="non-code-container">
                            <pre>{{ result[col] }}</pre>
                        </div>
                        {% endif %}
                    </td>
                    {% endfor %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
        {% endif %}
    </div>
    <script>
        function copyToClipboard(text) {
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(text).then(function () {
                    toast("複製到剪貼板成功！", 2000);
                }, function (err) {
                    console.error('Could not copy text: ', err);
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                fallbackCopyTextToClipboard(text);
            }
        }

        // Fallback function for copying text to clipboard
        function fallbackCopyTextToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;

            // Avoid scrolling to bottom
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                var successful = document.execCommand('copy');
                var msg = successful ? 'successful' : 'unsuccessful';
                toast("Copying text command was " + msg, 2000);
            } catch (err) {
                console.error('Fallback: Oops, unable to copy', err);
            }

            document.body.removeChild(textArea);
        }

        // Toast 函數定義
        function toast(message, duration) {
            var toastElement = document.createElement("div");
            toastElement.style.position = "fixed";
            toastElement.style.top = "50%";
            toastElement.style.left = "50%";
            toastElement.style.transform = "translate(-50%, -50%)";
            toastElement.style.backgroundColor = "#333";
            toastElement.style.color = "#fff";
            toastElement.style.padding = "10px";
            toastElement.style.borderRadius = "5px";
            toastElement.innerHTML = message;
            document.body.appendChild(toastElement);
            setTimeout(function () {
                document.body.removeChild(toastElement);
            }, duration);
        }

        function toggleDisplay(preElement) {
            if (preElement.hasClass("collapsed")) {
                preElement.removeClass('collapsed');
                preElement.addClass('expanded');
            } else {
                preElement.removeClass('expanded');
                preElement.addClass('collapsed');
            }
        }
    </script>
    <!-- Bootstrap JS (Optional, for certain components) -->
    <script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
    <script>
        $(document).ready(function () {
            // Event delegation for 'Copy' and 'Toggle' buttons
            $('table').on('click', '.btn-copy', function () {
                var textToCopy = $(this).closest('div').find('pre').text();
                copyToClipboard(textToCopy);
            });

            $('table').on('click', '.btn-toggle', function () {
                $(this).closest('tr').find('pre').each(function() {
                    toggleDisplay($(this));
                });
            });
        });

        function copyToClipboard(text) {
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(text).then(function () {
                    toast("複製到剪貼板成功！", 2000);
                }, function (err) {
                    console.error('Could not copy text: ', err);
                    fallbackCopyTextToClipboard(text);
                });
            } else {
                fallbackCopyTextToClipboard(text);
            }
        }

        // Fallback function for copying text to clipboard
        function fallbackCopyTextToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;

            // Avoid scrolling to bottom
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";

            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                var successful = document.execCommand('copy');
                var msg = successful ? 'successful' : 'unsuccessful';
                toast("Copying text command was " + msg, 2000);
            } catch (err) {
                console.error('Fallback: Oops, unable to copy', err);
            }

            document.body.removeChild(textArea);
        }
    </script>
</body>

</html>